# Job AI - Automated Job Application System

An AI-powered job search and application automation system that helps you find, analyze, and apply to relevant job opportunities with customized resumes and cover letters.

## 🚀 Features

### Core Functionality
- **AI-Powered Job Search**: Automated job discovery using Google Gemini AI
- **Intelligent Job Analysis**: Compatibility scoring and application strategy generation
- **Custom Resume Generation**: ATS-friendly resumes tailored for each job
- **Automated Applications**: Browser automation for form filling and submission
- **Professional PDF Generation**: High-quality resume and cover letter PDFs
- **Application Tracking**: Comprehensive database for managing applications

### AI Capabilities
- **Prompt Engineering**: Advanced AI prompting for consistent, high-quality outputs
- **ATS Optimization**: Resume optimization for Applicant Tracking Systems
- **Job Matching**: Intelligent compatibility analysis based on skills and experience
- **Content Customization**: Job-specific resume and cover letter generation
- **Workflow Automation**: AI-enhanced productivity and application management

## 🛠 Technology Stack

### Backend
- **Python 3.8+** - Core application language
- **Flask** - Web API framework
- **Google Gemini AI** - AI content generation and analysis
- **Playwright** - Browser automation for job applications
- **SQLite** - Local database for job and application tracking
- **ReportLab** - Professional PDF generation

### Frontend
- **React** - User interface framework
- **TypeScript** - Type-safe JavaScript
- **Tailwind CSS** - Utility-first CSS framework

### AI & Automation
- **Google Generative AI** - Advanced language model integration
- **Prompt Engineering** - Optimized AI prompting strategies
- **Web Scraping** - Automated job discovery
- **Form Automation** - Intelligent application form filling

## 📋 Prerequisites

- Python 3.8 or higher
- Node.js 16+ (for frontend)
- Google Gemini API key
- Modern web browser (Chrome/Chromium recommended)

## 🚀 Quick Start

### 1. Setup
```bash
# Clone the repository
git clone <repository-url>
cd job-ai

# Run the setup script
python setup.py
```

### 2. Configuration
Edit `config/credentials.env` and add your API key:
```env
GEMINI_API_KEY=your_actual_api_key_here
```

Get your Gemini API key from: https://makersuite.google.com/app/apikey

### 3. Test the System
```bash
python run_tests.py
```

### 4. Start the Application
```bash
# Start the backend API
python api_server.py

# In another terminal, start the frontend (if available)
cd frontend
npm install
npm start
```

## 📖 User Profile Configuration

The system is pre-configured for **Tishbian Meshach S**, an AI-Enhanced UI/UX Designer. Key profile highlights:

### Professional Background
- **Current Role**: Leading Designer at TripXplo
- **Experience**: 4+ years in design with AI integration
- **Education**: B.E Computer Science (2022-2026)
- **Location**: Tuticorin, Tamil Nadu, India

### Core Skills
- **Design**: UI/UX Design, Figma, Adobe Creative Suite
- **Development**: React, JavaScript, HTML5, CSS3, Tailwind CSS
- **AI Expertise**: Prompt Engineering, ChatGPT, Claude, Gemini AI, Midjourney, DALL-E

### AI Specialization
- **Prompt Engineering**: Advanced prompt crafting for design concepts
- **Workflow Automation**: 60% productivity increase through AI integration
- **AI Tools Mastery**: ChatGPT/GPT-4, Claude, Midjourney, DALL-E, Stable Diffusion
- **Design Innovation**: AI-powered design systems and automation

### Unique Value Propositions
- Combines traditional design expertise with cutting-edge AI capabilities
- Proven ability to increase team productivity through AI workflow integration
- Expert in prompt engineering for consistent, high-quality design outputs
- Pioneer in AI-assisted user research and design validation

## 🔧 API Endpoints

### Job Management
- `GET /api/jobs` - Fetch all jobs
- `POST /api/search` - Start job search
- `GET /api/search/status` - Get search status
- `GET /api/stats` - Get job statistics

### Job Analysis
- `POST /api/jobs/<id>/analyze` - Analyze job compatibility
- `POST /api/jobs/analyze-batch` - Batch analyze multiple jobs

### Resume & Applications
- `POST /api/resume/customize` - Generate custom resume
- `POST /api/resume/generate-pdf` - Generate PDF resume
- `POST /api/jobs/<id>/apply` - Auto-apply to job

## 🎯 Usage Examples

### Analyze a Job
```python
from src.ai.job_analyzer import JobAnalyzer
from src.utils.config import Config

analyzer = JobAnalyzer()
analysis = analyzer.analyze_job_match(job_data, Config.USER_PROFILE)
print(f"Match Score: {analysis['match_score']}/100")
```

### Generate Custom Resume
```python
from src.ai.resume_generator import AIResumeGenerator

resume_gen = AIResumeGenerator()
custom_resume = resume_gen.customize_resume(
    Config.USER_PROFILE, 
    job_data, 
    analysis
)
```

### Auto-Apply to Job
```python
from src.automation.job_applier import AutoJobApplier
import asyncio

applier = AutoJobApplier()
result = asyncio.run(applier.auto_apply(job_data, resume, cover_letter))
```

## 🧪 Testing

Run comprehensive tests:
```bash
python run_tests.py
```

Individual test components:
```bash
python test_system.py
```

## 📁 Project Structure

```
job-ai/
├── api_server.py              # Main Flask API server
├── src/
│   ├── ai/
│   │   ├── job_analyzer.py    # Job compatibility analysis
│   │   └── resume_generator.py # AI resume customization
│   ├── automation/
│   │   └── job_applier.py     # Automated job applications
│   ├── utils/
│   │   ├── config.py          # Configuration and user profile
│   │   ├── database.py        # SQLite database management
│   │   ├── gemini_client.py   # Google Gemini AI client
│   │   └── pdf_generator.py   # Professional PDF generation
│   └── job_searcher.py        # Job search automation
├── frontend/                  # React frontend application
├── config/
│   ├── credentials.env        # API keys and secrets
│   └── settings.yaml          # Application settings
├── data/                      # SQLite database and logs
├── requirements.txt           # Python dependencies
├── setup.py                   # Setup script
├── run_tests.py              # Test runner
└── test_system.py            # Comprehensive test suite
```

## ⚙️ Configuration

### User Profile Customization
Edit `src/utils/config.py` to customize the user profile:
- Personal information
- Skills and experience
- AI expertise level
- Job preferences
- Salary expectations

### Application Settings
Edit `config/settings.yaml` to configure:
- Job search keywords
- Target locations
- Application limits
- Automation preferences

## 🔒 Security & Privacy

- API keys stored in local environment files
- No sensitive data transmitted to external services
- Local SQLite database for job tracking
- Browser automation runs locally
- Resume data processed locally

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues and questions:
1. Check the test output: `python run_tests.py`
2. Review the logs in the `logs/` directory
3. Ensure all dependencies are installed
4. Verify your Gemini API key is valid

## 🚀 Future Enhancements

- Integration with more job boards
- Advanced AI models for better matching
- Mobile application
- Team collaboration features
- Analytics and reporting dashboard
- Integration with LinkedIn and other platforms

---

**Built with ❤️ by Tishbian Meshach S - AI-Enhanced Designer**

*Combining traditional design expertise with cutting-edge AI capabilities for the future of work.*
