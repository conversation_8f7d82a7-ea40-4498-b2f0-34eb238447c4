from src.utils.gemini_client import GeminiClient
from src.utils.database import JobDatabase
from src.utils.logger import setup_logger
from src.utils.config import Config
from src.scrapers.real_job_scraper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, JobSearchManager as RealJobSearchManager
import time
from datetime import datetime

class GeminiJobSearcher:
    def __init__(self):
        self.gemini = GeminiClient()
        self.db = JobDatabase()
        self.logger = setup_logger('job_searcher')
    
    def search_all_categories(self):
        """Search for jobs across different categories"""
        all_jobs = []
        
        # Reduced queries to stay within rate limits
        search_queries = [
            "UI UX design intern remote jobs 2025",
            "graphic design internship remote 2025"
        ]
        
        for i, query in enumerate(search_queries):
            self.logger.info(f"Searching for: {query}")
            jobs = self.gemini.search_jobs(query)
            
            for job in jobs:
                # Filter and save relevant jobs
                if self._is_relevant_job(job):
                    all_jobs.append(job)
                    self.db.insert_job(job)
            
            self.logger.info(f"Found {len(jobs)} jobs for query: {query}")
            
            # Add delay between queries to avoid rate limits
            if i < len(search_queries) - 1:
                self.logger.info("Waiting 30 seconds before next search...")
                time.sleep(30)
        
        self.logger.info(f"Total relevant jobs found: {len(all_jobs)}")
        return all_jobs
    
    def _is_relevant_job(self, job):
        """Filter jobs based on criteria"""
        title = job.get('title', '').lower()
        description = job.get('description', '').lower()
        
        # Check for design-related keywords
        design_keywords = ['design', 'ui', 'ux', 'graphic', 'visual', 'product']
        has_design = any(keyword in title or keyword in description for keyword in design_keywords)
        
        # Check if it's an internship or entry level
        entry_keywords = ['intern', 'internship', 'junior', 'entry', 'graduate']
        is_entry_level = any(keyword in title or keyword in description for keyword in entry_keywords)
        
        # Exclude senior positions
        exclude_keywords = ['senior', 'lead', 'manager', 'director', 'principal']
        is_senior = any(keyword in title for keyword in exclude_keywords)
        
        return has_design and is_entry_level and not is_senior

class EnhancedJobSearcher:
    def __init__(self):
        self.gemini = GeminiClient()
        self.db = JobDatabase()
        
    def search_latest_jobs(self):
        """Search for the most recent job postings"""
        current_date = datetime.now().strftime("%Y-%m-%d")
        
        enhanced_queries = [
            f"UI UX design intern remote jobs posted today {current_date}",
            f"frontend developer internship remote {current_date}",
            f"product design intern remote latest {current_date}",
            f"graphic design internship remote new postings {current_date}"
        ]
        
        all_jobs = []
        for query in enhanced_queries:
            jobs = self.gemini.search_jobs(query)
            
            # Filter for truly recent jobs
            recent_jobs = self._filter_recent_jobs(jobs)
            all_jobs.extend(recent_jobs)
            
            time.sleep(30)  # Rate limiting
            
        return self._deduplicate_jobs(all_jobs)
    
    def _filter_recent_jobs(self, jobs):
        """Filter jobs posted in last 24-48 hours"""
        recent_jobs = []
        for job in jobs:
            if self._is_recent_posting(job.get('posted_date', '')):
                recent_jobs.append(job)
        return recent_jobs

class JobSearchManager:
    def __init__(self, use_real_jobs=True):
        self.gemini_searcher = GeminiJobSearcher()
        self.real_searcher = RealJobSearchManager()
        self.use_real_jobs = use_real_jobs  # Default to True to avoid fake jobs
        self.logger = setup_logger('job_search_manager')
        self.is_searching = False
        self.search_results = {
            'jobs_found': 0,
            'last_search': None
        }

    def start_search(self):
        """Start job search in background"""
        if self.is_searching:
            return {'success': False, 'message': 'Search already in progress'}

        self.is_searching = True
        self.logger.info("Starting job search...")

        try:
            if self.use_real_jobs:
                # Use real job scraper
                jobs = self.real_searcher.search_all_jobs()

                # Save jobs to database
                db = JobDatabase()
                for job in jobs:
                    try:
                        db.insert_job(job)
                    except Exception as e:
                        self.logger.warning(f"Failed to insert job {job.get('job_id', 'unknown')}: {e}")
                        continue

                self.logger.info(f"Real job search completed. Found {len(jobs)} jobs.")
            else:
                # Use Gemini AI generated jobs (fallback)
                jobs = self.gemini_searcher.search_all_categories()
                self.logger.info(f"AI job search completed. Found {len(jobs)} jobs.")

            self.search_results = {
                'jobs_found': len(jobs),
                'last_search': datetime.now().isoformat()
            }

            return {'success': True, 'message': f'Found {len(jobs)} jobs'}
        except Exception as e:
            self.logger.error(f"Search failed: {e}")
            return {'success': False, 'message': f'Search failed: {str(e)}'}
        finally:
            self.is_searching = False

    def get_search_status(self):
        """Get current search status"""
        return {
            'is_searching': self.is_searching,
            'jobs_found': self.search_results['jobs_found'],
            'last_search': self.search_results['last_search']
        }

    def run_search(self):
        """Legacy method for compatibility"""
        result = self.start_search()
        return [] if not result['success'] else self.search_results['jobs_found']

