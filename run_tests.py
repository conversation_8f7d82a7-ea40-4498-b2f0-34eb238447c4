#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to run the Job AI system tests.
This script will start the server and run comprehensive tests.
"""

import subprocess
import time
import sys
import os
import signal
from pathlib import Path

def check_requirements():
    """Check if all required dependencies are installed"""
    print("Checking requirements...")
    
    required_packages = [
        'flask',
        'google-generativeai',
        'playwright',
        'reportlab',
        'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")
    
    if missing_packages:
        print(f"\nMissing packages: {', '.join(missing_packages)}")
        print("Please install them using:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_config():
    """Check if configuration is properly set up"""
    print("\nChecking configuration...")
    
    # Check if credentials file exists
    creds_path = Path('config/credentials.env')
    if not creds_path.exists():
        print("❌ config/credentials.env not found")
        print("Please create the credentials file with your GEMINI_API_KEY")
        return False
    
    print("✅ Credentials file found")
    
    # Check if data directory exists
    data_path = Path('data')
    if not data_path.exists():
        data_path.mkdir(exist_ok=True)
        print("✅ Created data directory")
    else:
        print("✅ Data directory exists")
    
    return True

def start_server():
    """Start the Flask API server"""
    print("\nStarting API server...")
    
    try:
        # Start server in background
        server_process = subprocess.Popen(
            [sys.executable, 'api_server.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # Wait a bit for server to start
        time.sleep(3)
        
        # Check if server is still running
        if server_process.poll() is None:
            print("✅ API server started successfully")
            return server_process
        else:
            stdout, stderr = server_process.communicate()
            print(f"❌ Server failed to start")
            print(f"STDOUT: {stdout.decode()}")
            print(f"STDERR: {stderr.decode()}")
            return None
            
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        return None

def run_tests():
    """Run the comprehensive test suite"""
    print("\nRunning comprehensive tests...")
    
    try:
        result = subprocess.run(
            [sys.executable, 'test_system.py'],
            capture_output=True,
            text=True,
            timeout=300  # 5 minute timeout
        )
        
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("❌ Tests timed out after 5 minutes")
        return False
    except Exception as e:
        print(f"❌ Failed to run tests: {e}")
        return False

def stop_server(server_process):
    """Stop the API server"""
    if server_process and server_process.poll() is None:
        print("\nStopping API server...")
        server_process.terminate()
        
        # Wait for graceful shutdown
        try:
            server_process.wait(timeout=5)
            print("✅ Server stopped gracefully")
        except subprocess.TimeoutExpired:
            print("⚠️ Server didn't stop gracefully, forcing...")
            server_process.kill()
            server_process.wait()
            print("✅ Server stopped forcefully")

def main():
    """Main test runner"""
    print("="*60)
    print("JOB AI - AUTOMATED APPLICATION SYSTEM TESTS")
    print("="*60)
    
    # Step 1: Check requirements
    if not check_requirements():
        print("\n❌ Requirements check failed. Please install missing packages.")
        return 1
    
    # Step 2: Check configuration
    if not check_config():
        print("\n❌ Configuration check failed. Please set up configuration.")
        return 1
    
    server_process = None
    
    try:
        # Step 3: Start server
        server_process = start_server()
        if not server_process:
            print("\n❌ Failed to start server. Running tests without API endpoints.")
        
        # Step 4: Run tests
        tests_passed = run_tests()
        
        # Step 5: Report results
        print("\n" + "="*60)
        print("FINAL RESULTS")
        print("="*60)
        
        if tests_passed:
            print("✅ ALL TESTS PASSED!")
            print("\nYour Job AI system is ready to use!")
            print("\nNext steps:")
            print("1. Start the server: python api_server.py")
            print("2. Open the frontend application")
            print("3. Begin searching and applying for jobs!")
            return 0
        else:
            print("❌ SOME TESTS FAILED")
            print("\nPlease check the test output above for details.")
            print("Common issues:")
            print("- Missing GEMINI_API_KEY in config/credentials.env")
            print("- Network connectivity issues")
            print("- Missing dependencies")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️ Tests interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return 1
    finally:
        # Always stop the server
        if server_process:
            stop_server(server_process)

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
