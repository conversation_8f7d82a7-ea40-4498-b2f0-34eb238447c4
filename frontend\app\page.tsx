'use client'

import { useState } from 'react'
import ResumeGenerator from './components/ResumeGenerator'
import CoverLetterGenerator from './components/CoverLetterGenerator'
import ProfileManager from './components/ProfileManager'

export default function Home() {
  const [activeTab, setActiveTab] = useState<'resume' | 'cover-letter' | 'profile'>('resume')

  return (
    <main className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            📝 Resume & Cover Letter Generator
          </h1>
          <p className="text-gray-600">
            Create professional resumes and personalized cover letters with AI assistance
          </p>
        </div>

        {/* Navigation Tabs */}
        <div className="flex justify-center mb-8">
          <div className="bg-white rounded-lg p-1 shadow-sm border">
            <button
              onClick={() => setActiveTab('resume')}
              className={`px-6 py-2 rounded-md font-medium transition-colors ${
                activeTab === 'resume'
                  ? 'bg-blue-500 text-white'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              📄 Resume Generator
            </button>
            <button
              onClick={() => setActiveTab('cover-letter')}
              className={`px-6 py-2 rounded-md font-medium transition-colors ${
                activeTab === 'cover-letter'
                  ? 'bg-blue-500 text-white'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              ✉️ Cover Letter
            </button>
            <button
              onClick={() => setActiveTab('profile')}
              className={`px-6 py-2 rounded-md font-medium transition-colors ${
                activeTab === 'profile'
                  ? 'bg-blue-500 text-white'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              👤 Profile
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-4xl mx-auto">
          {activeTab === 'resume' && <ResumeGenerator />}
          {activeTab === 'cover-letter' && <CoverLetterGenerator />}
          {activeTab === 'profile' && <ProfileManager />}
        </div>
      </div>
    </main>
  )
}






