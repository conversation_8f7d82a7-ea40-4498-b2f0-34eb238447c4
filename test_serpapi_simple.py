#!/usr/bin/env python3
"""
Simple test to verify SerpAPI methods exist in IndianJobScraper
"""

import sys
import os
sys.path.append('src')

# Force reload of modules
import importlib
if 'src.scrapers.indian_job_scraper' in sys.modules:
    importlib.reload(sys.modules['src.scrapers.indian_job_scraper'])

from src.scrapers.indian_job_scraper import IndianJobScraper

def test_methods_exist():
    """Test that SerpAPI methods exist"""
    print("Testing if SerpAPI methods exist in IndianJobScraper...")
    
    scraper = IndianJobScraper()
    
    # Check if methods exist
    methods_to_check = [
        '_search_indeed_india_serp',
        '_search_naukri_serp', 
        '_search_internshala_serp',
        '_parse_serp_result_indeed',
        '_parse_serp_result_naukri',
        '_parse_serp_result_internshala'
    ]
    
    for method_name in methods_to_check:
        if hasattr(scraper, method_name):
            print(f"✅ {method_name} exists")
        else:
            print(f"❌ {method_name} NOT FOUND")
    
    # List all methods that contain 'serp'
    print("\nAll methods containing 'serp':")
    all_methods = [method for method in dir(scraper) if 'serp' in method.lower()]
    for method in all_methods:
        print(f"  - {method}")
    
    if not all_methods:
        print("  No methods containing 'serp' found")

def test_serpapi_method():
    """Test calling a SerpAPI method"""
    print("\n" + "="*50)
    print("TESTING SERPAPI METHOD CALL")
    print("="*50)
    
    try:
        scraper = IndianJobScraper()
        
        # Test if we can call the method
        if hasattr(scraper, '_search_indeed_india_serp'):
            print("Calling _search_indeed_india_serp(1)...")
            jobs = scraper._search_indeed_india_serp(1)
            print(f"✅ Method call successful! Found {len(jobs)} jobs")
            
            if jobs:
                job = jobs[0]
                print(f"Sample job: {job.get('title')} at {job.get('company')}")
                print(f"URL: {job.get('apply_url')}")
        else:
            print("❌ Method _search_indeed_india_serp not found")
            
    except Exception as e:
        print(f"❌ Error calling SerpAPI method: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_methods_exist()
    test_serpapi_method()
