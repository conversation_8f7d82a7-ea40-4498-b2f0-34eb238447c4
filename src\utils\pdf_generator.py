from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.colors import black, darkblue, gray
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.units import inch
from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_RIGHT
import io
from datetime import datetime

class ProfessionalPDFGenerator:
    def __init__(self, buffer):
        self.buffer = buffer
        self.width, self.height = letter
        self.margin = 0.75 * inch
        self.content_width = self.width - 2 * self.margin
        
    def create_resume_pdf(self, resume_data):
        """Create a professional resume PDF"""
        doc = SimpleDocTemplate(
            self.buffer,
            pagesize=letter,
            rightMargin=self.margin,
            leftMargin=self.margin,
            topMargin=self.margin,
            bottomMargin=self.margin
        )
        
        # Build the story (content)
        story = []
        styles = getSampleStyleSheet()
        
        # Custom styles
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=12,
            textColor=darkblue,
            alignment=TA_CENTER
        )
        
        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading2'],
            fontSize=14,
            spaceAfter=6,
            spaceBefore=12,
            textColor=darkblue,
            borderWidth=1,
            borderColor=darkblue,
            borderPadding=3
        )
        
        body_style = ParagraphStyle(
            'CustomBody',
            parent=styles['Normal'],
            fontSize=10,
            spaceAfter=6,
            alignment=TA_LEFT
        )
        
        # Header - Contact Information
        contact_info = resume_data.get('contact_info', {})
        if contact_info:
            name = contact_info.get('name', 'Professional Resume')
            story.append(Paragraph(name, title_style))

            contact_details = []
            if contact_info.get('email'):
                contact_details.append(contact_info['email'])
            if contact_info.get('phone'):
                contact_details.append(contact_info['phone'])
            if contact_info.get('location'):
                contact_details.append(contact_info['location'])

            if contact_details:
                contact_text = " | ".join(contact_details)
                story.append(Paragraph(contact_text, body_style))

            # Additional contact links
            links = []
            if contact_info.get('portfolio_url'):
                links.append(f"Portfolio: {contact_info['portfolio_url']}")
            if contact_info.get('linkedin'):
                links.append(f"LinkedIn: {contact_info['linkedin']}")
            if contact_info.get('github'):
                links.append(f"GitHub: {contact_info['github']}")

            if links:
                links_text = " | ".join(links)
                story.append(Paragraph(links_text, body_style))

            story.append(Spacer(1, 12))
        
        # Professional Summary
        summary = resume_data.get('professional_summary', '')
        if summary:
            story.append(Paragraph("PROFESSIONAL SUMMARY", heading_style))
            story.append(Paragraph(summary, body_style))
            story.append(Spacer(1, 12))
        
        # Skills
        skills = resume_data.get('highlighted_skills', [])
        if skills:
            story.append(Paragraph("CORE COMPETENCIES", heading_style))

            # Format skills in columns
            skills_text = " • ".join(skills[:15])  # Limit to 15 skills
            story.append(Paragraph(skills_text, body_style))
            story.append(Spacer(1, 12))

        # AI Expertise Section
        ai_expertise = resume_data.get('ai_expertise', {})
        if ai_expertise and any(ai_expertise.values()):
            story.append(Paragraph("AI EXPERTISE & AUTOMATION", heading_style))

            if ai_expertise.get('prompt_engineering'):
                story.append(Paragraph("<b>Prompt Engineering:</b>", body_style))
                for skill in ai_expertise['prompt_engineering'][:3]:
                    story.append(Paragraph(f"• {skill}", body_style))
                story.append(Spacer(1, 6))

            if ai_expertise.get('ai_tools'):
                tools_text = f"<b>AI Tools Mastery:</b> {', '.join(ai_expertise['ai_tools'][:8])}"
                story.append(Paragraph(tools_text, body_style))
                story.append(Spacer(1, 6))

            if ai_expertise.get('automation'):
                story.append(Paragraph("<b>Workflow Automation:</b>", body_style))
                for skill in ai_expertise['automation'][:3]:
                    story.append(Paragraph(f"• {skill}", body_style))

            story.append(Spacer(1, 12))
        
        # Experience
        experience = resume_data.get('experience', [])
        if experience:
            story.append(Paragraph("PROFESSIONAL EXPERIENCE", heading_style))

            for exp in experience:
                # Job title and company
                title = exp.get('title', '')
                company = exp.get('company', '')
                duration = exp.get('duration', '')
                exp_type = exp.get('type', '')

                if title and company:
                    job_header = f"<b>{title}</b> | {company}"
                    if duration:
                        job_header += f" | {duration}"
                    if exp_type:
                        job_header += f" | {exp_type}"
                    story.append(Paragraph(job_header, body_style))

                # Achievements
                achievements = exp.get('achievements', [])
                if achievements:
                    story.append(Paragraph("<b>Key Achievements:</b>", body_style))
                    for achievement in achievements[:5]:  # Limit to 5 achievements
                        story.append(Paragraph(f"• {achievement}", body_style))

                story.append(Spacer(1, 10))
        
        # Projects
        projects = resume_data.get('projects', [])
        if projects:
            story.append(Paragraph("KEY PROJECTS", heading_style))

            for project in projects[:4]:  # Limit to 4 projects
                name = project.get('name', '')
                role = project.get('role', '')
                description = project.get('description', '')
                technologies = project.get('technologies', [])
                achievements = project.get('achievements', [])

                if name:
                    project_header = f"<b>{name}</b>"
                    if role:
                        project_header += f" | {role}"
                    story.append(Paragraph(project_header, body_style))

                if description:
                    story.append(Paragraph(description, body_style))

                if technologies:
                    tech_text = f"<b>Technologies:</b> {', '.join(technologies[:10])}"
                    story.append(Paragraph(tech_text, body_style))

                if achievements:
                    story.append(Paragraph("<b>Results:</b>", body_style))
                    for achievement in achievements[:3]:
                        story.append(Paragraph(f"• {achievement}", body_style))

                story.append(Spacer(1, 10))

        # Unique Value Propositions
        unique_props = resume_data.get('unique_value_props', [])
        if unique_props:
            story.append(Paragraph("UNIQUE VALUE PROPOSITIONS", heading_style))
            for prop in unique_props[:3]:
                story.append(Paragraph(f"• {prop}", body_style))
            story.append(Spacer(1, 12))
        
        # Education
        education = resume_data.get('education', {})
        if education:
            story.append(Paragraph("EDUCATION", heading_style))
            
            degree = education.get('degree', '')
            institution = education.get('institution', '')
            duration = education.get('duration', '')
            
            if degree and institution:
                edu_text = f"<b>{degree}</b> | {institution}"
                if duration:
                    edu_text += f" | {duration}"
                story.append(Paragraph(edu_text, body_style))
            
            # Relevant coursework
            coursework = education.get('relevant_coursework', [])
            if coursework:
                coursework_text = f"Relevant Coursework: {', '.join(coursework[:5])}"
                story.append(Paragraph(coursework_text, body_style))
            
            story.append(Spacer(1, 8))
        
        # Certifications
        certifications = resume_data.get('certifications', [])
        if certifications:
            story.append(Paragraph("CERTIFICATIONS", heading_style))
            for cert in certifications[:5]:  # Limit to 5 certifications
                story.append(Paragraph(f"• {cert}", body_style))
            story.append(Spacer(1, 8))
        
        # Footer with customization info
        customization_info = resume_data.get('customization_notes', '')
        if customization_info:
            footer_style = ParagraphStyle(
                'Footer',
                parent=styles['Normal'],
                fontSize=8,
                textColor=gray,
                alignment=TA_CENTER
            )
            story.append(Spacer(1, 20))
            story.append(Paragraph(f"Resume customized for: {resume_data.get('customized_for', 'Position')}", footer_style))
            story.append(Paragraph(f"Generated on: {datetime.now().strftime('%B %d, %Y')}", footer_style))
        
        # Build PDF
        doc.build(story)
        
    def create_cover_letter_pdf(self, cover_letter_text, job_info=None):
        """Create a professional cover letter PDF"""
        doc = SimpleDocTemplate(
            self.buffer,
            pagesize=letter,
            rightMargin=self.margin,
            leftMargin=self.margin,
            topMargin=self.margin,
            bottomMargin=self.margin
        )
        
        story = []
        styles = getSampleStyleSheet()
        
        # Custom styles
        title_style = ParagraphStyle(
            'CoverLetterTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=20,
            textColor=darkblue,
            alignment=TA_CENTER
        )
        
        body_style = ParagraphStyle(
            'CoverLetterBody',
            parent=styles['Normal'],
            fontSize=11,
            spaceAfter=12,
            alignment=TA_LEFT,
            leading=14
        )
        
        # Title
        story.append(Paragraph("Cover Letter", title_style))
        
        # Date
        date_text = datetime.now().strftime("%B %d, %Y")
        story.append(Paragraph(date_text, body_style))
        story.append(Spacer(1, 12))
        
        # Job info if provided
        if job_info:
            company = job_info.get('company', '')
            position = job_info.get('title', '')
            if company or position:
                story.append(Paragraph(f"Re: {position} at {company}", body_style))
                story.append(Spacer(1, 12))
        
        # Cover letter content
        # Split into paragraphs
        paragraphs = cover_letter_text.split('\n\n')
        for paragraph in paragraphs:
            if paragraph.strip():
                story.append(Paragraph(paragraph.strip(), body_style))
        
        # Closing
        story.append(Spacer(1, 20))
        story.append(Paragraph("Sincerely,", body_style))
        story.append(Spacer(1, 20))
        story.append(Paragraph("Tishbian Meshach S", body_style))
        
        # Build PDF
        doc.build(story)
