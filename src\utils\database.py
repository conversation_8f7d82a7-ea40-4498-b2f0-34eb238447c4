import sqlite3
import json
from datetime import datetime
from pathlib import Path

class JobDatabase:
    def __init__(self, db_path='data/jobs.db'):
        self.db_path = db_path
        Path(db_path).parent.mkdir(exist_ok=True)
        self.init_database()
    
    def init_database(self):
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS jobs (
                    job_id TEXT PRIMARY KEY,
                    title TEXT NOT NULL,
                    company TEXT NOT NULL,
                    description TEXT,
                    requirements TEXT,
                    apply_url TEXT,
                    posted_date TEXT,
                    source TEXT,
                    location TEXT,
                    scraped_at TEXT,
                    status TEXT DEFAULT 'new',
                    match_score INTEGER DEFAULT 0,
                    category TEXT
                )
            ''')
            
            conn.execute('''
                CREATE TABLE IF NOT EXISTS applications (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    job_id TEXT,
                    applied_at TEXT,
                    status TEXT DEFAULT 'submitted',
                    response_received TEXT,
                    notes TEXT,
                    FOREIGN KEY (job_id) REFERENCES jobs (job_id)
                )
            ''')
    
    def insert_job(self, job_data):
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                INSERT OR REPLACE INTO jobs 
                (job_id, title, company, description, requirements, apply_url, 
                 posted_date, source, location, scraped_at, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                job_data['job_id'], job_data['title'], job_data['company'],
                job_data['description'], job_data['requirements'], 
                job_data['apply_url'], job_data['posted_date'],
                job_data['source'], job_data['location'], 
                job_data['scraped_at'], job_data['status']
            ))
    
    def get_new_jobs(self):
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("SELECT * FROM jobs WHERE status = 'new'")
            return [dict(row) for row in cursor.fetchall()]
    
    def update_job_analysis(self, job_id, analysis_data):
        """Update job with analysis results"""
        with sqlite3.connect(self.db_path) as conn:
            match_score = analysis_data.get('match_score', 0)
            category = analysis_data.get('compatibility_level', 'unknown')
            conn.execute('''
                UPDATE jobs SET match_score = ?, category = ?, status = 'analyzed'
                WHERE job_id = ?
            ''', (match_score, category, job_id))

    def update_job_status(self, job_id, status):
        """Update job status"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                UPDATE jobs SET status = ? WHERE job_id = ?
            ''', (status, job_id))

    def get_job_by_id(self, job_id):
        """Get a specific job by ID"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("SELECT * FROM jobs WHERE job_id = ?", (job_id,))
            row = cursor.fetchone()
            return dict(row) if row else None

    def get_all_jobs(self):
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("SELECT * FROM jobs ORDER BY scraped_at DESC")
            return [dict(row) for row in cursor.fetchall()]

    def get_jobs_by_status(self, status):
        """Get jobs by status"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("SELECT * FROM jobs WHERE status = ? ORDER BY scraped_at DESC", (status,))
            return [dict(row) for row in cursor.fetchall()]

    def get_high_match_jobs(self, min_score=70):
        """Get jobs with high match scores"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute(
                "SELECT * FROM jobs WHERE match_score >= ? ORDER BY match_score DESC",
                (min_score,)
            )
            return [dict(row) for row in cursor.fetchall()]

    def clear_all_jobs(self):
        """Clear all jobs from the database"""
        with sqlite3.connect(self.db_path) as conn:
            # Clear jobs table
            conn.execute("DELETE FROM jobs")
            # Clear applications table
            conn.execute("DELETE FROM applications")
            conn.commit()

            # Reset auto-increment counters
            conn.execute("DELETE FROM sqlite_sequence WHERE name='applications'")
            conn.commit()
