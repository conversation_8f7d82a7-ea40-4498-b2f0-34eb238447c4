@echo off
echo ============================================================
echo JOB AI - DEVELOPMENT SERVER STARTER
echo ============================================================
echo.

echo Starting Flask backend server...
start "Flask Backend" cmd /k "python api_server.py"

echo Waiting for backend to start...
timeout /t 5 /nobreak > nul

echo Starting Next.js frontend server...
cd frontend
start "Next.js Frontend" cmd /k "npm run dev"
cd ..

echo.
echo ============================================================
echo SERVERS STARTED!
echo ============================================================
echo Frontend: http://localhost:3000
echo Backend:  http://localhost:8000
echo.
echo Press any key to continue...
pause > nul
