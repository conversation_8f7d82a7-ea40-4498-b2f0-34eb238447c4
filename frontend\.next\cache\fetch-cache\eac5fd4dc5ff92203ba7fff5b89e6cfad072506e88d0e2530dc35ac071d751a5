{"kind": "FETCH", "data": {"headers": {"access-control-allow-origin": "*", "connection": "close", "content-length": "16070", "content-type": "application/json", "date": "Wed, 16 Jul 2025 15:25:43 GMT", "server": "Werkzeug/3.1.3 Python/3.12.5"}, "body": "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", "status": 200, "url": "http://localhost:8000/api/jobs"}, "revalidate": 31536000, "tags": []}