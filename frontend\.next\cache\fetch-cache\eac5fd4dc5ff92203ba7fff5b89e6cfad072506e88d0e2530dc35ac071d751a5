{"kind": "FETCH", "data": {"headers": {"access-control-allow-origin": "*", "connection": "close", "content-length": "27425", "content-type": "application/json", "date": "Wed, 16 Jul 2025 16:48:48 GMT", "server": "Werkzeug/3.1.3 Python/3.12.5"}, "body": "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", "status": 200, "url": "http://localhost:8000/api/jobs"}, "revalidate": 31536000, "tags": []}