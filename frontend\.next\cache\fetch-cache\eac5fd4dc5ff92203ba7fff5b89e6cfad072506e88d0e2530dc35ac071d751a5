{"kind": "FETCH", "data": {"headers": {"access-control-allow-origin": "*", "connection": "close", "content-length": "16094", "content-type": "application/json", "date": "Wed, 16 Jul 2025 16:31:17 GMT", "server": "Werkzeug/3.1.3 Python/3.12.5"}, "body": "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", "status": 200, "url": "http://localhost:8000/api/jobs"}, "revalidate": 31536000, "tags": []}