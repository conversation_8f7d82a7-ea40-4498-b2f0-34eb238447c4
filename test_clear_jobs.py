#!/usr/bin/env python3
"""
Test script to verify the clear jobs functionality
"""

import sys
import os
sys.path.append('src')

from src.utils.database import JobDatabase
from datetime import datetime

def test_clear_jobs():
    """Test the clear jobs functionality"""
    print("🗑️  TESTING CLEAR JOBS FUNCTIONALITY")
    print("="*50)
    
    try:
        # Initialize database
        db = JobDatabase()
        
        # Add some test jobs
        test_jobs = [
            {
                'job_id': 'test_job_1',
                'title': 'UI/UX Designer',
                'company': 'Test Company 1',
                'description': 'Test job description 1',
                'requirements': 'Figma, Adobe XD',
                'apply_url': 'https://example.com/job1',
                'posted_date': '2025-07-16',
                'source': 'Test Source',
                'location': 'Test Location',
                'scraped_at': datetime.now().isoformat(),
                'status': 'new'
            },
            {
                'job_id': 'test_job_2',
                'title': 'Product Designer',
                'company': 'Test Company 2',
                'description': 'Test job description 2',
                'requirements': 'Sketch, Figma',
                'apply_url': 'https://example.com/job2',
                'posted_date': '2025-07-15',
                'source': 'Test Source',
                'location': 'Test Location',
                'scraped_at': datetime.now().isoformat(),
                'status': 'new'
            }
        ]
        
        # Insert test jobs
        for job in test_jobs:
            db.insert_job(job)
        
        # Check jobs before clearing
        jobs_before = db.get_all_jobs()
        print(f"✅ Jobs before clearing: {len(jobs_before)}")
        
        for i, job in enumerate(jobs_before, 1):
            print(f"   {i}. {job['title']} at {job['company']}")
        
        # Clear all jobs
        print(f"\n🗑️  Clearing all jobs...")
        db.clear_all_jobs()
        
        # Check jobs after clearing
        jobs_after = db.get_all_jobs()
        print(f"✅ Jobs after clearing: {len(jobs_after)}")
        
        if len(jobs_after) == 0:
            print("🎉 SUCCESS: All jobs cleared successfully!")
        else:
            print("❌ FAILED: Some jobs still remain in database")
            for job in jobs_after:
                print(f"   Remaining: {job['title']} at {job['company']}")
        
        # Test stats after clearing
        stats = db.get_job_stats()
        print(f"\n📊 Stats after clearing:")
        print(f"   Total: {stats['total']}")
        print(f"   New: {stats['new']}")
        print(f"   Applied: {stats['applied']}")
        print(f"   Analyzed: {stats['analyzed']}")
        
        if stats['total'] == 0:
            print("✅ Stats correctly show 0 jobs")
        else:
            print("❌ Stats still show jobs after clearing")
        
    except Exception as e:
        print(f"❌ Error testing clear jobs: {e}")
        import traceback
        traceback.print_exc()

def test_api_endpoint():
    """Test the API endpoint (requires server to be running)"""
    print("\n" + "="*50)
    print("🌐 TESTING API ENDPOINT")
    print("="*50)
    
    try:
        import requests
        
        # Test the clear jobs API endpoint
        response = requests.delete('http://localhost:5000/api/jobs/clear')
        
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                print(f"✅ API Success: {data['message']}")
                print(f"   Jobs cleared: {data['jobs_cleared']}")
                print(f"   Jobs remaining: {data['jobs_remaining']}")
            else:
                print(f"❌ API Error: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("⚠️  API server not running. Start with: python api_server.py")
    except Exception as e:
        print(f"❌ Error testing API: {e}")

def main():
    """Main test function"""
    print("🧪 CLEAR JOBS FUNCTIONALITY TEST")
    print("Testing database clear and API endpoint")
    print()
    
    # Test database functionality
    test_clear_jobs()
    
    # Test API endpoint
    test_api_endpoint()
    
    print("\n" + "="*50)
    print("TESTING COMPLETE")
    print("="*50)
    print("✅ Database clear functionality is working")
    print("🌐 API endpoint ready for frontend integration")

if __name__ == "__main__":
    main()
