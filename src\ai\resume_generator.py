import json
from src.utils.gemini_client import GeminiClient
from src.utils.logger import setup_logger

class AIResumeGenerator:
    def __init__(self):
        self.gemini = GeminiClient()
        self.logger = setup_logger('resume_generator')

    def customize_resume(self, base_profile, job_data, analysis=None):
        """Generate job-specific resume content using comprehensive config details"""
        try:
            # If no analysis provided, create basic analysis
            if analysis is None:
                from src.ai.job_analyzer import JobAnalyzer
                analyzer = JobAnalyzer()
                analysis = analyzer.analyze_job_match(job_data, base_profile)

            # Extract key information
            job_title = job_data.get('title', 'N/A')
            company = job_data.get('company', 'N/A')
            description = job_data.get('description', 'N/A')
            requirements = job_data.get('requirements', description)

            # Determine AI emphasis level
            is_ai_role = analysis.get('is_ai_role', False)
            ai_relevance = analysis.get('ai_relevance', 'low')
            match_score = analysis.get('match_score', 70)

            # Extract comprehensive profile details from config
            contact_info = {
                'name': base_profile.get('name', ''),
                'email': base_profile.get('email', ''),
                'phone': base_profile.get('phone', ''),
                'location': base_profile.get('location', ''),
                'portfolio_url': base_profile.get('portfolio_url', ''),
                'linkedin': base_profile.get('linkedin', ''),
                'github': base_profile.get('github', '')
            }

            # Get AI expertise details
            ai_expertise = base_profile.get('ai_expertise', {})
            prompt_engineering_skills = ai_expertise.get('prompt_engineering', [])
            ai_tools_mastery = ai_expertise.get('ai_tools_mastery', [])
            workflow_automation = ai_expertise.get('workflow_automation', [])

            # Get unique value propositions
            unique_value_props = base_profile.get('unique_value_propositions', [])

            # Get detailed experience and projects
            experience_details = base_profile.get('experience', [])
            project_details = base_profile.get('projects', [])
            education_details = base_profile.get('education', {})
            certifications = base_profile.get('certifications', [])

            # Get preferences for role targeting
            preferences = base_profile.get('preferences', {})
            target_roles = preferences.get('role_types', [])

            prompt = f"""
            Create a highly customized, ATS-friendly resume for this specific job using comprehensive candidate profile:

            JOB DETAILS:
            - Title: {job_title}
            - Company: {company}
            - Description: {description}
            - Requirements: {requirements}

            JOB ANALYSIS:
            - Match Score: {match_score}/100
            - AI Relevance: {ai_relevance}
            - Key Skills Match: {analysis.get('key_skills_match', [])}
            - Focus Areas: {analysis.get('custom_resume_focus', [])}
            - Recommended Keywords: {analysis.get('recommended_keywords', [])}

            COMPREHENSIVE CANDIDATE PROFILE:

            Contact Information:
            - Name: {contact_info['name']}
            - Email: {contact_info['email']}
            - Phone: {contact_info['phone']}
            - Location: {contact_info['location']}
            - Portfolio: {contact_info['portfolio_url']}
            - LinkedIn: {contact_info['linkedin']}
            - GitHub: {contact_info['github']}

            Professional Summary: {base_profile.get('summary', '')}

            Experience Level: {base_profile.get('experience_level', '')}

            Core Skills: {base_profile.get('skills', [])}

            AI EXPERTISE (Critical Differentiator):
            - Prompt Engineering: {prompt_engineering_skills}
            - AI Tools Mastery: {ai_tools_mastery}
            - Workflow Automation: {workflow_automation}

            DETAILED EXPERIENCE:
            {self._format_experience_for_prompt(experience_details)}

            PROJECTS:
            {self._format_projects_for_prompt(project_details)}

            EDUCATION:
            - Degree: {education_details.get('degree', '')}
            - Institution: {education_details.get('institution', '')}
            - Duration: {education_details.get('duration', '')}
            - Status: {education_details.get('status', '')}

            CERTIFICATIONS: {certifications}

            UNIQUE VALUE PROPOSITIONS: {unique_value_props}

            TARGET ROLES: {target_roles}

            CUSTOMIZATION STRATEGY:
            {"🚀 HEAVILY emphasize AI capabilities - this is an AI-related role! Position as AI-Enhanced Designer." if is_ai_role else "🎯 Include AI skills as key differentiator but focus on core design competencies. Show AI as productivity multiplier."}

            Generate a professionally optimized resume as JSON:
            {{
                "contact_info": {{
                    "name": "{contact_info['name']}",
                    "email": "{contact_info['email']}",
                    "phone": "{contact_info['phone']}",
                    "location": "{contact_info['location']}",
                    "portfolio_url": "{contact_info['portfolio_url']}",
                    "linkedin": "{contact_info['linkedin']}",
                    "github": "{contact_info['github']}"
                }},
                "professional_summary": "Compelling 2-3 sentence summary emphasizing most relevant skills, AI expertise level, and unique value for this specific role",
                "highlighted_skills": ["top 15 skills prioritized by job relevance", "include exact keywords from job posting", "emphasize AI skills appropriately", "include technical and soft skills"],
                "experience": [
                    {{
                        "title": "exact job title",
                        "company": "company name",
                        "duration": "time period",
                        "type": "employment type",
                        "achievements": ["quantified achievement with metrics", "AI-enhanced accomplishment if relevant", "impact-focused result", "relevant skill demonstration"]
                    }}
                ],
                "projects": [
                    {{
                        "name": "project name",
                        "role": "your role in project",
                        "description": "brief description emphasizing relevant technologies and outcomes",
                        "technologies": ["relevant tech stack"],
                        "achievements": ["quantified impact", "specific outcomes", "skills demonstrated"]
                    }}
                ],
                "education": {{
                    "degree": "{education_details.get('degree', '')}",
                    "institution": "{education_details.get('institution', '')}",
                    "duration": "{education_details.get('duration', '')}",
                    "status": "{education_details.get('status', '')}",
                    "relevant_coursework": ["if applicable to job"]
                }},
                "certifications": ["most relevant certifications for this role"],
                "ai_expertise": {{
                    "prompt_engineering": ["most relevant prompt engineering skills"],
                    "ai_tools": ["relevant AI tools for this role"],
                    "automation": ["relevant automation capabilities"]
                }},
                "keywords_optimized": ["ATS keywords strategically included throughout"],
                "ai_emphasis_level": "{ai_relevance}",
                "customization_notes": "specific customizations made for this role and company",
                "unique_value_props": ["top 3 unique value propositions relevant to this role"]
            }}

            CRITICAL REQUIREMENTS:
            1. Use EXACT keywords from job posting for ATS optimization
            2. Quantify ALL achievements with specific metrics and percentages
            3. {"Prominently feature AI and automation skills throughout all sections" if is_ai_role else "Strategically mention AI skills as productivity multipliers and differentiators"}
            4. Tailor experience descriptions to match job requirements
            5. Ensure professional summary directly addresses job needs
            6. Include relevant technical skills and tools mentioned in job posting
            7. Show progression and growth in experience section
            8. Highlight projects that demonstrate skills needed for this role

            Return ONLY the JSON object, no additional text or formatting.
            """

            response = self.gemini.model.generate_content(prompt)
            customized_resume = self._parse_resume_response(response.text)

            if customized_resume:
                # Add metadata
                customized_resume['job_id'] = job_data.get('job_id', '')
                customized_resume['customized_for'] = f"{job_title} at {company}"
                customized_resume['match_score'] = match_score
                return customized_resume
            else:
                return self._get_fallback_resume(base_profile, job_data)

        except Exception as e:
            self.logger.error(f"Error customizing resume: {e}")
            return self._get_fallback_resume(base_profile, job_data)

    def _format_experience_for_prompt(self, experience_details):
        """Format experience details for the AI prompt"""
        formatted_exp = []
        for exp in experience_details:
            exp_str = f"""
            Title: {exp.get('title', '')}
            Company: {exp.get('company', '')}
            Duration: {exp.get('duration', '')}
            Type: {exp.get('type', '')}
            Responsibilities: {exp.get('responsibilities', [])}
            Achievements: {exp.get('achievements', [])}
            """
            formatted_exp.append(exp_str)
        return '\n'.join(formatted_exp)

    def _format_projects_for_prompt(self, project_details):
        """Format project details for the AI prompt"""
        formatted_projects = []
        for project in project_details:
            project_str = f"""
            Name: {project.get('name', '')}
            Role: {project.get('role', '')}
            Description: {project.get('description', '')}
            Technologies: {project.get('technologies', [])}
            Achievements: {project.get('achievements', [])}
            """
            formatted_projects.append(project_str)
        return '\n'.join(formatted_projects)

    def _parse_resume_response(self, response_text):
        """Parse Gemini response and extract resume JSON"""
        try:
            # Try to extract JSON from the response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1

            if start_idx != -1 and end_idx != -1:
                json_str = response_text[start_idx:end_idx]
                resume_data = json.loads(json_str)
                return resume_data
            else:
                self.logger.warning("No JSON found in response, using fallback")
                return None

        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse JSON from resume response: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Error parsing resume response: {e}")
            return None

    def _get_fallback_resume(self, base_profile, job_data):
        """Generate comprehensive fallback resume structure using config details"""
        job_title = job_data.get('title', 'Design Position')
        company = job_data.get('company', 'Company')

        # Extract comprehensive details from config
        contact_info = {
            'name': base_profile.get('name', ''),
            'email': base_profile.get('email', ''),
            'phone': base_profile.get('phone', ''),
            'location': base_profile.get('location', ''),
            'portfolio_url': base_profile.get('portfolio_url', ''),
            'linkedin': base_profile.get('linkedin', ''),
            'github': base_profile.get('github', '')
        }

        # Get AI expertise
        ai_expertise = base_profile.get('ai_expertise', {})

        # Format experience with full details
        formatted_experience = []
        for exp in base_profile.get('experience', []):
            formatted_experience.append({
                "title": exp.get('title', ''),
                "company": exp.get('company', ''),
                "duration": exp.get('duration', ''),
                "type": exp.get('type', ''),
                "achievements": exp.get('achievements', exp.get('responsibilities', []))[:4]
            })

        # Format projects with full details
        formatted_projects = []
        for proj in base_profile.get('projects', []):
            formatted_projects.append({
                "name": proj.get('name', ''),
                "role": proj.get('role', ''),
                "description": proj.get('description', ''),
                "technologies": proj.get('technologies', []),
                "achievements": proj.get('achievements', ['Successful project completion'])
            })

        return {
            "contact_info": contact_info,
            "professional_summary": base_profile.get('summary', f"Innovative {base_profile.get('experience_level', 'Designer')} with expertise in UI/UX design, AI-enhanced workflows, and modern design technologies. Proven track record of increasing productivity through strategic AI integration."),
            "highlighted_skills": base_profile.get('skills', [])[:15],
            "experience": formatted_experience,
            "projects": formatted_projects,
            "education": base_profile.get('education', {}),
            "certifications": base_profile.get('certifications', []),
            "ai_expertise": {
                "prompt_engineering": ai_expertise.get('prompt_engineering', [])[:3],
                "ai_tools": ai_expertise.get('ai_tools_mastery', [])[:5],
                "automation": ai_expertise.get('workflow_automation', [])[:3]
            },
            "keywords_optimized": ["UI/UX Design", "Figma", "Adobe Creative Suite", "AI-Enhanced Design", "Prompt Engineering", "Workflow Automation"],
            "ai_emphasis_level": "medium",
            "customization_notes": f"Fallback resume generated for {job_title} at {company} using comprehensive profile data",
            "unique_value_props": base_profile.get('unique_value_propositions', [])[:3],
            "job_id": job_data.get('job_id', ''),
            "customized_for": f"{job_title} at {company}",
            "match_score": 70
        }

    def make_ats_friendly(self, resume_data, job_requirements):
        """Make resume ATS-friendly based on job requirements"""
        try:
            prompt = f"""
            Optimize this resume to be ATS (Applicant Tracking System) friendly for this job:

            Job Requirements: {job_requirements}
            Current Resume Data: {json.dumps(resume_data, indent=2)}

            Make it ATS-friendly by:
            1. Using exact keywords from job description
            2. Formatting with standard section headers
            3. Using bullet points with action verbs
            4. Quantifying achievements where possible
            5. Matching skill names exactly as mentioned in job posting
            6. Using standard job titles and company formats
            7. Including relevant technical skills prominently

            Return optimized resume sections as a valid JSON object:
            {{
                "professional_summary": "with keywords",
                "skills": ["prioritized and keyword-matched"],
                "experience": ["with quantified achievements"],
                "projects": ["relevant to job"],
                "education": "formatted properly"
            }}

            Ensure 80%+ keyword match with job requirements.
            Return ONLY the JSON object, no additional text.
            """

            response = self.gemini.model.generate_content(prompt)
            optimized = self._parse_resume_response(response.text)

            if optimized:
                return optimized
            else:
                self.logger.warning("ATS optimization failed, returning original resume")
                return resume_data

        except Exception as e:
            self.logger.error(f"Error making resume ATS-friendly: {e}")
            return resume_data

