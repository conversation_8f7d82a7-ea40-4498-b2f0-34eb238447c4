import json
from src.utils.gemini_client import Gemini<PERSON><PERSON>
from src.utils.logger import setup_logger

class AIResumeGenerator:
    def __init__(self):
        self.gemini = GeminiClient()
        self.logger = setup_logger('resume_generator')

    def customize_resume(self, base_profile, job_data, analysis=None):
        """Generate job-specific resume content"""
        try:
            # If no analysis provided, create basic analysis
            if analysis is None:
                from src.ai.job_analyzer import JobAnalyzer
                analyzer = JobAnalyzer()
                analysis = analyzer.analyze_job_match(job_data, base_profile)

            # Extract key information
            job_title = job_data.get('title', 'N/A')
            company = job_data.get('company', 'N/A')
            description = job_data.get('description', 'N/A')
            requirements = job_data.get('requirements', description)

            # Determine AI emphasis level
            is_ai_role = analysis.get('is_ai_role', False)
            ai_relevance = analysis.get('ai_relevance', 'low')
            match_score = analysis.get('match_score', 70)

            prompt = f"""
            Create a highly customized, ATS-friendly resume for this specific job:

            Job Title: {job_title}
            Company: {company}
            Job Description: {description}
            Requirements: {requirements}

            Job Analysis Results:
            - Match Score: {match_score}/100
            - AI Relevance: {ai_relevance}
            - Key Skills Match: {analysis.get('key_skills_match', [])}
            - Focus Areas: {analysis.get('custom_resume_focus', [])}
            - Recommended Keywords: {analysis.get('recommended_keywords', [])}

            Candidate Profile:
            - Name: {base_profile.get('name', '')}
            - Experience Level: {base_profile.get('experience_level', '')}
            - Core Skills: {base_profile.get('skills', [])[:20]}
            - AI Expertise: {base_profile.get('ai_expertise', {})}
            - Experience: {[exp.get('title', '') + ' at ' + exp.get('company', '') for exp in base_profile.get('experience', [])]}

            CUSTOMIZATION STRATEGY:
            {"HEAVILY emphasize AI capabilities - this is an AI-related role!" if is_ai_role else "Include AI skills as differentiator but focus on core design skills"}

            Generate a professionally optimized resume as JSON:
            {{
                "professional_summary": "2-3 sentence summary emphasizing most relevant skills and AI expertise level based on job requirements",
                "highlighted_skills": ["top 12-15 skills prioritized by job relevance", "include exact keywords from job posting", "emphasize AI skills if relevant"],
                "experience": [
                    {{
                        "title": "job title",
                        "company": "company name",
                        "duration": "time period",
                        "achievements": ["quantified achievement 1", "achievement 2 with AI emphasis if relevant", "achievement 3"]
                    }}
                ],
                "projects": [
                    {{
                        "name": "project name",
                        "description": "brief description emphasizing relevant technologies",
                        "technologies": ["relevant tech stack"],
                        "impact": "quantified impact or outcome"
                    }}
                ],
                "education": {{
                    "degree": "degree name",
                    "institution": "institution name",
                    "duration": "time period",
                    "relevant_coursework": ["if applicable"]
                }},
                "certifications": ["relevant certifications"],
                "keywords_optimized": ["ATS keywords included throughout"],
                "ai_emphasis_level": "{ai_relevance}",
                "customization_notes": "brief note on what was emphasized for this role"
            }}

            CRITICAL: Use exact keywords from the job posting. Quantify achievements.
            {"Prominently feature AI and automation skills throughout." if is_ai_role else "Mention AI skills as value-add but focus on core design competencies."}

            Return ONLY the JSON object, no additional text.
            """

            response = self.gemini.model.generate_content(prompt)
            customized_resume = self._parse_resume_response(response.text)

            if customized_resume:
                # Add metadata
                customized_resume['job_id'] = job_data.get('job_id', '')
                customized_resume['customized_for'] = f"{job_title} at {company}"
                customized_resume['match_score'] = match_score
                return customized_resume
            else:
                return self._get_fallback_resume(base_profile, job_data)

        except Exception as e:
            self.logger.error(f"Error customizing resume: {e}")
            return self._get_fallback_resume(base_profile, job_data)

    def _parse_resume_response(self, response_text):
        """Parse Gemini response and extract resume JSON"""
        try:
            # Try to extract JSON from the response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1

            if start_idx != -1 and end_idx != -1:
                json_str = response_text[start_idx:end_idx]
                resume_data = json.loads(json_str)
                return resume_data
            else:
                self.logger.warning("No JSON found in response, using fallback")
                return None

        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse JSON from resume response: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Error parsing resume response: {e}")
            return None

    def _get_fallback_resume(self, base_profile, job_data):
        """Generate fallback resume structure"""
        job_title = job_data.get('title', 'Design Position')
        company = job_data.get('company', 'Company')

        return {
            "professional_summary": f"Innovative {base_profile.get('experience_level', 'Designer')} with expertise in UI/UX design, AI-enhanced workflows, and modern design technologies. Proven track record of increasing productivity through strategic AI integration.",
            "highlighted_skills": base_profile.get('skills', [])[:15],
            "experience": [
                {
                    "title": exp.get('title', ''),
                    "company": exp.get('company', ''),
                    "duration": exp.get('duration', ''),
                    "achievements": exp.get('achievements', exp.get('responsibilities', []))[:3]
                } for exp in base_profile.get('experience', [])
            ],
            "projects": [
                {
                    "name": proj.get('name', ''),
                    "description": proj.get('description', ''),
                    "technologies": proj.get('technologies', []),
                    "impact": proj.get('achievements', ['Successful project completion'])[0] if proj.get('achievements') else 'Successful project completion'
                } for proj in base_profile.get('projects', [])
            ],
            "education": base_profile.get('education', {}),
            "certifications": base_profile.get('certifications', []),
            "keywords_optimized": ["UI/UX Design", "Figma", "Adobe Creative Suite", "AI-Enhanced Design", "Prompt Engineering"],
            "ai_emphasis_level": "medium",
            "customization_notes": f"Fallback resume generated for {job_title} at {company}",
            "job_id": job_data.get('job_id', ''),
            "customized_for": f"{job_title} at {company}",
            "match_score": 70
        }

    def make_ats_friendly(self, resume_data, job_requirements):
        """Make resume ATS-friendly based on job requirements"""
        try:
            prompt = f"""
            Optimize this resume to be ATS (Applicant Tracking System) friendly for this job:

            Job Requirements: {job_requirements}
            Current Resume Data: {json.dumps(resume_data, indent=2)}

            Make it ATS-friendly by:
            1. Using exact keywords from job description
            2. Formatting with standard section headers
            3. Using bullet points with action verbs
            4. Quantifying achievements where possible
            5. Matching skill names exactly as mentioned in job posting
            6. Using standard job titles and company formats
            7. Including relevant technical skills prominently

            Return optimized resume sections as a valid JSON object:
            {{
                "professional_summary": "with keywords",
                "skills": ["prioritized and keyword-matched"],
                "experience": ["with quantified achievements"],
                "projects": ["relevant to job"],
                "education": "formatted properly"
            }}

            Ensure 80%+ keyword match with job requirements.
            Return ONLY the JSON object, no additional text.
            """

            response = self.gemini.model.generate_content(prompt)
            optimized = self._parse_resume_response(response.text)

            if optimized:
                return optimized
            else:
                self.logger.warning("ATS optimization failed, returning original resume")
                return resume_data

        except Exception as e:
            self.logger.error(f"Error making resume ATS-friendly: {e}")
            return resume_data

