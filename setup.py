#!/usr/bin/env python3
"""
Setup script for Job AI - Automated Job Application System
This script helps users set up the system for first-time use.
"""

import os
import sys
from pathlib import Path
import subprocess

def create_directories():
    """Create necessary directories"""
    print("Creating directories...")
    
    directories = [
        'config',
        'data',
        'logs',
        'temp'
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created/verified directory: {directory}")

def create_credentials_template():
    """Create credentials template file"""
    print("\nCreating credentials template...")
    
    creds_path = Path('config/credentials.env')
    
    if creds_path.exists():
        print("✅ Credentials file already exists")
        return
    
    template_content = """# Job AI Configuration
# Copy this file and add your actual API keys

# Google Gemini AI API Key (Required)
# Get your key from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

# Optional: MongoDB connection (if using MongoDB instead of SQLite)
MONGODB_URI=mongodb://localhost:27017/

# Application Settings
REQUEST_DELAY_MIN=8
REQUEST_DELAY_MAX=25
MAX_DAILY_APPLICATIONS=15
JOB_RECENCY_DAYS=7
"""
    
    with open(creds_path, 'w') as f:
        f.write(template_content)
    
    print(f"✅ Created credentials template: {creds_path}")
    print("⚠️  Please edit this file and add your actual GEMINI_API_KEY")

def install_dependencies():
    """Install required Python packages"""
    print("\nInstalling dependencies...")
    
    try:
        # Install from requirements.txt
        result = subprocess.run(
            [sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print("✅ Dependencies installed successfully")
        else:
            print(f"❌ Failed to install dependencies: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error installing dependencies: {e}")
        return False
    
    return True

def install_playwright():
    """Install Playwright browsers"""
    print("\nInstalling Playwright browsers...")
    
    try:
        result = subprocess.run(
            [sys.executable, '-m', 'playwright', 'install', 'chromium'],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print("✅ Playwright browsers installed successfully")
        else:
            print(f"⚠️ Playwright installation warning: {result.stderr}")
            print("You may need to run: playwright install chromium")
            
    except Exception as e:
        print(f"⚠️ Playwright installation error: {e}")
        print("You may need to install Playwright manually")

def create_sample_config():
    """Create sample configuration files"""
    print("\nCreating sample configuration...")
    
    # Create settings.yaml template
    settings_path = Path('config/settings.yaml')
    if not settings_path.exists():
        settings_content = """# Job AI Settings
search:
  keywords:
    - "UI/UX Designer"
    - "Graphic Designer"
    - "Product Designer"
    - "AI Designer"
  
  locations:
    - "Remote"
    - "India"
    - "Tamil Nadu"
  
  experience_levels:
    - "Entry Level"
    - "Internship"
    - "Junior"

automation:
  max_applications_per_day: 15
  delay_between_applications: 30  # seconds
  save_as_draft: true  # Don't auto-submit, save as draft for review

ai:
  emphasis_ai_skills: true
  customize_for_company: true
  ats_optimization: true
"""
        
        with open(settings_path, 'w') as f:
            f.write(settings_content)
        
        print(f"✅ Created settings template: {settings_path}")

def check_system_requirements():
    """Check system requirements"""
    print("Checking system requirements...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        return False
    else:
        print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}")
    
    # Check pip
    try:
        subprocess.run([sys.executable, '-m', 'pip', '--version'], 
                      capture_output=True, check=True)
        print("✅ pip is available")
    except subprocess.CalledProcessError:
        print("❌ pip is not available")
        return False
    
    return True

def print_next_steps():
    """Print next steps for the user"""
    print("\n" + "="*60)
    print("SETUP COMPLETE!")
    print("="*60)
    
    print("\nNext steps:")
    print("1. Edit config/credentials.env and add your GEMINI_API_KEY")
    print("   Get your key from: https://makersuite.google.com/app/apikey")
    print()
    print("2. (Optional) Customize config/settings.yaml for your preferences")
    print()
    print("3. Test the system:")
    print("   python run_tests.py")
    print()
    print("4. Start the application:")
    print("   python api_server.py")
    print()
    print("5. Open your browser and navigate to the frontend application")
    print()
    print("For help and documentation, check the README.md file")
    print()
    print("🚀 Happy job hunting with AI automation!")

def main():
    """Main setup function"""
    print("="*60)
    print("JOB AI - AUTOMATED APPLICATION SYSTEM SETUP")
    print("="*60)
    print()
    print("This script will set up your Job AI system for first-time use.")
    print()
    
    # Check system requirements
    if not check_system_requirements():
        print("\n❌ System requirements not met. Please fix the issues above.")
        return 1
    
    try:
        # Create directories
        create_directories()
        
        # Create configuration files
        create_credentials_template()
        create_sample_config()
        
        # Install dependencies
        if not install_dependencies():
            print("\n⚠️ Dependency installation failed. You may need to install manually.")
        
        # Install Playwright
        install_playwright()
        
        # Print next steps
        print_next_steps()
        
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️ Setup interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
