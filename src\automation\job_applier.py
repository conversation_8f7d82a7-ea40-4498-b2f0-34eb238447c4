import json
import asyncio
from playwright.async_api import async_playwright
from src.utils.gemini_client import Gemini<PERSON><PERSON>
from src.utils.logger import setup_logger
from src.utils.config import Config

class AutoJobApplier:
    def __init__(self):
        self.gemini = GeminiClient()
        self.logger = setup_logger('job_applier')
        self.browser = None

    async def auto_apply(self, job_data, customized_resume, cover_letter):
        """Automatically apply to job using AI navigation"""
        try:
            self.logger.info(f"Starting auto-apply for {job_data.get('title')} at {job_data.get('company')}")

            # Check if apply_url exists
            apply_url = job_data.get('apply_url')
            if not apply_url or apply_url == '':
                self.logger.warning("No apply URL found, cannot auto-apply")
                return {
                    'success': False,
                    'message': 'No application URL available',
                    'action_taken': 'none'
                }

            # Initialize browser automation
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=False, slow_mo=1000)
                page = await browser.new_page()

                try:
                    # Navigate to job application
                    self.logger.info(f"Navigating to: {apply_url}")
                    await page.goto(apply_url, wait_until='networkidle', timeout=30000)

                    # Wait for page to load
                    await page.wait_for_timeout(3000)

                    # AI-powered form analysis
                    form_analysis = await self._analyze_application_form(page)

                    if form_analysis:
                        # Fill application form
                        result = await self._fill_application_form(page, form_analysis, job_data, customized_resume, cover_letter)

                        # Handle submission (save as draft for safety)
                        submission_result = await self._handle_submission(page, job_data, save_as_draft=True)

                        return {
                            'success': True,
                            'message': 'Application form filled and saved as draft',
                            'action_taken': 'form_filled',
                            'form_analysis': form_analysis,
                            'submission_result': submission_result
                        }
                    else:
                        return {
                            'success': False,
                            'message': 'Could not analyze application form',
                            'action_taken': 'analysis_failed'
                        }

                except Exception as e:
                    self.logger.error(f"Error during auto-apply: {e}")
                    return {
                        'success': False,
                        'message': f'Error during application: {str(e)}',
                        'action_taken': 'error'
                    }
                finally:
                    await browser.close()

        except Exception as e:
            self.logger.error(f"Error in auto_apply: {e}")
            return {
                'success': False,
                'message': f'Failed to start application process: {str(e)}',
                'action_taken': 'initialization_failed'
            }

    async def _analyze_application_form(self, page):
        """Use AI to understand form structure"""
        try:
            # Get page content and form elements
            page_content = await page.content()

            # Get all form elements
            form_elements = await page.query_selector_all('input, textarea, select')

            element_info = []
            for element in form_elements:
                try:
                    tag_name = await element.evaluate('el => el.tagName.toLowerCase()')
                    element_type = await element.evaluate('el => el.type || el.tagName.toLowerCase()')
                    name = await element.evaluate('el => el.name || el.id || ""')
                    placeholder = await element.evaluate('el => el.placeholder || ""')
                    required = await element.evaluate('el => el.required')

                    element_info.append({
                        'tag': tag_name,
                        'type': element_type,
                        'name': name,
                        'placeholder': placeholder,
                        'required': required
                    })
                except:
                    continue

            prompt = f"""
            Analyze this job application form and identify field mappings:

            Form Elements Found: {json.dumps(element_info, indent=2)}

            Map these elements to standard application fields:
            - name/full_name
            - email
            - phone
            - resume_upload
            - cover_letter_upload or cover_letter_text
            - experience_years
            - current_company
            - linkedin_url
            - portfolio_url
            - additional_info/comments

            Return JSON mapping with:
            {{
                "field_mappings": {{
                    "name": "field_name_or_id",
                    "email": "field_name_or_id",
                    "phone": "field_name_or_id",
                    "resume_upload": "field_name_or_id",
                    "cover_letter": "field_name_or_id"
                }},
                "required_fields": ["list", "of", "required", "fields"],
                "file_uploads": ["list", "of", "file", "upload", "fields"],
                "submit_button": "submit_button_selector"
            }}

            Return ONLY the JSON object.
            """

            response = self.gemini.model.generate_content(prompt)
            return self._parse_form_analysis(response.text)

        except Exception as e:
            self.logger.error(f"Error analyzing form: {e}")
            return None
    def _parse_form_analysis(self, response_text):
        """Parse AI response for form analysis"""
        try:
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1

            if start_idx != -1 and end_idx != -1:
                json_str = response_text[start_idx:end_idx]
                analysis = json.loads(json_str)
                return analysis
            else:
                self.logger.warning("No JSON found in form analysis response")
                return None

        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse form analysis JSON: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Error parsing form analysis: {e}")
            return None

    async def _fill_application_form(self, page, form_analysis, job_data, customized_resume, cover_letter):
        """Fill the application form with user data"""
        try:
            if not form_analysis or 'field_mappings' not in form_analysis:
                self.logger.error("Invalid form analysis provided")
                return False

            field_mappings = form_analysis['field_mappings']
            user_profile = Config.USER_PROFILE

            # Fill basic information fields
            if 'name' in field_mappings and field_mappings['name']:
                await self._fill_field(page, field_mappings['name'], user_profile.get('name', ''))

            if 'email' in field_mappings and field_mappings['email']:
                await self._fill_field(page, field_mappings['email'], user_profile.get('email', ''))

            if 'phone' in field_mappings and field_mappings['phone']:
                await self._fill_field(page, field_mappings['phone'], user_profile.get('phone', ''))

            # Fill experience and portfolio fields
            if 'linkedin_url' in field_mappings and field_mappings['linkedin_url']:
                await self._fill_field(page, field_mappings['linkedin_url'], user_profile.get('linkedin', ''))

            if 'portfolio_url' in field_mappings and field_mappings['portfolio_url']:
                await self._fill_field(page, field_mappings['portfolio_url'], user_profile.get('portfolio_url', ''))

            # Fill cover letter if text field exists
            if 'cover_letter' in field_mappings and field_mappings['cover_letter'] and cover_letter:
                await self._fill_field(page, field_mappings['cover_letter'], cover_letter)

            # Handle file uploads (resume, cover letter)
            if 'file_uploads' in form_analysis:
                await self._handle_file_uploads(page, form_analysis['file_uploads'], customized_resume, cover_letter)

            self.logger.info("Application form filled successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error filling application form: {e}")
            return False

    async def _fill_field(self, page, field_selector, value):
        """Fill a specific form field"""
        try:
            if not value:
                return

            # Try different selector strategies
            selectors = [
                f'[name="{field_selector}"]',
                f'[id="{field_selector}"]',
                f'#{field_selector}',
                field_selector
            ]

            for selector in selectors:
                try:
                    element = await page.query_selector(selector)
                    if element:
                        await element.clear()
                        await element.fill(str(value))
                        self.logger.debug(f"Filled field {field_selector} with value")
                        return
                except:
                    continue

            self.logger.warning(f"Could not find field: {field_selector}")

        except Exception as e:
            self.logger.error(f"Error filling field {field_selector}: {e}")

    async def _handle_file_uploads(self, page, file_upload_fields, customized_resume, cover_letter):
        """Handle file upload fields"""
        try:
            self.logger.info(f"File upload fields detected: {file_upload_fields}")

            # Generate temporary PDF files
            temp_files = []

            if customized_resume:
                resume_pdf_path = await self._generate_resume_pdf(customized_resume)
                if resume_pdf_path:
                    temp_files.append(('resume', resume_pdf_path))

            if cover_letter:
                cover_letter_pdf_path = await self._generate_cover_letter_pdf(cover_letter)
                if cover_letter_pdf_path:
                    temp_files.append(('cover_letter', cover_letter_pdf_path))

            # Upload files to detected upload fields
            for field_name in file_upload_fields:
                try:
                    # Try to find the file input element
                    file_input = await page.query_selector(f'input[type="file"][name*="{field_name}"]')
                    if not file_input:
                        file_input = await page.query_selector(f'input[type="file"]#{field_name}')
                    if not file_input:
                        file_input = await page.query_selector('input[type="file"]')

                    if file_input and temp_files:
                        # Determine which file to upload based on field name
                        if 'resume' in field_name.lower() and any(f[0] == 'resume' for f in temp_files):
                            file_path = next(f[1] for f in temp_files if f[0] == 'resume')
                            await file_input.set_input_files(file_path)
                            self.logger.info(f"Uploaded resume to field: {field_name}")
                        elif 'cover' in field_name.lower() and any(f[0] == 'cover_letter' for f in temp_files):
                            file_path = next(f[1] for f in temp_files if f[0] == 'cover_letter')
                            await file_input.set_input_files(file_path)
                            self.logger.info(f"Uploaded cover letter to field: {field_name}")
                        elif temp_files:
                            # Default to resume if unclear
                            file_path = temp_files[0][1]
                            await file_input.set_input_files(file_path)
                            self.logger.info(f"Uploaded file to field: {field_name}")

                except Exception as upload_error:
                    self.logger.warning(f"Failed to upload to field {field_name}: {upload_error}")
                    continue

            # Clean up temporary files
            import os
            for file_type, file_path in temp_files:
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        self.logger.debug(f"Cleaned up temporary file: {file_path}")
                except:
                    pass

        except Exception as e:
            self.logger.error(f"Error handling file uploads: {e}")

    async def _generate_resume_pdf(self, resume_data):
        """Generate PDF from resume data"""
        try:
            import tempfile
            import os
            from reportlab.pdfgen import canvas
            from reportlab.lib.pagesizes import letter

            # Create temporary file
            temp_fd, temp_path = tempfile.mkstemp(suffix='.pdf', prefix='resume_')
            os.close(temp_fd)

            # Generate PDF
            c = canvas.Canvas(temp_path, pagesize=letter)
            width, height = letter

            # Add content to PDF
            y_position = height - 50

            # Title
            c.setFont("Helvetica-Bold", 16)
            name = resume_data.get('contact_info', {}).get('name', 'Resume')
            c.drawString(50, y_position, name)
            y_position -= 30

            # Professional Summary
            c.setFont("Helvetica-Bold", 12)
            c.drawString(50, y_position, "Professional Summary")
            y_position -= 20

            c.setFont("Helvetica", 10)
            summary = resume_data.get('professional_summary', '')
            if summary:
                # Simple text wrapping
                words = summary.split()
                line = ""
                for word in words:
                    if len(line + word) < 80:
                        line += word + " "
                    else:
                        c.drawString(50, y_position, line.strip())
                        y_position -= 15
                        line = word + " "
                if line:
                    c.drawString(50, y_position, line.strip())
                    y_position -= 25

            # Skills
            c.setFont("Helvetica-Bold", 12)
            c.drawString(50, y_position, "Skills")
            y_position -= 20

            skills = resume_data.get('highlighted_skills', [])
            if skills:
                c.setFont("Helvetica", 10)
                skills_text = ", ".join(skills[:15])  # Limit skills
                c.drawString(50, y_position, skills_text)
                y_position -= 25

            c.save()
            self.logger.info(f"Generated resume PDF: {temp_path}")
            return temp_path

        except Exception as e:
            self.logger.error(f"Error generating resume PDF: {e}")
            return None

    async def _generate_cover_letter_pdf(self, cover_letter_text):
        """Generate PDF from cover letter text"""
        try:
            import tempfile
            import os
            from reportlab.pdfgen import canvas
            from reportlab.lib.pagesizes import letter

            # Create temporary file
            temp_fd, temp_path = tempfile.mkstemp(suffix='.pdf', prefix='cover_letter_')
            os.close(temp_fd)

            # Generate PDF
            c = canvas.Canvas(temp_path, pagesize=letter)
            width, height = letter

            # Add content
            y_position = height - 50

            c.setFont("Helvetica-Bold", 14)
            c.drawString(50, y_position, "Cover Letter")
            y_position -= 40

            c.setFont("Helvetica", 10)

            # Simple text wrapping for cover letter
            words = cover_letter_text.split()
            line = ""
            for word in words:
                if len(line + word) < 80:
                    line += word + " "
                else:
                    c.drawString(50, y_position, line.strip())
                    y_position -= 15
                    line = word + " "
                    if y_position < 50:  # Start new page if needed
                        c.showPage()
                        y_position = height - 50
            if line:
                c.drawString(50, y_position, line.strip())

            c.save()
            self.logger.info(f"Generated cover letter PDF: {temp_path}")
            return temp_path

        except Exception as e:
            self.logger.error(f"Error generating cover letter PDF: {e}")
            return None

    async def _handle_submission(self, page, job_data, save_as_draft=True):
        """Handle form submission or save as draft"""
        try:
            if save_as_draft:
                # Look for "Save as Draft" or similar buttons
                draft_selectors = [
                    'button:has-text("Save as Draft")',
                    'button:has-text("Save Draft")',
                    'input[value*="draft"]',
                    'button:has-text("Save")'
                ]

                for selector in draft_selectors:
                    try:
                        element = await page.query_selector(selector)
                        if element:
                            await element.click()
                            self.logger.info("Application saved as draft")
                            return {'action': 'saved_as_draft', 'success': True}
                    except:
                        continue

                self.logger.info("No draft save option found - application form filled but not submitted")
                return {'action': 'form_filled_only', 'success': True}
            else:
                # Look for submit button
                submit_selectors = [
                    'button[type="submit"]',
                    'input[type="submit"]',
                    'button:has-text("Submit")',
                    'button:has-text("Apply")',
                    'button:has-text("Send Application")'
                ]

                for selector in submit_selectors:
                    try:
                        element = await page.query_selector(selector)
                        if element:
                            await element.click()
                            self.logger.info("Application submitted")
                            return {'action': 'submitted', 'success': True}
                    except:
                        continue

                self.logger.warning("No submit button found")
                return {'action': 'no_submit_button', 'success': False}

        except Exception as e:
            self.logger.error(f"Error handling submission: {e}")
            return {'action': 'error', 'success': False, 'error': str(e)}