'use client'
import { useState } from 'react'
import { MapPinIcon, BuildingOfficeIcon, CalendarIcon, LinkIcon, SparklesIcon, CogIcon } from '@heroicons/react/24/outline'

interface Job {
  job_id: string
  title: string
  company: string
  description: string
  location: string
  source: string
  posted_date: string
  apply_url: string
  status: string
  match_score: number
}

interface JobCardProps {
  job: Job
  onJobUpdate?: (jobId: string, updates: any) => void
}

export default function JobCard({ job, onJobUpdate }: JobCardProps) {
  const [applying, setApplying] = useState(false)
  const [analyzing, setAnalyzing] = useState(false)
  const [analysis, setAnalysis] = useState<any>(null)
  const [showAnalysis, setShowAnalysis] = useState(false)
  const analyzeJob = async () => {
    setAnalyzing(true)
    try {
      const response = await fetch(`/api/jobs/${job.job_id}/analyze`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })
      const data = await response.json()

      if (data.success) {
        setAnalysis(data.analysis)
        setShowAnalysis(true)
        // Update parent component if callback provided
        onJobUpdate?.(job.job_id, {
          status: 'analyzed',
          match_score: data.analysis.match_score
        })
      } else {
        console.error('Analysis failed:', data.error)
      }
    } catch (error) {
      console.error('Error analyzing job:', error)
    } finally {
      setAnalyzing(false)
    }
  }

  const autoApply = async () => {
    setApplying(true)
    try {
      const response = await fetch(`/api/jobs/${job.job_id}/apply`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })
      const result = await response.json()

      if (result.success) {
        // Show success message (you might want to add a toast library)
        alert('Application submitted successfully!')
        onJobUpdate?.(job.job_id, { status: 'applied' })
      } else {
        alert(`Application failed: ${result.error || 'Unknown error'}`)
      }
    } catch (error) {
      console.error('Error applying to job:', error)
      alert('Error submitting application')
    } finally {
      setApplying(false)
    }
  }

  const customizeResume = async () => {
    try {
      const response = await fetch('/api/resume/customize', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ jobId: job.job_id })
      })
      const data = await response.json()

      if (data.success) {
        // Open resume in new tab or download
        const pdfResponse = await fetch('/api/resume/generate-pdf', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            resumeData: data.resume,
            jobId: job.job_id
          })
        })

        if (pdfResponse.ok) {
          const blob = await pdfResponse.blob()
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = `resume-${job.company}-${job.title}.pdf`
          a.click()
          window.URL.revokeObjectURL(url)
        }
      }
    } catch (error) {
      console.error('Error customizing resume:', error)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'bg-green-100 text-green-800'
      case 'applied': return 'bg-blue-100 text-blue-800'
      case 'analyzed': return 'bg-yellow-100 text-yellow-800'
      case 'failed': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getMatchScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 font-semibold'
    if (score >= 60) return 'text-yellow-600 font-medium'
    return 'text-red-600'
  }

  return (
    <div className="card hover:shadow-lg transition-shadow">
      <div className="flex justify-between items-start mb-4">
        <div className="flex-1">
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            {job.title}
          </h3>
          <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
            <div className="flex items-center">
              <BuildingOfficeIcon className="h-4 w-4 mr-1" />
              {job.company}
            </div>
            <div className="flex items-center">
              <MapPinIcon className="h-4 w-4 mr-1" />
              {job.location}
            </div>
            <div className="flex items-center">
              <CalendarIcon className="h-4 w-4 mr-1" />
              {job.posted_date}
            </div>
          </div>
        </div>
        <div className="flex flex-col items-end space-y-2">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(job.status)}`}>
            {job.status}
          </span>
          <span className="text-xs text-gray-500">
            Source: {job.source}
          </span>
        </div>
      </div>

      <p className="text-gray-700 mb-4 line-clamp-3">
        {job.description}
      </p>

      {/* AI Analysis Section */}
      {showAnalysis && analysis && (
        <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <h4 className="font-semibold text-blue-900 mb-2">AI Analysis Results</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Match Score:</span>
              <span className={`ml-2 ${getMatchScoreColor(analysis.match_score)}`}>
                {analysis.match_score}%
              </span>
            </div>
            <div>
              <span className="font-medium">Priority:</span>
              <span className="ml-2 capitalize">{analysis.application_priority}</span>
            </div>
            <div>
              <span className="font-medium">AI Relevance:</span>
              <span className="ml-2 capitalize">{analysis.ai_relevance}</span>
            </div>
            <div>
              <span className="font-medium">Competition:</span>
              <span className="ml-2 capitalize">{analysis.estimated_competition}</span>
            </div>
          </div>

          {analysis.key_skills_match && analysis.key_skills_match.length > 0 && (
            <div className="mt-3">
              <span className="font-medium text-sm">Matching Skills:</span>
              <div className="flex flex-wrap gap-1 mt-1">
                {analysis.key_skills_match.slice(0, 5).map((skill: string, index: number) => (
                  <span key={index} className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">
                    {skill}
                  </span>
                ))}
              </div>
            </div>
          )}

          {analysis.application_strategy && (
            <div className="mt-3">
              <span className="font-medium text-sm">Strategy:</span>
              <p className="text-sm text-gray-700 mt-1">{analysis.application_strategy}</p>
            </div>
          )}
        </div>
      )}

      <div className="flex justify-between items-center mt-4">
        <div className="flex items-center space-x-4">
          {job.match_score > 0 && (
            <span className={`text-sm ${getMatchScoreColor(job.match_score)}`}>
              Match: {job.match_score}%
            </span>
          )}
          {analysis && analysis.is_ai_role && (
            <span className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">
              AI Role
            </span>
          )}
        </div>

        <div className="flex space-x-2">
          {/* AI Analysis Button */}
          <button
            onClick={analyzeJob}
            disabled={analyzing}
            className="btn-secondary flex items-center text-sm"
          >
            <SparklesIcon className="h-4 w-4 mr-1" />
            {analyzing ? 'Analyzing...' : 'AI Analyze'}
          </button>

          {/* Customize Resume Button */}
          <button
            onClick={customizeResume}
            className="btn-secondary flex items-center text-sm"
          >
            <CogIcon className="h-4 w-4 mr-1" />
            Custom Resume
          </button>

          {/* Auto Apply Button */}
          <button
            onClick={autoApply}
            disabled={applying || job.status === 'applied'}
            className="btn-primary flex items-center text-sm"
          >
            <SparklesIcon className="h-4 w-4 mr-1" />
            {applying ? 'Applying...' : job.status === 'applied' ? 'Applied' : 'Auto Apply'}
          </button>

          {/* Manual Apply Link */}
          {job.apply_url && (
            <a
              href={job.apply_url}
              target="_blank"
              rel="noopener noreferrer"
              className="btn-outline flex items-center text-sm"
            >
              <LinkIcon className="h-4 w-4 mr-1" />
              Manual Apply
            </a>
          )}
        </div>
      </div>
    </div>
  )
}