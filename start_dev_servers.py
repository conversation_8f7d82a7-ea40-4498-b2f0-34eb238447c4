#!/usr/bin/env python3
"""
Development server starter for Job AI system.
Starts both the Flask backend and Next.js frontend servers.
"""

import subprocess
import sys
import time
import os
from pathlib import Path

def check_port_available(port):
    """Check if a port is available"""
    import socket
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        try:
            s.bind(('localhost', port))
            return True
        except OSError:
            return False

def start_backend():
    """Start the Flask backend server"""
    print("🚀 Starting Flask backend server on port 8000...")
    
    try:
        # Start Flask server
        backend_process = subprocess.Popen(
            [sys.executable, 'api_server.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd=os.getcwd()
        )
        
        # Wait a moment for server to start
        time.sleep(3)
        
        # Check if process is still running
        if backend_process.poll() is None:
            print("✅ Flask backend server started successfully")
            return backend_process
        else:
            stdout, stderr = backend_process.communicate()
            print(f"❌ Backend server failed to start")
            print(f"STDOUT: {stdout.decode()}")
            print(f"STDERR: {stderr.decode()}")
            return None
            
    except Exception as e:
        print(f"❌ Error starting backend server: {e}")
        return None

def start_frontend():
    """Start the Next.js frontend server"""
    print("🚀 Starting Next.js frontend server on port 3000...")
    
    frontend_dir = Path('frontend')
    if not frontend_dir.exists():
        print("❌ Frontend directory not found")
        return None
    
    try:
        # Check if node_modules exists
        if not (frontend_dir / 'node_modules').exists():
            print("📦 Installing frontend dependencies...")
            install_process = subprocess.run(
                ['npm', 'install'],
                cwd=frontend_dir,
                capture_output=True,
                text=True
            )
            
            if install_process.returncode != 0:
                print(f"❌ Failed to install dependencies: {install_process.stderr}")
                return None
            else:
                print("✅ Dependencies installed successfully")
        
        # Start Next.js dev server
        frontend_process = subprocess.Popen(
            ['npm', 'run', 'dev'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd=frontend_dir
        )
        
        # Wait a moment for server to start
        time.sleep(5)
        
        # Check if process is still running
        if frontend_process.poll() is None:
            print("✅ Next.js frontend server started successfully")
            return frontend_process
        else:
            stdout, stderr = frontend_process.communicate()
            print(f"❌ Frontend server failed to start")
            print(f"STDOUT: {stdout.decode()}")
            print(f"STDERR: {stderr.decode()}")
            return None
            
    except Exception as e:
        print(f"❌ Error starting frontend server: {e}")
        return None

def main():
    """Main function to start both servers"""
    print("="*60)
    print("JOB AI - DEVELOPMENT SERVER STARTER")
    print("="*60)
    
    # Check if ports are available
    if not check_port_available(8000):
        print("⚠️  Port 8000 is already in use. Please stop any existing Flask server.")
        return 1
    
    if not check_port_available(3000):
        print("⚠️  Port 3000 is already in use. Please stop any existing Next.js server.")
        return 1
    
    # Start backend server
    backend_process = start_backend()
    if not backend_process:
        print("❌ Failed to start backend server. Exiting.")
        return 1
    
    # Start frontend server
    frontend_process = start_frontend()
    if not frontend_process:
        print("❌ Failed to start frontend server. Stopping backend.")
        backend_process.terminate()
        return 1
    
    print("\n" + "="*60)
    print("🎉 BOTH SERVERS STARTED SUCCESSFULLY!")
    print("="*60)
    print("📱 Frontend: http://localhost:3000")
    print("🔧 Backend API: http://localhost:8000")
    print("📚 API Docs: http://localhost:8000/api/jobs")
    print("\n💡 Press Ctrl+C to stop both servers")
    print("="*60)
    
    try:
        # Keep the script running and monitor both processes
        while True:
            time.sleep(1)
            
            # Check if backend is still running
            if backend_process.poll() is not None:
                print("❌ Backend server stopped unexpectedly")
                break
            
            # Check if frontend is still running
            if frontend_process.poll() is not None:
                print("❌ Frontend server stopped unexpectedly")
                break
                
    except KeyboardInterrupt:
        print("\n🛑 Stopping servers...")
        
        # Stop both processes
        if backend_process and backend_process.poll() is None:
            backend_process.terminate()
            print("✅ Backend server stopped")
        
        if frontend_process and frontend_process.poll() is None:
            frontend_process.terminate()
            print("✅ Frontend server stopped")
        
        print("👋 Goodbye!")
        return 0
    
    # If we get here, one of the servers stopped
    print("🛑 Stopping remaining servers...")
    
    if backend_process and backend_process.poll() is None:
        backend_process.terminate()
    
    if frontend_process and frontend_process.poll() is None:
        frontend_process.terminate()
    
    return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
