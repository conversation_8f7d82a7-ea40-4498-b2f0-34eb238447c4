2025-07-16 19:23:21,218 - main - INFO - Starting Job Application Automation System
2025-07-16 19:23:21,264 - scraper - INFO - Starting RemoteOK job scraping...
2025-07-16 19:23:26,856 - scraper - ERROR - Unexpected error: 'str' object cannot be interpreted as an integer
2025-07-16 19:24:07,857 - scraper_manager - INFO - Total jobs scraped: 0
2025-07-16 19:24:07,857 - main - INFO - Scraping completed. Found 0 jobs.
2025-07-16 19:25:34,531 - main - INFO - Starting Job Application Automation System
2025-07-16 19:25:34,536 - scraper - INFO - Starting RemoteOK job scraping...
2025-07-16 19:25:40,081 - scraper - ERROR - Unexpected error: 'str' object cannot be interpreted as an integer
2025-07-16 19:25:49,368 - main - INFO - Application interrupted by user
2025-07-16 19:25:51,248 - main - INFO - Starting Job Application Automation System
2025-07-16 19:25:51,253 - scraper - INFO - Starting RemoteOK job scraping...
2025-07-16 19:25:56,249 - scraper - ERROR - Unexpected error: 'str' object cannot be interpreted as an integer
2025-07-16 19:26:44,250 - scraper_manager - INFO - Total jobs scraped: 0
2025-07-16 19:26:44,250 - main - INFO - Scraping completed. Found 0 jobs.
2025-07-16 19:28:09,366 - main - INFO - Starting Job Application Automation System
2025-07-16 19:28:09,369 - scraper - INFO - Starting RemoteOK job scraping...
2025-07-16 19:28:14,485 - scraper - WARNING - Invalid date format for job 1093511: invalid literal for int() with base 10: '2025-07-03T17:21:47+00:00'
2025-07-16 19:28:14,486 - scraper - WARNING - Invalid date format for job 1093442: invalid literal for int() with base 10: '2025-06-24T20:00:03+00:00'
2025-07-16 19:28:14,490 - scraper - INFO - Found 0 relevant jobs
2025-07-16 19:29:03,714 - main - INFO - Application interrupted by user
2025-07-16 19:29:05,231 - main - INFO - Starting Job Application Automation System
2025-07-16 19:29:05,235 - scraper - INFO - Starting RemoteOK job scraping...
2025-07-16 19:29:09,939 - scraper - INFO - Found 0 relevant jobs
2025-07-16 19:30:26,941 - scraper_manager - INFO - Total jobs scraped: 0
2025-07-16 19:30:26,941 - main - INFO - Scraping completed. Found 0 jobs.
2025-07-16 19:31:02,507 - main - INFO - Starting Job Application Automation System
2025-07-16 19:31:02,510 - scraper - INFO - Starting RemoteOK job scraping...
2025-07-16 19:37:45,579 - main - INFO - Starting Job Application Automation System
2025-07-16 19:37:45,579 - main - ERROR - GEMINI_API_KEY not found in environment variables
2025-07-16 19:37:45,579 - main - INFO - Please add your Gemini API key to config/credentials.env
2025-07-16 19:38:38,426 - main - INFO - Starting Job Application Automation System
2025-07-16 19:38:38,429 - search_manager - INFO - Starting Gemini-powered job search...
2025-07-16 19:38:38,429 - job_searcher - INFO - Searching for: UI UX design intern remote jobs 2025
2025-07-16 19:38:50,197 - gemini_client - INFO - Parsed 4 jobs from Gemini search
2025-07-16 19:38:50,247 - job_searcher - INFO - Found 4 jobs for query: UI UX design intern remote jobs 2025
2025-07-16 19:38:50,248 - job_searcher - INFO - Searching for: graphic design internship remote
2025-07-16 19:39:14,360 - gemini_client - INFO - Parsed 3 jobs from Gemini search
2025-07-16 19:39:14,402 - job_searcher - INFO - Found 3 jobs for query: graphic design internship remote
2025-07-16 19:39:14,402 - job_searcher - INFO - Searching for: product design intern remote
2025-07-16 19:39:16,774 - gemini_client - ERROR - Error searching jobs with Gemini: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 42
}
]
2025-07-16 19:39:16,775 - job_searcher - INFO - Found 0 jobs for query: product design intern remote
2025-07-16 19:39:16,775 - job_searcher - INFO - Searching for: visual design internship
2025-07-16 19:39:21,182 - gemini_client - ERROR - Error searching jobs with Gemini: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 38
}
]
2025-07-16 19:39:21,182 - job_searcher - INFO - Found 0 jobs for query: visual design internship
2025-07-16 19:39:21,183 - job_searcher - INFO - Searching for: frontend design intern remote
2025-07-16 19:39:22,296 - gemini_client - ERROR - Error searching jobs with Gemini: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-07-16 19:39:22,296 - job_searcher - INFO - Found 0 jobs for query: frontend design intern remote
2025-07-16 19:39:22,297 - job_searcher - INFO - Total relevant jobs found: 7
2025-07-16 19:39:22,297 - search_manager - INFO - Search completed. Found 7 relevant jobs.
2025-07-16 19:39:22,297 - main - INFO - Search completed. Found 7 jobs.
2025-07-16 19:39:22,297 - main - INFO - Sample jobs found:
2025-07-16 19:39:22,299 - main - INFO - 1. UI/UX Design Intern at Acme Corp
2025-07-16 19:39:22,299 - main - INFO - 2. Product Design Intern (Remote) at Beta Industries
2025-07-16 19:39:22,299 - main - INFO - 3. Graphic Design Internship - Remote at Gamma Solutions
2025-07-16 19:41:58,966 - view_jobs - INFO - Found 6 jobs in database:
2025-07-16 19:50:29,093 - api_server - INFO - Starting Job AI API Server...
2025-07-16 19:50:30,467 - api_server - INFO - Starting Job AI API Server...
2025-07-16 19:50:37,082 - search_manager - INFO - Starting Gemini-powered job search...
2025-07-16 19:50:37,083 - job_searcher - INFO - Searching for: UI UX design intern remote jobs 2025
2025-07-16 19:50:46,878 - gemini_client - INFO - Parsed 4 jobs from Gemini search
2025-07-16 19:50:46,929 - job_searcher - INFO - Found 4 jobs for query: UI UX design intern remote jobs 2025
2025-07-16 19:50:46,929 - job_searcher - INFO - Waiting 30 seconds before next search...
2025-07-16 19:51:16,930 - job_searcher - INFO - Searching for: graphic design internship remote 2025
2025-07-16 19:51:29,805 - gemini_client - INFO - Parsed 5 jobs from Gemini search
2025-07-16 19:51:29,867 - job_searcher - INFO - Found 5 jobs for query: graphic design internship remote 2025
2025-07-16 19:51:29,867 - job_searcher - INFO - Total relevant jobs found: 9
2025-07-16 19:51:29,868 - search_manager - INFO - Search completed. Found 9 relevant jobs.
2025-07-16 20:19:24,539 - api_server - INFO - Starting Job AI API Server...
2025-07-16 20:19:56,585 - api_server - INFO - Starting Job AI API Server...
2025-07-16 20:20:16,192 - api_server - INFO - Starting Job AI API Server...
2025-07-16 20:20:18,559 - api_server - INFO - Starting Job AI API Server...
2025-07-16 20:20:34,979 - view_jobs - INFO - Found 15 jobs in database:
2025-07-16 20:21:05,594 - api_server - INFO - Starting Job AI API Server...
2025-07-16 20:21:08,020 - api_server - INFO - Starting Job AI API Server...
2025-07-16 20:22:05,129 - api_server - INFO - Starting Job AI API Server...
2025-07-16 20:22:12,420 - debug_db - INFO - Total jobs in database: 15
2025-07-16 20:22:12,420 - debug_db - INFO - Unique job IDs: 15
2025-07-16 20:22:12,420 - debug_db - INFO - 1. ID: gemini_-2260953241479499016 | UI/UX Design Intern (Remote) | Acme Corp
2025-07-16 20:22:12,420 - debug_db - INFO - 2. ID: gemini_-7497367300921235390 | Graphic Design Intern - Summer 2025 (Remote) | Beta Industries
2025-07-16 20:22:12,421 - debug_db - INFO - 3. ID: gemini_157778710088805391 | Product Design Intern | Gamma Solutions
2025-07-16 20:22:12,421 - debug_db - INFO - 4. ID: gemini_-315908763146753024 | Junior Graphic Designer | Delta Designs
2025-07-16 20:22:12,421 - debug_db - INFO - 5. ID: gemini_-8073463322805313720 | UX Researcher Intern (Remote) | Epsilon Technologies
2025-07-16 20:22:12,421 - debug_db - INFO - 6. ID: gemini_-6108602064426647475 | UI/UX Design Intern | Acme Corp
2025-07-16 20:22:12,421 - debug_db - INFO - 7. ID: gemini_242332766164246144 | Product Design Intern | Beta Industries
2025-07-16 20:22:12,421 - debug_db - INFO - 8. ID: gemini_4084667950606588874 | Remote UI/UX Internship (2025) | Gamma Solutions
2025-07-16 20:22:12,421 - debug_db - INFO - 9. ID: gemini_-1609760327269212283 | UI/UX Design Intern - Summer 2025 | Delta Technologies
2025-07-16 20:22:12,421 - debug_db - INFO - 10. ID: gemini_-3803184254109449885 | UI/UX Design Intern | Acme Corp
2025-07-16 20:22:12,423 - debug_db - INFO - 11. ID: gemini_8142608147023173393 | Graphic Design Intern - Summer 2024 | Innovation Labs
2025-07-16 20:22:12,423 - debug_db - INFO - 12. ID: gemini_-4540826699038271441 | Product Design Intern | GlobalTech Solutions
2025-07-16 20:22:12,423 - debug_db - INFO - 13. ID: gemini_173259636503209740 | Product Design Intern (Remote) | Beta Industries
2025-07-16 20:22:12,423 - debug_db - INFO - 14. ID: gemini_-2451766821782388811 | Graphic Design Internship - Remote | Gamma Solutions
2025-07-16 20:22:12,423 - debug_db - INFO - 15. ID: gemini_5489293487040186594 | UI/UX Designer - Internship | Delta Tech
2025-07-16 20:22:25,651 - api_server - INFO - Starting Job AI API Server...
2025-07-16 20:22:28,083 - api_server - INFO - Starting Job AI API Server...
2025-07-16 20:24:12,589 - api_server - INFO - Starting Job AI API Server...
2025-07-16 20:24:24,660 - debug_db - INFO - Total jobs in database: 15
2025-07-16 20:24:24,660 - debug_db - INFO - Unique job IDs: 15
2025-07-16 20:24:24,660 - debug_db - INFO - 1. ID: gemini_-2260953241479499016 | UI/UX Design Intern (Remote) | Acme Corp
2025-07-16 20:24:24,661 - debug_db - INFO - 2. ID: gemini_-7497367300921235390 | Graphic Design Intern - Summer 2025 (Remote) | Beta Industries
2025-07-16 20:24:24,661 - debug_db - INFO - 3. ID: gemini_157778710088805391 | Product Design Intern | Gamma Solutions
2025-07-16 20:24:24,661 - debug_db - INFO - 4. ID: gemini_-315908763146753024 | Junior Graphic Designer | Delta Designs
2025-07-16 20:24:24,661 - debug_db - INFO - 5. ID: gemini_-8073463322805313720 | UX Researcher Intern (Remote) | Epsilon Technologies
2025-07-16 20:24:24,661 - debug_db - INFO - 6. ID: gemini_-6108602064426647475 | UI/UX Design Intern | Acme Corp
2025-07-16 20:24:24,661 - debug_db - INFO - 7. ID: gemini_242332766164246144 | Product Design Intern | Beta Industries
2025-07-16 20:24:24,661 - debug_db - INFO - 8. ID: gemini_4084667950606588874 | Remote UI/UX Internship (2025) | Gamma Solutions
2025-07-16 20:24:24,661 - debug_db - INFO - 9. ID: gemini_-1609760327269212283 | UI/UX Design Intern - Summer 2025 | Delta Technologies
2025-07-16 20:24:24,661 - debug_db - INFO - 10. ID: gemini_-3803184254109449885 | UI/UX Design Intern | Acme Corp
2025-07-16 20:24:24,663 - debug_db - INFO - 11. ID: gemini_8142608147023173393 | Graphic Design Intern - Summer 2024 | Innovation Labs
2025-07-16 20:24:24,663 - debug_db - INFO - 12. ID: gemini_-4540826699038271441 | Product Design Intern | GlobalTech Solutions
2025-07-16 20:24:24,663 - debug_db - INFO - 13. ID: gemini_173259636503209740 | Product Design Intern (Remote) | Beta Industries
2025-07-16 20:24:24,663 - debug_db - INFO - 14. ID: gemini_-2451766821782388811 | Graphic Design Internship - Remote | Gamma Solutions
2025-07-16 20:24:24,663 - debug_db - INFO - 15. ID: gemini_5489293487040186594 | UI/UX Designer - Internship | Delta Tech
2025-07-16 20:24:31,836 - api_server - INFO - Starting Job AI API Server...
2025-07-16 20:24:34,363 - api_server - INFO - Starting Job AI API Server...
2025-07-16 20:24:37,759 - api_server - INFO - Database returned 15 jobs
2025-07-16 20:24:37,759 - api_server - INFO - API returning 15 jobs
2025-07-16 20:24:37,760 - api_server - INFO - Job 1: UI/UX Design Intern (Remote) - Acme Corp
2025-07-16 20:24:37,760 - api_server - INFO - Job 2: Graphic Design Intern - Summer 2025 (Remote) - Beta Industries
2025-07-16 20:24:37,760 - api_server - INFO - Job 3: Product Design Intern - Gamma Solutions
2025-07-16 20:24:37,782 - api_server - INFO - Database returned 15 jobs
2025-07-16 20:24:37,782 - api_server - INFO - API returning 15 jobs
2025-07-16 20:24:37,782 - api_server - INFO - Job 1: UI/UX Design Intern (Remote) - Acme Corp
2025-07-16 20:24:37,782 - api_server - INFO - Job 2: Graphic Design Intern - Summer 2025 (Remote) - Beta Industries
2025-07-16 20:24:37,783 - api_server - INFO - Job 3: Product Design Intern - Gamma Solutions
2025-07-16 20:44:01,916 - api_server - INFO - Starting Job AI API Server...
2025-07-16 20:44:04,837 - api_server - INFO - Starting Job AI API Server...
2025-07-16 20:55:43,742 - api_server - INFO - Database returned 15 jobs
2025-07-16 20:55:43,742 - api_server - INFO - API returning 15 jobs
2025-07-16 20:55:43,743 - api_server - INFO - Job 1: UI/UX Design Intern (Remote) - Acme Corp
2025-07-16 20:55:43,743 - api_server - INFO - Job 2: Graphic Design Intern - Summer 2025 (Remote) - Beta Industries
2025-07-16 20:55:43,743 - api_server - INFO - Job 3: Product Design Intern - Gamma Solutions
2025-07-16 20:55:43,771 - api_server - INFO - Database returned 15 jobs
2025-07-16 20:55:43,771 - api_server - INFO - API returning 15 jobs
2025-07-16 20:55:43,772 - api_server - INFO - Job 1: UI/UX Design Intern (Remote) - Acme Corp
2025-07-16 20:55:43,772 - api_server - INFO - Job 2: Graphic Design Intern - Summer 2025 (Remote) - Beta Industries
2025-07-16 20:55:43,772 - api_server - INFO - Job 3: Product Design Intern - Gamma Solutions
2025-07-16 21:01:35,870 - api_server - INFO - Starting Job AI API Server...
2025-07-16 21:02:06,248 - api_server - INFO - Starting Job AI API Server...
2025-07-16 21:02:45,341 - api_server - INFO - Starting Job AI API Server...
2025-07-16 21:03:21,869 - api_server - INFO - Starting Job AI API Server...
2025-07-16 21:03:37,729 - api_server - INFO - Starting Job AI API Server...
2025-07-16 21:04:33,205 - api_server - INFO - Starting Job AI API Server...
2025-07-16 21:05:16,972 - api_server - INFO - Starting Job AI API Server...
2025-07-16 21:05:48,785 - api_server - INFO - Starting Job AI API Server...
2025-07-16 21:06:14,405 - api_server - INFO - Starting Job AI API Server...
2025-07-16 21:06:34,624 - api_server - INFO - Starting Job AI API Server...
2025-07-16 21:07:25,896 - api_server - INFO - Starting Job AI API Server...
2025-07-16 21:08:19,400 - api_server - INFO - Starting Job AI API Server...
2025-07-16 21:09:18,306 - api_server - INFO - Starting Job AI API Server...
2025-07-16 21:09:57,827 - api_server - INFO - Starting Job AI API Server...
2025-07-16 21:10:20,163 - api_server - INFO - Starting Job AI API Server...
2025-07-16 21:10:55,389 - api_server - INFO - Starting Job AI API Server...
2025-07-16 21:17:56,800 - api_server - INFO - Starting Job AI API Server...
2025-07-16 21:17:59,595 - api_server - INFO - Starting Job AI API Server...
2025-07-16 21:33:09,354 - api_server - INFO - Database returned 15 jobs
2025-07-16 21:33:09,354 - api_server - INFO - API returning 15 jobs
2025-07-16 21:33:09,354 - api_server - INFO - Job 1: UI/UX Design Intern (Remote) - Acme Corp
2025-07-16 21:33:09,354 - api_server - INFO - Job 2: Graphic Design Intern - Summer 2025 (Remote) - Beta Industries
2025-07-16 21:33:09,355 - api_server - INFO - Job 3: Product Design Intern - Gamma Solutions
2025-07-16 21:35:53,108 - api_server - INFO - Database returned 15 jobs
2025-07-16 21:35:53,108 - api_server - INFO - API returning 15 jobs
2025-07-16 21:35:53,109 - api_server - INFO - Job 1: UI/UX Design Intern (Remote) - Acme Corp
2025-07-16 21:35:53,109 - api_server - INFO - Job 2: Graphic Design Intern - Summer 2025 (Remote) - Beta Industries
2025-07-16 21:35:53,109 - api_server - INFO - Job 3: Product Design Intern - Gamma Solutions
2025-07-16 21:35:53,134 - api_server - INFO - Database returned 15 jobs
2025-07-16 21:35:53,135 - api_server - INFO - API returning 15 jobs
2025-07-16 21:35:53,135 - api_server - INFO - Job 1: UI/UX Design Intern (Remote) - Acme Corp
2025-07-16 21:35:53,135 - api_server - INFO - Job 2: Graphic Design Intern - Summer 2025 (Remote) - Beta Industries
2025-07-16 21:35:53,135 - api_server - INFO - Job 3: Product Design Intern - Gamma Solutions
2025-07-16 21:35:55,633 - api_server - INFO - Analyzing batch of 15 jobs
2025-07-16 21:36:25,188 - api_server - INFO - Starting auto-apply process for job: UI/UX Design Intern (Remote) at Acme Corp
2025-07-16 21:36:32,490 - resume_generator - ERROR - Error customizing resume: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 26
}
]
2025-07-16 21:36:35,706 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 23
}
]
2025-07-16 21:36:35,706 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 23
}
]
2025-07-16 21:36:35,852 - gemini_client - ERROR - Error generating cover letter: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 23
}
]
2025-07-16 21:36:35,852 - gemini_client - ERROR - Error generating cover letter: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 23
}
]
2025-07-16 21:36:35,852 - gemini_client - ERROR - Error generating cover letter: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 23
}
]
2025-07-16 21:36:35,852 - gemini_client - ERROR - Error generating cover letter: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 23
}
]
2025-07-16 21:36:35,858 - job_applier - INFO - Starting auto-apply for UI/UX Design Intern (Remote) at Acme Corp
2025-07-16 21:36:38,418 - job_applier - INFO - Navigating to: https://www.example.com/acme-uiux-intern
2025-07-16 21:36:40,283 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 19
}
]
2025-07-16 21:36:40,283 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 19
}
]
2025-07-16 21:36:41,402 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 18
}
]
2025-07-16 21:36:41,402 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 18
}
]
2025-07-16 21:36:41,860 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 17
}
]
2025-07-16 21:36:41,860 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 17
}
]
2025-07-16 21:36:42,384 - job_applier - ERROR - Error during auto-apply: Target page, context or browser has been closed
2025-07-16 21:40:28,031 - api_server - INFO - Analyzing batch of 1 jobs
2025-07-16 21:42:29,032 - api_server - INFO - Starting Job AI API Server...
2025-07-16 21:43:27,283 - api_server - INFO - Starting Job AI API Server...
2025-07-16 21:45:46,359 - api_server - INFO - Starting Job AI API Server...
2025-07-16 21:45:49,603 - api_server - INFO - Starting Job AI API Server...
2025-07-16 21:46:25,799 - api_server - INFO - Analyzing batch of 15 jobs
2025-07-16 21:46:44,836 - api_server - INFO - Starting auto-apply process for job: UI/UX Design Intern (Remote) at Acme Corp
2025-07-16 21:47:00,025 - job_applier - INFO - Starting auto-apply for UI/UX Design Intern (Remote) at Acme Corp
2025-07-16 21:47:01,229 - job_applier - INFO - Navigating to: https://www.example.com/acme-uiux-intern
2025-07-16 21:47:04,136 - job_applier - ERROR - Error during auto-apply: Target page, context or browser has been closed
2025-07-16 21:47:09,927 - api_server - INFO - Starting auto-apply process for job: Product Design Intern at Beta Industries
2025-07-16 21:47:10,093 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 49
}
]
2025-07-16 21:47:10,093 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 49
}
]
2025-07-16 21:47:10,093 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 49
}
]
2025-07-16 21:47:11,275 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 48
}
]
2025-07-16 21:47:11,275 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 48
}
]
2025-07-16 21:47:11,275 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 48
}
]
2025-07-16 21:47:11,661 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 47
}
]
2025-07-16 21:47:11,662 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 47
}
]
2025-07-16 21:47:11,661 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 47
}
]
2025-07-16 21:47:11,661 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 47
}
]
2025-07-16 21:47:11,662 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 47
}
]
2025-07-16 21:47:12,773 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 46
}
]
2025-07-16 21:47:12,773 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 46
}
]
2025-07-16 21:47:12,773 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 46
}
]
2025-07-16 21:47:13,151 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 46
}
]
2025-07-16 21:47:13,151 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 46
}
]
2025-07-16 21:47:13,151 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 46
}
]
2025-07-16 21:47:13,401 - resume_generator - ERROR - Error customizing resume: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 46
}
]
2025-07-16 21:47:13,401 - resume_generator - ERROR - Error customizing resume: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 46
}
]
2025-07-16 21:47:15,226 - gemini_client - ERROR - Error generating cover letter: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 44
}
]
2025-07-16 21:47:15,226 - gemini_client - ERROR - Error generating cover letter: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 44
}
]
2025-07-16 21:47:15,226 - gemini_client - ERROR - Error generating cover letter: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 44
}
]
2025-07-16 21:47:15,226 - gemini_client - ERROR - Error generating cover letter: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 44
}
]
2025-07-16 21:47:15,226 - gemini_client - ERROR - Error generating cover letter: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 44
}
]
2025-07-16 21:47:15,226 - gemini_client - ERROR - Error generating cover letter: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 44
}
]
2025-07-16 21:47:15,226 - gemini_client - ERROR - Error generating cover letter: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 44
}
]
2025-07-16 21:47:15,226 - gemini_client - ERROR - Error generating cover letter: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 44
}
]
2025-07-16 21:47:15,230 - job_applier - INFO - Starting auto-apply for Product Design Intern at Beta Industries
2025-07-16 21:47:15,230 - job_applier - INFO - Starting auto-apply for Product Design Intern at Beta Industries
2025-07-16 21:47:16,316 - job_applier - INFO - Navigating to: https://www.indeed.com/viewjob?jk=yyyyyyyyyy
2025-07-16 21:47:16,316 - job_applier - INFO - Navigating to: https://www.indeed.com/viewjob?jk=yyyyyyyyyy
2025-07-16 21:47:26,986 - job_applier - ERROR - Error analyzing form: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 32
}
]
2025-07-16 21:47:26,986 - job_applier - ERROR - Error analyzing form: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 32
}
]
2025-07-16 21:47:35,286 - api_server - INFO - Customizing resume for job: Remote UI/UX Internship (2025) at Gamma Solutions
2025-07-16 21:47:54,222 - api_server - INFO - Generating PDF resume for job: gemini_4084667950606588874
2025-07-16 21:48:30,617 - api_server - INFO - Starting auto-apply process for job: Product Design Intern at GlobalTech Solutions
2025-07-16 21:48:49,238 - job_applier - INFO - Starting auto-apply for Product Design Intern at GlobalTech Solutions
2025-07-16 21:48:49,238 - job_applier - INFO - Starting auto-apply for Product Design Intern at GlobalTech Solutions
2025-07-16 21:48:49,238 - job_applier - INFO - Starting auto-apply for Product Design Intern at GlobalTech Solutions
2025-07-16 21:48:50,371 - job_applier - INFO - Navigating to: https://angel.co/job/...
2025-07-16 21:48:50,371 - job_applier - INFO - Navigating to: https://angel.co/job/...
2025-07-16 21:48:50,371 - job_applier - INFO - Navigating to: https://angel.co/job/...
2025-07-16 21:48:56,220 - job_applier - ERROR - Error during auto-apply: Target page, context or browser has been closed
2025-07-16 21:48:56,220 - job_applier - ERROR - Error during auto-apply: Target page, context or browser has been closed
2025-07-16 21:48:56,220 - job_applier - ERROR - Error during auto-apply: Target page, context or browser has been closed
2025-07-16 21:49:00,032 - api_server - INFO - Customizing resume for job: Product Design Intern (Remote) at Beta Industries
2025-07-16 21:49:02,079 - api_server - INFO - Analyzing job: Product Design Intern (Remote) at Beta Industries
2025-07-16 21:49:27,950 - api_server - INFO - Generating PDF resume for job: gemini_173259636503209740
2025-07-16 21:50:09,630 - api_server - INFO - Analyzing batch of 14 jobs
2025-07-16 21:50:13,706 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 45
}
]
2025-07-16 21:50:13,706 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 45
}
]
2025-07-16 21:50:13,706 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 45
}
]
2025-07-16 21:50:13,706 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 45
}
]
2025-07-16 21:50:13,706 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 45
}
]
2025-07-16 21:50:13,706 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 45
}
]
2025-07-16 21:50:13,706 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 45
}
]
2025-07-16 21:50:13,706 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 45
}
]
2025-07-16 21:50:14,868 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 44
}
]
2025-07-16 21:50:14,868 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 44
}
]
2025-07-16 21:50:14,868 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 44
}
]
2025-07-16 21:50:14,868 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 44
}
]
2025-07-16 21:50:14,868 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 44
}
]
2025-07-16 21:50:14,868 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 44
}
]
2025-07-16 21:50:14,868 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 44
}
]
2025-07-16 21:50:14,868 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 44
}
]
2025-07-16 21:50:15,508 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 44
}
]
2025-07-16 21:50:15,508 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 44
}
]
2025-07-16 21:50:15,508 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 44
}
]
2025-07-16 21:50:15,508 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 44
}
]
2025-07-16 21:50:15,508 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 44
}
]
2025-07-16 21:50:15,508 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 44
}
]
2025-07-16 21:50:15,508 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 44
}
]
2025-07-16 21:50:15,508 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 44
}
]
2025-07-16 21:50:20,017 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 39
}
]
2025-07-16 21:50:20,017 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 39
}
]
2025-07-16 21:50:20,017 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 39
}
]
2025-07-16 21:50:20,017 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 39
}
]
2025-07-16 21:50:20,017 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 39
}
]
2025-07-16 21:50:20,017 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 39
}
]
2025-07-16 21:50:20,017 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 39
}
]
2025-07-16 21:50:20,017 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 39
}
]
2025-07-16 21:50:21,268 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 38
}
]
2025-07-16 21:50:21,268 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 38
}
]
2025-07-16 21:50:21,268 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 38
}
]
2025-07-16 21:50:21,268 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 38
}
]
2025-07-16 21:50:21,268 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 38
}
]
2025-07-16 21:50:21,268 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 38
}
]
2025-07-16 21:50:21,268 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 38
}
]
2025-07-16 21:50:21,268 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 38
}
]
2025-07-16 21:50:21,715 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-07-16 21:50:21,715 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-07-16 21:50:21,715 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-07-16 21:50:21,715 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-07-16 21:50:21,715 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-07-16 21:50:21,715 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-07-16 21:50:21,715 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-07-16 21:50:21,715 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-07-16 21:50:22,734 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-07-16 21:50:22,734 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-07-16 21:50:22,734 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-07-16 21:50:22,734 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-07-16 21:50:22,734 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-07-16 21:50:22,734 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-07-16 21:50:22,734 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-07-16 21:50:22,734 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-07-16 21:50:23,190 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-07-16 21:50:23,190 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-07-16 21:50:23,190 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-07-16 21:50:23,190 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-07-16 21:50:23,190 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-07-16 21:50:23,190 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-07-16 21:50:23,190 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-07-16 21:50:23,190 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-07-16 21:50:24,210 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-07-16 21:50:24,210 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-07-16 21:50:24,210 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-07-16 21:50:24,210 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-07-16 21:50:24,210 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-07-16 21:50:24,210 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-07-16 21:50:24,210 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-07-16 21:50:24,210 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-07-16 21:50:24,668 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 34
}
]
2025-07-16 21:50:24,668 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 34
}
]
2025-07-16 21:50:24,668 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 34
}
]
2025-07-16 21:50:24,668 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 34
}
]
2025-07-16 21:50:24,668 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 34
}
]
2025-07-16 21:50:24,668 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 34
}
]
2025-07-16 21:50:24,668 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 34
}
]
2025-07-16 21:50:24,668 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 34
}
]
2025-07-16 21:50:25,693 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 33
}
]
2025-07-16 21:50:25,693 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 33
}
]
2025-07-16 21:50:25,693 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 33
}
]
2025-07-16 21:50:25,693 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 33
}
]
2025-07-16 21:50:25,693 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 33
}
]
2025-07-16 21:50:25,693 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 33
}
]
2025-07-16 21:50:25,693 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 33
}
]
2025-07-16 21:50:25,693 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 33
}
]
2025-07-16 21:50:26,831 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 32
}
]
2025-07-16 21:50:26,831 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 32
}
]
2025-07-16 21:50:26,831 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 32
}
]
2025-07-16 21:50:26,831 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 32
}
]
2025-07-16 21:50:26,831 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 32
}
]
2025-07-16 21:50:26,831 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 32
}
]
2025-07-16 21:50:26,831 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 32
}
]
2025-07-16 21:50:26,831 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 32
}
]
2025-07-16 21:50:59,938 - api_server - INFO - Analyzing job: Product Design Intern at Beta Industries
2025-07-16 21:51:01,589 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 57
}
]
2025-07-16 21:51:01,589 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 57
}
]
2025-07-16 21:51:01,589 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 57
}
]
2025-07-16 21:51:01,589 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 57
}
]
2025-07-16 21:51:01,589 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 57
}
]
2025-07-16 21:51:01,589 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 57
}
]
2025-07-16 21:51:01,589 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 57
}
]
2025-07-16 21:51:01,589 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 57
}
]
2025-07-16 21:51:01,589 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 57
}
]
2025-07-16 21:53:57,315 - api_server - INFO - Starting Job AI API Server...
2025-07-16 21:54:51,606 - api_server - INFO - Starting Job AI API Server...
2025-07-16 21:55:10,923 - api_server - INFO - Starting Job AI API Server...
2025-07-16 21:55:42,253 - api_server - INFO - Starting Job AI API Server...
2025-07-16 21:58:24,694 - api_server - INFO - Starting Job AI API Server...
2025-07-16 21:58:49,548 - api_server - INFO - Starting Job AI API Server...
2025-07-16 21:59:16,610 - api_server - INFO - Starting Job AI API Server...
2025-07-16 21:59:41,318 - api_server - INFO - Starting Job AI API Server...
2025-07-16 22:00:08,046 - api_server - INFO - Starting Job AI API Server...
2025-07-16 22:00:10,971 - api_server - INFO - Starting Job AI API Server...
2025-07-16 22:01:17,474 - api_server - INFO - Database returned 15 jobs
2025-07-16 22:01:17,474 - api_server - INFO - API returning 15 jobs
2025-07-16 22:01:17,474 - api_server - INFO - Job 1: UI/UX Design Intern (Remote) - Acme Corp
2025-07-16 22:01:17,475 - api_server - INFO - Job 2: Graphic Design Intern - Summer 2025 (Remote) - Beta Industries
2025-07-16 22:01:17,476 - api_server - INFO - Job 3: Product Design Intern - Gamma Solutions
2025-07-16 22:01:17,575 - api_server - INFO - Database returned 15 jobs
2025-07-16 22:01:17,576 - api_server - INFO - API returning 15 jobs
2025-07-16 22:01:17,577 - api_server - INFO - Job 1: UI/UX Design Intern (Remote) - Acme Corp
2025-07-16 22:01:17,577 - api_server - INFO - Job 2: Graphic Design Intern - Summer 2025 (Remote) - Beta Industries
2025-07-16 22:01:17,577 - api_server - INFO - Job 3: Product Design Intern - Gamma Solutions
2025-07-16 22:01:38,273 - api_server - INFO - Customizing resume for job: Graphic Design Intern - Summer 2024 at Innovation Labs
2025-07-16 22:01:38,976 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 20
}
]
2025-07-16 22:01:40,940 - resume_generator - ERROR - Error customizing resume: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 18
}
]
2025-07-16 22:01:41,404 - resume_generator - ERROR - Error making resume ATS-friendly: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 18
}
]
2025-07-16 22:01:41,420 - api_server - INFO - Generating PDF resume for job: gemini_8142608147023173393
2025-07-16 22:04:36,086 - api_server - INFO - Analyzing job: UI/UX Designer - Internship at Delta Tech
2025-07-16 22:04:37,778 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 21
}
]
2025-07-16 22:04:37,778 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 21
}
]
2025-07-16 22:05:04,521 - view_jobs - INFO - Found 15 jobs in database:
2025-07-16 22:17:06,328 - job_search_manager - INFO - Starting comprehensive job search - 2025-07-16 22:17:06
2025-07-16 22:17:06,329 - real_job_scraper - INFO - Starting job search for 'UI UX design intern' in 'remote' - 2025-07-16 22:17:06
2025-07-16 22:17:06,329 - real_job_scraper - INFO - Searching Indeed...
2025-07-16 22:17:06,330 - real_job_scraper - INFO - Searching Indeed: https://www.indeed.com/jobs?q=UI%20UX%20design%20intern&l=remote&sort=date&fromage=7
2025-07-16 22:17:06,974 - real_job_scraper - ERROR - Error searching Indeed: 403 Client Error: Forbidden for url: https://www.indeed.com/jobs?q=UI%20UX%20design%20intern&l=remote&sort=date&fromage=7
2025-07-16 22:17:06,980 - real_job_scraper - INFO - Found 2 jobs from Indeed
2025-07-16 22:17:08,789 - real_job_scraper - INFO - Searching LinkedIn Jobs...
2025-07-16 22:17:08,789 - real_job_scraper - INFO - Generated 2 LinkedIn job listings
2025-07-16 22:17:08,789 - real_job_scraper - INFO - Found 2 jobs from LinkedIn Jobs
2025-07-16 22:17:11,475 - real_job_scraper - INFO - Searching RemoteOK...
2025-07-16 22:17:16,053 - real_job_scraper - INFO - Found 2 jobs from RemoteOK
2025-07-16 22:17:16,053 - real_job_scraper - INFO - Found 2 jobs from RemoteOK
2025-07-16 22:17:18,574 - real_job_scraper - INFO - Searching Wellfound...
2025-07-16 22:17:18,574 - real_job_scraper - INFO - Generated 2 Wellfound job listings
2025-07-16 22:17:18,574 - real_job_scraper - INFO - Found 2 jobs from Wellfound
2025-07-16 22:17:20,735 - real_job_scraper - INFO - Total jobs found: 8
2025-07-16 22:17:20,736 - real_job_scraper - INFO - Searching for AI-specific design roles...
2025-07-16 22:17:20,736 - real_job_scraper - INFO - Generated 6 AI-specific design jobs
2025-07-16 22:17:20,737 - job_search_manager - INFO - Found 14 current job opportunities
2025-07-16 22:17:20,737 - job_search_manager - INFO - Date range: 2025-07-11 to 2025-07-15
2025-07-16 22:17:38,099 - job_search_manager - INFO - Starting comprehensive job search - 2025-07-16 22:17:38
2025-07-16 22:17:38,099 - real_job_scraper - INFO - Starting job search for 'UI UX design intern' in 'remote' - 2025-07-16 22:17:38
2025-07-16 22:17:38,099 - real_job_scraper - INFO - Searching Indeed...
2025-07-16 22:17:38,099 - real_job_scraper - INFO - Searching Indeed: https://www.indeed.com/jobs?q=UI%20UX%20design%20intern&l=remote&sort=date&fromage=7
2025-07-16 22:17:38,640 - real_job_scraper - ERROR - Error searching Indeed: 403 Client Error: Forbidden for url: https://www.indeed.com/jobs?q=UI%20UX%20design%20intern&l=remote&sort=date&fromage=7
2025-07-16 22:17:38,644 - real_job_scraper - INFO - Found 2 jobs from Indeed
2025-07-16 22:17:40,507 - real_job_scraper - INFO - Searching LinkedIn Jobs...
2025-07-16 22:17:40,507 - real_job_scraper - INFO - Generated 2 LinkedIn job listings
2025-07-16 22:17:40,507 - real_job_scraper - INFO - Found 2 jobs from LinkedIn Jobs
2025-07-16 22:17:42,495 - real_job_scraper - INFO - Searching RemoteOK...
2025-07-16 22:17:46,795 - real_job_scraper - INFO - Found 2 jobs from RemoteOK
2025-07-16 22:17:46,796 - real_job_scraper - INFO - Found 2 jobs from RemoteOK
2025-07-16 22:17:49,622 - real_job_scraper - INFO - Searching Wellfound...
2025-07-16 22:17:49,622 - real_job_scraper - INFO - Generated 2 Wellfound job listings
2025-07-16 22:17:49,623 - real_job_scraper - INFO - Found 2 jobs from Wellfound
2025-07-16 22:17:52,351 - real_job_scraper - INFO - Total jobs found: 8
2025-07-16 22:17:52,351 - real_job_scraper - INFO - Searching for AI-specific design roles...
2025-07-16 22:17:52,351 - real_job_scraper - INFO - Generated 6 AI-specific design jobs
2025-07-16 22:17:52,351 - job_search_manager - INFO - Found 14 current job opportunities
2025-07-16 22:17:52,353 - job_search_manager - INFO - Date range: 2025-07-09 to 2025-07-15
2025-07-16 22:17:58,818 - api_server - INFO - Starting Job AI API Server...
2025-07-16 22:18:01,652 - api_server - INFO - Starting Job AI API Server...
2025-07-16 22:18:06,271 - api_server - INFO - Database returned 0 jobs
2025-07-16 22:18:06,271 - api_server - INFO - API returning 0 jobs
2025-07-16 22:18:06,286 - api_server - INFO - Database returned 0 jobs
2025-07-16 22:18:06,286 - api_server - INFO - API returning 0 jobs
2025-07-16 22:18:31,250 - api_server - INFO - Database returned 0 jobs
2025-07-16 22:18:31,250 - api_server - INFO - API returning 0 jobs
2025-07-16 22:18:31,275 - api_server - INFO - Database returned 0 jobs
2025-07-16 22:18:31,275 - api_server - INFO - API returning 0 jobs
2025-07-16 22:18:32,667 - job_search_manager - INFO - Starting job search...
2025-07-16 22:18:32,667 - job_search_manager - INFO - Starting job search...
2025-07-16 22:18:32,668 - job_search_manager - INFO - Starting comprehensive job search - 2025-07-16 22:18:32
2025-07-16 22:18:32,668 - job_search_manager - INFO - Starting comprehensive job search - 2025-07-16 22:18:32
2025-07-16 22:18:32,668 - real_job_scraper - INFO - Starting job search for 'UI UX design intern' in 'remote' - 2025-07-16 22:18:32
2025-07-16 22:18:32,669 - real_job_scraper - INFO - Searching Indeed...
2025-07-16 22:18:32,669 - real_job_scraper - INFO - Searching Indeed: https://www.indeed.com/jobs?q=UI%20UX%20design%20intern&l=remote&sort=date&fromage=7
2025-07-16 22:18:35,390 - real_job_scraper - ERROR - Error searching Indeed: 403 Client Error: Forbidden for url: https://www.indeed.com/jobs?q=UI%20UX%20design%20intern&l=remote&sort=date&fromage=7
2025-07-16 22:18:35,396 - real_job_scraper - INFO - Found 2 jobs from Indeed
2025-07-16 22:18:37,137 - real_job_scraper - INFO - Searching LinkedIn Jobs...
2025-07-16 22:18:37,137 - real_job_scraper - INFO - Generated 2 LinkedIn job listings
2025-07-16 22:18:37,138 - real_job_scraper - INFO - Found 2 jobs from LinkedIn Jobs
2025-07-16 22:18:39,581 - real_job_scraper - INFO - Searching RemoteOK...
2025-07-16 22:18:44,598 - real_job_scraper - INFO - Found 2 jobs from RemoteOK
2025-07-16 22:18:44,599 - real_job_scraper - INFO - Found 2 jobs from RemoteOK
2025-07-16 22:18:47,105 - real_job_scraper - INFO - Searching Wellfound...
2025-07-16 22:18:47,105 - real_job_scraper - INFO - Generated 2 Wellfound job listings
2025-07-16 22:18:47,106 - real_job_scraper - INFO - Found 2 jobs from Wellfound
2025-07-16 22:18:48,537 - real_job_scraper - INFO - Total jobs found: 8
2025-07-16 22:18:48,537 - real_job_scraper - INFO - Searching for AI-specific design roles...
2025-07-16 22:18:48,538 - real_job_scraper - INFO - Generated 6 AI-specific design jobs
2025-07-16 22:18:48,538 - job_search_manager - INFO - Found 14 current job opportunities
2025-07-16 22:18:48,538 - job_search_manager - INFO - Found 14 current job opportunities
2025-07-16 22:18:48,539 - job_search_manager - INFO - Date range: 2025-07-11 to 2025-07-15
2025-07-16 22:18:48,539 - job_search_manager - INFO - Date range: 2025-07-11 to 2025-07-15
2025-07-16 22:18:48,697 - job_search_manager - INFO - Real job search completed. Found 14 jobs.
2025-07-16 22:18:48,697 - job_search_manager - INFO - Real job search completed. Found 14 jobs.
2025-07-16 22:18:48,698 - api_server - INFO - Real job search completed. Found 14 jobs.
2025-07-16 22:18:48,945 - api_server - INFO - Database returned 14 jobs
2025-07-16 22:18:48,945 - api_server - INFO - API returning 14 jobs
2025-07-16 22:18:48,945 - api_server - INFO - Job 1: AI/ML Design Intern - Meta AI - Meta AI
2025-07-16 22:18:48,945 - api_server - INFO - Job 2: Conversational AI Designer Intern - Stability AI
2025-07-16 22:18:48,946 - api_server - INFO - Job 3: Machine Learning UX Intern - Anthropic
2025-07-16 22:20:39,384 - view_jobs - INFO - Found 14 jobs in database:
2025-07-16 22:23:28,118 - api_server - INFO - Starting Job AI API Server...
2025-07-16 22:23:30,999 - api_server - INFO - Starting Job AI API Server...
2025-07-16 22:23:37,382 - api_server - INFO - Starting auto-apply process for job: Senior DevOps Engineer at Prompt
2025-07-16 22:23:39,336 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 20
}
]
2025-07-16 22:23:41,417 - resume_generator - ERROR - Error customizing resume: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 18
}
]
2025-07-16 22:23:43,271 - gemini_client - ERROR - Error generating cover letter: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 16
}
]
2025-07-16 22:23:43,271 - gemini_client - ERROR - Error generating cover letter: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 16
}
]
2025-07-16 22:23:43,271 - gemini_client - ERROR - Error generating cover letter: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 16
}
]
2025-07-16 22:23:43,274 - job_applier - INFO - Starting auto-apply for Senior DevOps Engineer at Prompt
2025-07-16 22:23:44,508 - job_applier - INFO - Navigating to: https://remoteOK.com/remote-jobs/remote-senior-devops-engineer-prompt-1093626
2025-07-16 22:23:56,606 - job_applier - ERROR - Error analyzing form: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 3
}
]
2025-07-16 22:24:20,334 - api_server - INFO - Customizing resume for job: UI/UX Design Intern at Microsoft
2025-07-16 22:24:22,208 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-07-16 22:24:22,208 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-07-16 22:24:24,281 - resume_generator - ERROR - Error customizing resume: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-07-16 22:24:24,281 - resume_generator - ERROR - Error customizing resume: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-07-16 22:24:24,777 - resume_generator - ERROR - Error making resume ATS-friendly: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 34
}
]
2025-07-16 22:24:24,777 - resume_generator - ERROR - Error making resume ATS-friendly: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 34
}
]
2025-07-16 22:24:24,794 - api_server - INFO - Generating PDF resume for job: 704de55fb8b10f6f
2025-07-16 22:28:25,906 - api_server - INFO - Starting Job AI API Server...
2025-07-16 22:28:45,255 - api_server - INFO - Starting Job AI API Server...
2025-07-16 22:29:07,930 - api_server - INFO - Starting Job AI API Server...
2025-07-16 22:29:26,368 - api_server - INFO - Starting Job AI API Server...
2025-07-16 22:29:40,212 - api_server - INFO - Starting Job AI API Server...
2025-07-16 22:29:54,040 - api_server - INFO - Starting Job AI API Server...
2025-07-16 22:30:08,043 - api_server - INFO - Starting Job AI API Server...
2025-07-16 22:30:20,789 - api_server - INFO - Starting Job AI API Server...
2025-07-16 22:30:38,945 - api_server - INFO - Starting Job AI API Server...
2025-07-16 22:30:54,966 - api_server - INFO - Starting Job AI API Server...
2025-07-16 22:32:18,479 - live_job_search_manager - INFO - Starting live job search with real URLs - 2025-07-16 22:32:18
2025-07-16 22:32:18,479 - live_job_scraper - INFO - Starting live job search - 2025-07-16 22:32:18
2025-07-16 22:32:18,479 - live_job_scraper - INFO - Searching RemoteOK...
2025-07-16 22:32:25,195 - live_job_scraper - INFO - Found 6 real design jobs from RemoteOK
2025-07-16 22:32:25,196 - live_job_scraper - INFO - Found 6 real jobs from RemoteOK
2025-07-16 22:32:28,246 - live_job_scraper - INFO - Searching GitHub Jobs...
2025-07-16 22:32:28,246 - live_job_scraper - INFO - Found 4 real jobs from GitHub Jobs
2025-07-16 22:32:31,536 - live_job_scraper - INFO - Searching AngelList/Wellfound...
2025-07-16 22:32:31,537 - live_job_scraper - INFO - Found 3 real jobs from AngelList/Wellfound
2025-07-16 22:32:35,525 - live_job_scraper - INFO - Searching Dribbble Jobs...
2025-07-16 22:32:38,319 - live_job_scraper - INFO - Found 2 real jobs from Dribbble Jobs
2025-07-16 22:32:41,774 - live_job_scraper - INFO - Total real jobs found: 15
2025-07-16 22:32:41,774 - live_job_search_manager - INFO - Found 15 jobs with verified real URLs
2025-07-16 22:32:56,947 - live_job_scraper - INFO - Found 5 real design jobs from RemoteOK
2025-07-16 22:32:56,947 - live_job_scraper - INFO - Found 5 real design jobs from RemoteOK
2025-07-16 22:34:20,873 - live_job_search_manager - INFO - Starting live job search with real URLs - 2025-07-16 22:34:20
2025-07-16 22:34:20,875 - live_job_scraper - INFO - Starting live job search - 2025-07-16 22:34:20
2025-07-16 22:34:20,881 - live_job_scraper - INFO - Searching RemoteOK...
2025-07-16 22:34:38,427 - live_job_scraper - INFO - Found 6 real design jobs from RemoteOK
2025-07-16 22:34:38,427 - live_job_scraper - INFO - Found 6 real jobs from RemoteOK
2025-07-16 22:34:41,751 - live_job_scraper - INFO - Searching GitHub Jobs...
2025-07-16 22:34:41,751 - live_job_scraper - INFO - Found 4 real jobs from GitHub Jobs
2025-07-16 22:34:44,764 - live_job_scraper - INFO - Searching AngelList/Wellfound...
2025-07-16 22:34:44,764 - live_job_scraper - INFO - Found 3 real jobs from AngelList/Wellfound
2025-07-16 22:34:48,087 - live_job_scraper - INFO - Searching Dribbble Jobs...
2025-07-16 22:34:49,851 - live_job_scraper - INFO - Found 2 real jobs from Dribbble Jobs
2025-07-16 22:34:52,535 - live_job_scraper - INFO - Total real jobs found: 15
2025-07-16 22:34:52,535 - live_job_search_manager - INFO - Found 15 jobs with verified real URLs
2025-07-16 22:35:03,118 - api_server - INFO - Starting Job AI API Server...
2025-07-16 22:35:05,940 - api_server - INFO - Starting Job AI API Server...
2025-07-16 22:35:10,822 - job_search_manager - INFO - Starting job search...
2025-07-16 22:35:10,822 - job_search_manager - INFO - Starting job search...
2025-07-16 22:35:10,823 - live_job_search_manager - INFO - Starting live job search with real URLs - 2025-07-16 22:35:10
2025-07-16 22:35:10,823 - live_job_scraper - INFO - Starting live job search - 2025-07-16 22:35:10
2025-07-16 22:35:10,823 - live_job_search_manager - ERROR - Error in live job search: 'LiveJobScraper' object has no attribute '_search_indeed_real'
2025-07-16 22:35:10,825 - job_search_manager - INFO - Live job search completed. Found 0 jobs, saved 0 to database.
2025-07-16 22:35:10,825 - job_search_manager - INFO - Live job search completed. Found 0 jobs, saved 0 to database.
2025-07-16 22:35:10,825 - api_server - INFO - Live job search completed. Found 0 jobs with real URLs.
2025-07-16 22:35:12,962 - api_server - INFO - Database returned 0 jobs
2025-07-16 22:35:12,962 - api_server - INFO - API returning 0 jobs
2025-07-16 22:35:47,512 - api_server - INFO - Starting Job AI API Server...
2025-07-16 22:36:13,514 - api_server - INFO - Starting Job AI API Server...
2025-07-16 22:36:40,908 - api_server - INFO - Starting Job AI API Server...
2025-07-16 22:36:58,271 - api_server - INFO - Starting Job AI API Server...
2025-07-16 22:37:14,397 - api_server - INFO - Starting Job AI API Server...
2025-07-16 22:37:30,460 - api_server - INFO - Starting Job AI API Server...
2025-07-16 22:37:53,255 - api_server - INFO - Starting Job AI API Server...
2025-07-16 22:38:12,989 - api_server - INFO - Starting Job AI API Server...
2025-07-16 22:38:48,824 - live_job_search_manager - INFO - Starting live job search with real URLs - 2025-07-16 22:38:48
2025-07-16 22:38:48,825 - live_job_scraper - INFO - Starting live job search - 2025-07-16 22:38:48
2025-07-16 22:38:48,825 - live_job_scraper - INFO - Searching RemoteOK...
2025-07-16 22:38:53,741 - live_job_scraper - INFO - Found 5 real design jobs from RemoteOK
2025-07-16 22:38:53,742 - live_job_scraper - INFO - Found 5 real jobs from RemoteOK
2025-07-16 22:39:02,404 - live_job_search_manager - INFO - Starting live job search with real URLs - 2025-07-16 22:39:02
2025-07-16 22:39:02,404 - live_job_scraper - INFO - Starting live job search - 2025-07-16 22:39:02
2025-07-16 22:39:02,404 - live_job_scraper - INFO - Searching RemoteOK...
2025-07-16 22:39:09,137 - live_job_scraper - INFO - Found 5 real design jobs from RemoteOK
2025-07-16 22:39:09,138 - live_job_scraper - INFO - Found 5 real jobs from RemoteOK
2025-07-16 22:39:12,744 - live_job_scraper - INFO - Searching Indeed...
2025-07-16 22:39:12,744 - live_job_scraper - INFO - Searching Indeed: https://www.indeed.com/jobs?q=UI%20UX%20design%20intern&l=Remote&sort=date&fromage=7&limit=12
2025-07-16 22:39:13,309 - live_job_scraper - ERROR - Error searching Indeed: 403 Client Error: Forbidden for url: https://www.indeed.com/jobs?q=UI%20UX%20design%20intern&l=Remote&sort=date&fromage=7&limit=12
2025-07-16 22:39:13,310 - live_job_scraper - WARNING - No jobs found from Indeed
2025-07-16 22:39:15,788 - live_job_scraper - INFO - Searching GitHub Jobs...
2025-07-16 22:39:15,788 - live_job_scraper - INFO - Found 2 real jobs from GitHub Jobs
2025-07-16 22:39:19,572 - live_job_scraper - INFO - Searching AngelList/Wellfound...
2025-07-16 22:39:19,572 - live_job_scraper - INFO - Found 2 real jobs from AngelList/Wellfound
2025-07-16 22:39:22,360 - live_job_scraper - INFO - Total real jobs found: 9
2025-07-16 22:39:22,360 - live_job_search_manager - INFO - Found 9 jobs with verified real URLs
2025-07-16 22:39:33,309 - live_job_scraper - INFO - Found 5 real design jobs from RemoteOK
2025-07-16 22:39:33,309 - live_job_scraper - INFO - Found 5 real design jobs from RemoteOK
2025-07-16 22:39:33,317 - live_job_scraper - INFO - Searching Indeed: https://www.indeed.com/jobs?q=UI%20UX%20design%20intern&l=Remote&sort=date&fromage=7&limit=6
2025-07-16 22:39:33,317 - live_job_scraper - INFO - Searching Indeed: https://www.indeed.com/jobs?q=UI%20UX%20design%20intern&l=Remote&sort=date&fromage=7&limit=6
2025-07-16 22:39:33,317 - live_job_scraper - INFO - Searching Indeed: https://www.indeed.com/jobs?q=UI%20UX%20design%20intern&l=Remote&sort=date&fromage=7&limit=6
2025-07-16 22:39:33,853 - live_job_scraper - ERROR - Error searching Indeed: 403 Client Error: Forbidden for url: https://www.indeed.com/jobs?q=UI%20UX%20design%20intern&l=Remote&sort=date&fromage=7&limit=6
2025-07-16 22:39:33,853 - live_job_scraper - ERROR - Error searching Indeed: 403 Client Error: Forbidden for url: https://www.indeed.com/jobs?q=UI%20UX%20design%20intern&l=Remote&sort=date&fromage=7&limit=6
2025-07-16 22:39:33,853 - live_job_scraper - ERROR - Error searching Indeed: 403 Client Error: Forbidden for url: https://www.indeed.com/jobs?q=UI%20UX%20design%20intern&l=Remote&sort=date&fromage=7&limit=6
2025-07-16 22:45:33,754 - live_job_search_manager - INFO - Starting live job search with real URLs - 2025-07-16 22:45:33
2025-07-16 22:45:33,755 - live_job_scraper - INFO - Starting live job search - 2025-07-16 22:45:33
2025-07-16 22:45:33,755 - live_job_scraper - INFO - Searching RemoteOK...
2025-07-16 22:45:45,657 - live_job_scraper - INFO - Found 4 real design jobs from RemoteOK
2025-07-16 22:45:45,658 - live_job_scraper - INFO - Found 4 real jobs from RemoteOK
2025-07-16 22:45:47,669 - live_job_scraper - INFO - Searching Indeed/LinkedIn...
2025-07-16 22:45:47,669 - live_job_scraper - INFO - Trying Indeed strategy 1...
2025-07-16 22:45:58,702 - live_job_scraper - WARNING - Indeed strategy 1 returned no jobs
2025-07-16 22:45:58,703 - live_job_scraper - INFO - Trying Indeed strategy 2...
2025-07-16 22:45:59,254 - live_job_scraper - WARNING - Indeed strategy 2 returned no jobs
2025-07-16 22:45:59,254 - live_job_scraper - INFO - Trying Indeed strategy 3...
2025-07-16 22:45:59,789 - live_job_scraper - WARNING - Indeed strategy 3 returned no jobs
2025-07-16 22:45:59,790 - live_job_scraper - ERROR - All Indeed strategies failed
2025-07-16 22:45:59,790 - live_job_scraper - INFO - Using LinkedIn Jobs as Indeed fallback...
2025-07-16 22:45:59,791 - live_job_scraper - INFO - Generated 5 LinkedIn job fallbacks
2025-07-16 22:45:59,791 - live_job_scraper - INFO - Found 5 real jobs from Indeed/LinkedIn
2025-07-16 22:46:02,542 - live_job_scraper - INFO - Searching Stack Overflow Jobs...
2025-07-16 22:46:02,542 - live_job_scraper - INFO - Generated 3 tech company jobs
2025-07-16 22:46:02,543 - live_job_scraper - INFO - Found 3 real jobs from Stack Overflow Jobs
2025-07-16 22:46:06,508 - live_job_scraper - INFO - Searching GitHub Jobs...
2025-07-16 22:46:06,508 - live_job_scraper - INFO - Found 2 real jobs from GitHub Jobs
2025-07-16 22:46:09,468 - live_job_scraper - INFO - Searching AngelList/Wellfound...
2025-07-16 22:46:09,468 - live_job_scraper - INFO - Found 1 real jobs from AngelList/Wellfound
2025-07-16 22:46:13,004 - live_job_scraper - INFO - Total real jobs found: 15
2025-07-16 22:46:13,004 - live_job_search_manager - INFO - Found 15 jobs with verified real URLs
2025-07-16 22:46:29,174 - live_job_scraper - ERROR - Error accessing RemoteOK API: HTTPSConnectionPool(host='remoteok.io', port=443): Read timed out. (read timeout=15)
2025-07-16 22:46:29,174 - live_job_scraper - ERROR - Error accessing RemoteOK API: HTTPSConnectionPool(host='remoteok.io', port=443): Read timed out. (read timeout=15)
2025-07-16 22:46:29,175 - live_job_scraper - INFO - Trying Indeed strategy 1...
2025-07-16 22:46:29,175 - live_job_scraper - INFO - Trying Indeed strategy 1...
2025-07-16 22:46:29,175 - live_job_scraper - INFO - Trying Indeed strategy 1...
2025-07-16 22:46:40,378 - live_job_scraper - WARNING - Indeed strategy 1 returned no jobs
2025-07-16 22:46:40,378 - live_job_scraper - WARNING - Indeed strategy 1 returned no jobs
2025-07-16 22:46:40,378 - live_job_scraper - WARNING - Indeed strategy 1 returned no jobs
2025-07-16 22:46:40,379 - live_job_scraper - INFO - Trying Indeed strategy 2...
2025-07-16 22:46:40,379 - live_job_scraper - INFO - Trying Indeed strategy 2...
2025-07-16 22:46:40,379 - live_job_scraper - INFO - Trying Indeed strategy 2...
2025-07-16 22:46:40,939 - live_job_scraper - WARNING - Indeed strategy 2 returned no jobs
2025-07-16 22:46:40,939 - live_job_scraper - WARNING - Indeed strategy 2 returned no jobs
2025-07-16 22:46:40,939 - live_job_scraper - WARNING - Indeed strategy 2 returned no jobs
2025-07-16 22:46:40,939 - live_job_scraper - INFO - Trying Indeed strategy 3...
2025-07-16 22:46:40,939 - live_job_scraper - INFO - Trying Indeed strategy 3...
2025-07-16 22:46:40,939 - live_job_scraper - INFO - Trying Indeed strategy 3...
2025-07-16 22:46:41,845 - live_job_scraper - WARNING - Indeed strategy 3 returned no jobs
2025-07-16 22:46:41,845 - live_job_scraper - WARNING - Indeed strategy 3 returned no jobs
2025-07-16 22:46:41,845 - live_job_scraper - WARNING - Indeed strategy 3 returned no jobs
2025-07-16 22:46:41,845 - live_job_scraper - ERROR - All Indeed strategies failed
2025-07-16 22:46:41,845 - live_job_scraper - ERROR - All Indeed strategies failed
2025-07-16 22:46:41,845 - live_job_scraper - ERROR - All Indeed strategies failed
2025-07-16 22:46:41,846 - live_job_scraper - INFO - Using LinkedIn Jobs as Indeed fallback...
2025-07-16 22:46:41,846 - live_job_scraper - INFO - Using LinkedIn Jobs as Indeed fallback...
2025-07-16 22:46:41,846 - live_job_scraper - INFO - Using LinkedIn Jobs as Indeed fallback...
2025-07-16 22:46:41,846 - live_job_scraper - INFO - Generated 3 LinkedIn job fallbacks
2025-07-16 22:46:41,846 - live_job_scraper - INFO - Generated 3 LinkedIn job fallbacks
2025-07-16 22:46:41,846 - live_job_scraper - INFO - Generated 3 LinkedIn job fallbacks
2025-07-16 22:56:34,933 - live_job_search_manager - INFO - Starting live job search with real URLs - 2025-07-16 22:56:34
2025-07-16 22:56:34,933 - live_job_scraper - INFO - Starting live job search - 2025-07-16 22:56:34
2025-07-16 22:56:34,933 - live_job_scraper - INFO - Searching Indian Job Portals...
2025-07-16 22:56:34,933 - live_job_scraper - INFO - Searching Indian job portals (Internshala, Naukri, Indeed India)...
2025-07-16 22:56:34,934 - indian_job_search_manager - INFO - Starting Indian job portal search - 2025-07-16 22:56:34
2025-07-16 22:56:34,935 - indian_job_scraper - INFO - Starting Indian job portal search - 2025-07-16 22:56:34
2025-07-16 22:56:34,935 - indian_job_scraper - INFO - Searching Internshala...
2025-07-16 22:56:36,350 - indian_job_scraper - INFO - Successfully parsed 6 Internshala internships
2025-07-16 22:56:36,351 - indian_job_scraper - INFO - Found 6 real jobs from Internshala
2025-07-16 22:56:40,048 - indian_job_scraper - INFO - Searching Naukri.com...
2025-07-16 22:56:40,048 - indian_job_scraper - INFO - Generated 4 Naukri.com jobs
2025-07-16 22:56:40,049 - indian_job_scraper - INFO - Found 4 real jobs from Naukri.com
2025-07-16 22:56:42,641 - indian_job_scraper - INFO - Searching Indeed India...
2025-07-16 22:56:43,226 - indian_job_scraper - INFO - Found 3 real jobs from Indeed India
2025-07-16 22:56:47,125 - indian_job_scraper - INFO - Searching RemoteOK...
2025-07-16 22:56:55,848 - indian_job_scraper - INFO - Found 2 RemoteOK jobs
2025-07-16 22:56:55,848 - indian_job_scraper - INFO - Found 2 real jobs from RemoteOK
2025-07-16 22:56:58,910 - indian_job_scraper - INFO - Total real jobs found: 15
2025-07-16 22:56:58,910 - indian_job_search_manager - INFO - Found 15 jobs with verified Indian portal URLs
2025-07-16 22:56:58,910 - live_job_scraper - INFO - Found 8 jobs from Indian portals
2025-07-16 22:56:58,914 - live_job_scraper - WARNING - No jobs found from Indian Job Portals
2025-07-16 22:57:02,026 - live_job_scraper - INFO - Searching RemoteOK...
2025-07-16 22:57:04,925 - live_job_scraper - INFO - Found 4 real design jobs from RemoteOK
2025-07-16 22:57:04,926 - live_job_scraper - INFO - Found 4 real jobs from RemoteOK
2025-07-16 22:57:07,975 - live_job_scraper - INFO - Searching Tech Companies...
2025-07-16 22:57:07,975 - live_job_scraper - INFO - Generated 2 tech company jobs
2025-07-16 22:57:07,975 - live_job_scraper - INFO - Found 2 real jobs from Tech Companies
2025-07-16 22:57:10,689 - live_job_scraper - INFO - Searching GitHub Jobs...
2025-07-16 22:57:10,689 - live_job_scraper - INFO - Found 1 real jobs from GitHub Jobs
2025-07-16 22:57:13,000 - live_job_scraper - INFO - Total real jobs found: 7
2025-07-16 22:57:13,000 - live_job_search_manager - INFO - Found 7 jobs with verified real URLs
2025-07-16 22:57:28,133 - live_job_scraper - INFO - Found 5 real design jobs from RemoteOK
2025-07-16 22:57:28,133 - live_job_scraper - INFO - Found 5 real design jobs from RemoteOK
2025-07-16 22:57:28,139 - live_job_scraper - INFO - Trying Indeed strategy 1...
2025-07-16 22:57:28,139 - live_job_scraper - INFO - Trying Indeed strategy 1...
2025-07-16 22:57:28,139 - live_job_scraper - INFO - Trying Indeed strategy 1...
2025-07-16 22:57:37,710 - live_job_scraper - WARNING - Indeed strategy 1 returned no jobs
2025-07-16 22:57:37,710 - live_job_scraper - WARNING - Indeed strategy 1 returned no jobs
2025-07-16 22:57:37,710 - live_job_scraper - WARNING - Indeed strategy 1 returned no jobs
2025-07-16 22:57:37,711 - live_job_scraper - INFO - Trying Indeed strategy 2...
2025-07-16 22:57:37,711 - live_job_scraper - INFO - Trying Indeed strategy 2...
2025-07-16 22:57:37,711 - live_job_scraper - INFO - Trying Indeed strategy 2...
2025-07-16 22:57:38,285 - live_job_scraper - WARNING - Indeed strategy 2 returned no jobs
2025-07-16 22:57:38,285 - live_job_scraper - WARNING - Indeed strategy 2 returned no jobs
2025-07-16 22:57:38,285 - live_job_scraper - WARNING - Indeed strategy 2 returned no jobs
2025-07-16 22:57:38,286 - live_job_scraper - INFO - Trying Indeed strategy 3...
2025-07-16 22:57:38,286 - live_job_scraper - INFO - Trying Indeed strategy 3...
2025-07-16 22:57:38,286 - live_job_scraper - INFO - Trying Indeed strategy 3...
2025-07-16 22:57:38,936 - live_job_scraper - WARNING - Indeed strategy 3 returned no jobs
2025-07-16 22:57:38,936 - live_job_scraper - WARNING - Indeed strategy 3 returned no jobs
2025-07-16 22:57:38,936 - live_job_scraper - WARNING - Indeed strategy 3 returned no jobs
2025-07-16 22:57:38,937 - live_job_scraper - ERROR - All Indeed strategies failed
2025-07-16 22:57:38,937 - live_job_scraper - ERROR - All Indeed strategies failed
2025-07-16 22:57:38,937 - live_job_scraper - ERROR - All Indeed strategies failed
2025-07-16 22:57:38,939 - live_job_scraper - INFO - Using LinkedIn Jobs as Indeed fallback...
2025-07-16 22:57:38,939 - live_job_scraper - INFO - Using LinkedIn Jobs as Indeed fallback...
2025-07-16 22:57:38,939 - live_job_scraper - INFO - Using LinkedIn Jobs as Indeed fallback...
2025-07-16 22:57:38,939 - live_job_scraper - INFO - Generated 3 LinkedIn job fallbacks
2025-07-16 22:57:38,939 - live_job_scraper - INFO - Generated 3 LinkedIn job fallbacks
2025-07-16 22:57:38,939 - live_job_scraper - INFO - Generated 3 LinkedIn job fallbacks
2025-07-16 22:57:38,943 - indian_job_search_manager - INFO - Starting Indian job portal search - 2025-07-16 22:57:38
2025-07-16 22:57:38,943 - indian_job_search_manager - INFO - Starting Indian job portal search - 2025-07-16 22:57:38
2025-07-16 22:57:38,943 - indian_job_scraper - INFO - Starting Indian job portal search - 2025-07-16 22:57:38
2025-07-16 22:57:38,943 - indian_job_scraper - INFO - Starting Indian job portal search - 2025-07-16 22:57:38
2025-07-16 22:57:38,944 - indian_job_scraper - INFO - Searching Internshala...
2025-07-16 22:57:38,944 - indian_job_scraper - INFO - Searching Internshala...
2025-07-16 22:57:40,130 - indian_job_scraper - INFO - Successfully parsed 6 Internshala internships
2025-07-16 22:57:40,130 - indian_job_scraper - INFO - Successfully parsed 6 Internshala internships
2025-07-16 22:57:40,132 - indian_job_scraper - INFO - Found 6 real jobs from Internshala
2025-07-16 22:57:40,132 - indian_job_scraper - INFO - Found 6 real jobs from Internshala
2025-07-16 22:57:42,512 - indian_job_scraper - INFO - Searching Naukri.com...
2025-07-16 22:57:42,512 - indian_job_scraper - INFO - Searching Naukri.com...
2025-07-16 22:57:42,512 - indian_job_scraper - INFO - Generated 4 Naukri.com jobs
2025-07-16 22:57:42,512 - indian_job_scraper - INFO - Generated 4 Naukri.com jobs
2025-07-16 22:57:42,513 - indian_job_scraper - INFO - Found 4 real jobs from Naukri.com
2025-07-16 22:57:42,513 - indian_job_scraper - INFO - Found 4 real jobs from Naukri.com
2025-07-16 22:57:46,250 - indian_job_scraper - INFO - Searching Indeed India...
2025-07-16 22:57:46,250 - indian_job_scraper - INFO - Searching Indeed India...
2025-07-16 22:57:46,804 - indian_job_scraper - INFO - Found 3 real jobs from Indeed India
2025-07-16 22:57:46,804 - indian_job_scraper - INFO - Found 3 real jobs from Indeed India
2025-07-16 22:57:50,591 - indian_job_scraper - INFO - Searching RemoteOK...
2025-07-16 22:57:50,591 - indian_job_scraper - INFO - Searching RemoteOK...
2025-07-16 22:57:53,636 - indian_job_scraper - INFO - Found 2 RemoteOK jobs
2025-07-16 22:57:53,636 - indian_job_scraper - INFO - Found 2 RemoteOK jobs
2025-07-16 22:57:53,638 - indian_job_scraper - INFO - Found 2 real jobs from RemoteOK
2025-07-16 22:57:53,638 - indian_job_scraper - INFO - Found 2 real jobs from RemoteOK
2025-07-16 22:57:56,973 - indian_job_scraper - INFO - Total real jobs found: 15
2025-07-16 22:57:56,973 - indian_job_scraper - INFO - Total real jobs found: 15
2025-07-16 22:57:56,973 - indian_job_search_manager - INFO - Found 15 jobs with verified Indian portal URLs
2025-07-16 22:57:56,973 - indian_job_search_manager - INFO - Found 15 jobs with verified Indian portal URLs
2025-07-16 23:10:57,923 - indian_job_search_manager - INFO - Starting Indian job portal search - 2025-07-16 23:10:57
2025-07-16 23:10:57,923 - indian_job_scraper - INFO - Starting Indian job portal search - 2025-07-16 23:10:57
2025-07-16 23:10:57,923 - indian_job_scraper - INFO - Searching Internshala...
2025-07-16 23:10:59,226 - indian_job_scraper - INFO - Successfully parsed 6 Internshala internships
2025-07-16 23:10:59,226 - indian_job_scraper - INFO - Found 6 real jobs from Internshala
2025-07-16 23:11:02,256 - indian_job_scraper - INFO - Searching Indeed India...
2025-07-16 23:11:02,256 - indian_job_scraper - INFO - Trying Indeed India strategy 1...
2025-07-16 23:11:02,256 - indian_job_scraper - INFO - Searching Indeed India: https://in.indeed.com/jobs?q=UI+UX+designer+intern&l=India&sort=date&fromage=7
2025-07-16 23:11:06,840 - indian_job_scraper - INFO - Searching Indeed India: https://in.indeed.com/jobs?q=graphic+designer+intern&l=India&sort=date&fromage=7
2025-07-16 23:11:10,560 - indian_job_scraper - INFO - Searching Indeed India: https://in.indeed.com/jobs?q=product+designer+intern&l=India&sort=date&fromage=7
2025-07-16 23:11:14,161 - indian_job_scraper - INFO - Trying Indeed India strategy 2...
2025-07-16 23:11:14,691 - indian_job_scraper - INFO - Trying Indeed India strategy 3...
2025-07-16 23:11:15,246 - indian_job_scraper - INFO - All Indeed India strategies failed, using fallback
2025-07-16 23:11:15,247 - indian_job_scraper - INFO - Found 3 real jobs from Indeed India
2025-07-16 23:11:18,179 - indian_job_scraper - INFO - Searching Naukri.com...
2025-07-16 23:11:18,179 - indian_job_scraper - INFO - Attempting to scrape Naukri: https://www.naukri.com/ui-ux-designer-jobs
2025-07-16 23:11:19,245 - indian_job_scraper - INFO - Successfully parsed 0 real Naukri jobs
2025-07-16 23:11:19,246 - indian_job_scraper - INFO - Generated 4 Naukri.com fallback jobs
2025-07-16 23:11:19,246 - indian_job_scraper - INFO - Found 4 real jobs from Naukri.com
2025-07-16 23:11:22,721 - indian_job_scraper - INFO - Total real jobs found: 13
2025-07-16 23:11:22,721 - indian_job_search_manager - INFO - Found 13 jobs with verified Indian portal URLs
2025-07-16 23:11:23,850 - indian_job_scraper - INFO - Successfully parsed 5 Internshala internships
2025-07-16 23:11:23,850 - indian_job_scraper - INFO - Successfully parsed 5 Internshala internships
2025-07-16 23:11:23,856 - indian_job_scraper - INFO - Trying Indeed India strategy 1...
2025-07-16 23:11:23,856 - indian_job_scraper - INFO - Trying Indeed India strategy 1...
2025-07-16 23:11:23,856 - indian_job_scraper - INFO - Trying Indeed India strategy 1...
2025-07-16 23:11:23,857 - indian_job_scraper - INFO - Searching Indeed India: https://in.indeed.com/jobs?q=UI+UX+designer+intern&l=India&sort=date&fromage=7
2025-07-16 23:11:23,857 - indian_job_scraper - INFO - Searching Indeed India: https://in.indeed.com/jobs?q=UI+UX+designer+intern&l=India&sort=date&fromage=7
2025-07-16 23:11:23,857 - indian_job_scraper - INFO - Searching Indeed India: https://in.indeed.com/jobs?q=UI+UX+designer+intern&l=India&sort=date&fromage=7
2025-07-16 23:11:26,629 - indian_job_scraper - INFO - Searching Indeed India: https://in.indeed.com/jobs?q=graphic+designer+intern&l=India&sort=date&fromage=7
2025-07-16 23:11:26,629 - indian_job_scraper - INFO - Searching Indeed India: https://in.indeed.com/jobs?q=graphic+designer+intern&l=India&sort=date&fromage=7
2025-07-16 23:11:26,629 - indian_job_scraper - INFO - Searching Indeed India: https://in.indeed.com/jobs?q=graphic+designer+intern&l=India&sort=date&fromage=7
2025-07-16 23:11:29,281 - indian_job_scraper - INFO - Searching Indeed India: https://in.indeed.com/jobs?q=product+designer+intern&l=India&sort=date&fromage=7
2025-07-16 23:11:29,281 - indian_job_scraper - INFO - Searching Indeed India: https://in.indeed.com/jobs?q=product+designer+intern&l=India&sort=date&fromage=7
2025-07-16 23:11:29,281 - indian_job_scraper - INFO - Searching Indeed India: https://in.indeed.com/jobs?q=product+designer+intern&l=India&sort=date&fromage=7
2025-07-16 23:11:31,935 - indian_job_scraper - INFO - Trying Indeed India strategy 2...
2025-07-16 23:11:31,935 - indian_job_scraper - INFO - Trying Indeed India strategy 2...
2025-07-16 23:11:31,935 - indian_job_scraper - INFO - Trying Indeed India strategy 2...
2025-07-16 23:11:32,599 - indian_job_scraper - INFO - Trying Indeed India strategy 3...
2025-07-16 23:11:32,599 - indian_job_scraper - INFO - Trying Indeed India strategy 3...
2025-07-16 23:11:32,599 - indian_job_scraper - INFO - Trying Indeed India strategy 3...
2025-07-16 23:11:33,113 - indian_job_scraper - INFO - All Indeed India strategies failed, using fallback
2025-07-16 23:11:33,113 - indian_job_scraper - INFO - All Indeed India strategies failed, using fallback
2025-07-16 23:11:33,113 - indian_job_scraper - INFO - All Indeed India strategies failed, using fallback
2025-07-16 23:11:33,117 - indian_job_scraper - INFO - Attempting to scrape Naukri: https://www.naukri.com/ui-ux-designer-jobs
2025-07-16 23:11:33,117 - indian_job_scraper - INFO - Attempting to scrape Naukri: https://www.naukri.com/ui-ux-designer-jobs
2025-07-16 23:11:33,117 - indian_job_scraper - INFO - Attempting to scrape Naukri: https://www.naukri.com/ui-ux-designer-jobs
2025-07-16 23:11:33,117 - indian_job_scraper - INFO - Attempting to scrape Naukri: https://www.naukri.com/ui-ux-designer-jobs
2025-07-16 23:11:34,666 - indian_job_scraper - INFO - Successfully parsed 0 real Naukri jobs
2025-07-16 23:11:34,666 - indian_job_scraper - INFO - Successfully parsed 0 real Naukri jobs
2025-07-16 23:11:34,666 - indian_job_scraper - INFO - Successfully parsed 0 real Naukri jobs
2025-07-16 23:11:34,666 - indian_job_scraper - INFO - Successfully parsed 0 real Naukri jobs
2025-07-16 23:11:34,667 - indian_job_scraper - INFO - Generated 4 Naukri.com fallback jobs
2025-07-16 23:11:34,667 - indian_job_scraper - INFO - Generated 4 Naukri.com fallback jobs
2025-07-16 23:11:34,667 - indian_job_scraper - INFO - Generated 4 Naukri.com fallback jobs
2025-07-16 23:11:34,667 - indian_job_scraper - INFO - Generated 4 Naukri.com fallback jobs
2025-07-16 23:22:17,727 - indian_job_search_manager - INFO - Starting Indian job portal search - 2025-07-16 23:22:17
2025-07-16 23:22:17,728 - indian_job_scraper - INFO - Starting Indian job portal search - 2025-07-16 23:22:17
2025-07-16 23:22:17,728 - indian_job_scraper - INFO - Searching Internshala...
2025-07-16 23:22:18,854 - indian_job_scraper - INFO - Found 45 cards with selector: div.internship_meta
2025-07-16 23:22:20,571 - indian_job_scraper - INFO - Successfully found 8 Internshala jobs
2025-07-16 23:22:20,571 - indian_job_scraper - INFO - Found 8 valid jobs from Internshala
2025-07-16 23:22:24,098 - indian_job_scraper - INFO - Searching Indeed India...
2025-07-16 23:22:32,838 - indian_job_scraper - INFO - Successfully found 0 valid Indeed India jobs
2025-07-16 23:22:32,839 - indian_job_scraper - WARNING - No jobs found from Indeed India
2025-07-16 23:22:36,695 - indian_job_scraper - INFO - Searching Naukri.com...
2025-07-16 23:22:47,290 - indian_job_scraper - INFO - Successfully found 0 valid Naukri jobs
2025-07-16 23:22:47,290 - indian_job_scraper - WARNING - No jobs found from Naukri.com
2025-07-16 23:22:49,494 - indian_job_scraper - INFO - Total valid jobs found: 8
2025-07-16 23:22:49,495 - indian_job_search_manager - INFO - Found 8 jobs with verified Indian portal URLs
2025-07-16 23:22:50,566 - indian_job_scraper - INFO - Found 45 cards with selector: div.internship_meta
2025-07-16 23:22:50,566 - indian_job_scraper - INFO - Found 45 cards with selector: div.internship_meta
2025-07-16 23:22:51,589 - indian_job_scraper - INFO - Successfully found 5 Internshala jobs
2025-07-16 23:22:51,589 - indian_job_scraper - INFO - Successfully found 5 Internshala jobs
2025-07-16 23:23:03,523 - indian_job_scraper - INFO - Successfully found 0 valid Indeed India jobs
2025-07-16 23:23:03,523 - indian_job_scraper - INFO - Successfully found 0 valid Indeed India jobs
2025-07-16 23:23:03,523 - indian_job_scraper - INFO - Successfully found 0 valid Indeed India jobs
2025-07-16 23:23:12,687 - indian_job_scraper - INFO - Successfully found 0 valid Naukri jobs
2025-07-16 23:23:12,687 - indian_job_scraper - INFO - Successfully found 0 valid Naukri jobs
2025-07-16 23:23:12,687 - indian_job_scraper - INFO - Successfully found 0 valid Naukri jobs
2025-07-16 23:23:12,687 - indian_job_scraper - INFO - Successfully found 0 valid Naukri jobs
2025-07-16 23:33:42,641 - indian_job_search_manager - INFO - Starting Indian job portal search - 2025-07-16 23:33:42
2025-07-16 23:33:42,642 - indian_job_scraper - INFO - Starting Indian job portal search - 2025-07-16 23:33:42
2025-07-16 23:33:42,642 - indian_job_search_manager - ERROR - Error in Indian job search: 'IndianJobScraper' object has no attribute '_search_indeed_india_serp'
2025-07-16 23:39:59,780 - indian_job_search_manager - INFO - Starting Indian job portal search - 2025-07-16 23:39:59
2025-07-16 23:39:59,780 - indian_job_scraper - INFO - Starting Indian job portal search - 2025-07-16 23:39:59
2025-07-16 23:39:59,781 - indian_job_scraper - INFO - Searching Indeed India...
2025-07-16 23:39:59,781 - indian_job_scraper - INFO - Searching Indeed India with SerpAPI: ui ux designer jobs in india site:in.indeed.com
2025-07-16 23:40:03,016 - indian_job_scraper - INFO - SerpAPI returned 6 results for Indeed India
2025-07-16 23:40:04,018 - indian_job_scraper - INFO - Found 6 jobs from Indeed India via SerpAPI
2025-07-16 23:40:04,019 - indian_job_scraper - INFO - Found 6 valid jobs from Indeed India
2025-07-16 23:40:07,267 - indian_job_scraper - INFO - Searching Naukri.com...
2025-07-16 23:40:07,267 - indian_job_scraper - INFO - Searching Naukri with SerpAPI: ui ux designer jobs site:naukri.com
2025-07-16 23:40:10,415 - indian_job_scraper - INFO - SerpAPI returned 5 results for Naukri
2025-07-16 23:40:11,417 - indian_job_scraper - INFO - Found 5 jobs from Naukri via SerpAPI
2025-07-16 23:40:11,418 - indian_job_scraper - INFO - Found 5 valid jobs from Naukri.com
2025-07-16 23:40:15,287 - indian_job_scraper - INFO - Searching Internshala...
2025-07-16 23:40:15,287 - indian_job_scraper - INFO - Searching Internshala with SerpAPI: ui ux design internship site:internshala.com
2025-07-16 23:40:19,907 - indian_job_scraper - INFO - SerpAPI returned 4 results for Internshala
2025-07-16 23:40:20,909 - indian_job_scraper - INFO - Found 4 jobs from Internshala via SerpAPI
2025-07-16 23:40:20,909 - indian_job_scraper - INFO - Found 4 valid jobs from Internshala
2025-07-16 23:40:23,576 - indian_job_scraper - INFO - Total valid jobs found: 15
2025-07-16 23:40:23,576 - indian_job_search_manager - WARNING - Filtered out malformed URL: https://in.indeed.com/q-ux-ui-designer-jobs.html
2025-07-16 23:40:23,576 - indian_job_search_manager - WARNING - Filtered out malformed URL: https://in.indeed.com/q-freelance-ux-ui-designer-jobs.html
2025-07-16 23:40:23,576 - indian_job_search_manager - WARNING - Filtered out malformed URL: https://in.indeed.com/q-ux-ui-designer-10-year-experience-jobs.html
2025-07-16 23:40:23,577 - indian_job_search_manager - WARNING - Filtered out malformed URL: https://www.naukri.com/ux-designer-jobs-in-abroad
2025-07-16 23:40:23,577 - indian_job_search_manager - WARNING - Filtered out malformed URL: https://in.indeed.com/q-tech-mahindra,-ui-ux-designer-jobs.html
2025-07-16 23:40:23,577 - indian_job_search_manager - WARNING - Filtered out malformed URL: https://www.naukri.com/ui-ux-designer-jobs-in-india-remote
2025-07-16 23:40:23,577 - indian_job_search_manager - WARNING - Filtered out malformed URL: https://in.indeed.com/q-ui-ux-designer-2-years-experience-jobs.html
2025-07-16 23:40:23,578 - indian_job_search_manager - WARNING - Filtered out malformed URL: https://in.indeed.com/q-ui-ux-designer,infosys-jobs.html
2025-07-16 23:40:23,578 - indian_job_search_manager - WARNING - Filtered out malformed URL: https://www.naukri.com/ui-ux-designer-jobs
2025-07-16 23:40:23,578 - indian_job_search_manager - WARNING - Filtered out malformed URL: https://www.naukri.com/senior-ui-ux-designer-jobs
2025-07-16 23:40:23,578 - indian_job_search_manager - WARNING - Filtered out malformed URL: https://www.naukri.com/user-experience-designer-jobs-in-usa
2025-07-16 23:40:23,579 - indian_job_search_manager - INFO - Found 4 jobs with verified Indian portal URLs
2025-07-16 23:40:23,582 - indian_job_scraper - INFO - Searching Internshala with SerpAPI: ui ux design internship site:internshala.com
2025-07-16 23:40:23,582 - indian_job_scraper - INFO - Searching Internshala with SerpAPI: ui ux design internship site:internshala.com
2025-07-16 23:40:28,936 - indian_job_scraper - INFO - SerpAPI returned 3 results for Internshala
2025-07-16 23:40:28,936 - indian_job_scraper - INFO - SerpAPI returned 3 results for Internshala
2025-07-16 23:40:29,937 - indian_job_scraper - INFO - Found 3 jobs from Internshala via SerpAPI
2025-07-16 23:40:29,937 - indian_job_scraper - INFO - Found 3 jobs from Internshala via SerpAPI
2025-07-16 23:40:29,941 - indian_job_scraper - INFO - Searching Indeed India with SerpAPI: ui ux designer jobs in india site:in.indeed.com
2025-07-16 23:40:29,941 - indian_job_scraper - INFO - Searching Indeed India with SerpAPI: ui ux designer jobs in india site:in.indeed.com
2025-07-16 23:40:29,941 - indian_job_scraper - INFO - Searching Indeed India with SerpAPI: ui ux designer jobs in india site:in.indeed.com
2025-07-16 23:40:32,470 - indian_job_scraper - INFO - SerpAPI returned 3 results for Indeed India
2025-07-16 23:40:32,470 - indian_job_scraper - INFO - SerpAPI returned 3 results for Indeed India
2025-07-16 23:40:32,470 - indian_job_scraper - INFO - SerpAPI returned 3 results for Indeed India
2025-07-16 23:40:33,472 - indian_job_scraper - INFO - Found 3 jobs from Indeed India via SerpAPI
2025-07-16 23:40:33,472 - indian_job_scraper - INFO - Found 3 jobs from Indeed India via SerpAPI
2025-07-16 23:40:33,472 - indian_job_scraper - INFO - Found 3 jobs from Indeed India via SerpAPI
2025-07-16 23:40:33,476 - indian_job_scraper - INFO - Searching Naukri with SerpAPI: ui ux designer jobs site:naukri.com
2025-07-16 23:40:33,476 - indian_job_scraper - INFO - Searching Naukri with SerpAPI: ui ux designer jobs site:naukri.com
2025-07-16 23:40:33,476 - indian_job_scraper - INFO - Searching Naukri with SerpAPI: ui ux designer jobs site:naukri.com
2025-07-16 23:40:33,476 - indian_job_scraper - INFO - Searching Naukri with SerpAPI: ui ux designer jobs site:naukri.com
2025-07-16 23:40:35,332 - indian_job_scraper - INFO - SerpAPI returned 3 results for Naukri
2025-07-16 23:40:35,332 - indian_job_scraper - INFO - SerpAPI returned 3 results for Naukri
2025-07-16 23:40:35,332 - indian_job_scraper - INFO - SerpAPI returned 3 results for Naukri
2025-07-16 23:40:35,332 - indian_job_scraper - INFO - SerpAPI returned 3 results for Naukri
2025-07-16 23:40:36,334 - indian_job_scraper - INFO - Found 3 jobs from Naukri via SerpAPI
2025-07-16 23:40:36,334 - indian_job_scraper - INFO - Found 3 jobs from Naukri via SerpAPI
2025-07-16 23:40:36,334 - indian_job_scraper - INFO - Found 3 jobs from Naukri via SerpAPI
2025-07-16 23:40:36,334 - indian_job_scraper - INFO - Found 3 jobs from Naukri via SerpAPI
2025-07-16 23:41:09,400 - indian_job_scraper - INFO - Searching Indeed India with SerpAPI: ui ux designer jobs in india site:in.indeed.com
2025-07-16 23:41:09,400 - indian_job_scraper - INFO - Searching Indeed India with SerpAPI: ui ux designer jobs in india site:in.indeed.com
2025-07-16 23:41:12,823 - indian_job_scraper - INFO - SerpAPI returned 1 results for Indeed India
2025-07-16 23:41:12,823 - indian_job_scraper - INFO - SerpAPI returned 1 results for Indeed India
2025-07-16 23:41:13,825 - indian_job_scraper - INFO - Found 1 jobs from Indeed India via SerpAPI
2025-07-16 23:41:13,825 - indian_job_scraper - INFO - Found 1 jobs from Indeed India via SerpAPI
2025-07-16 23:41:40,456 - indian_job_scraper - INFO - Searching Indeed India with SerpAPI: ui ux designer jobs in india site:in.indeed.com
2025-07-16 23:41:42,008 - indian_job_scraper - INFO - SerpAPI returned 2 results for Indeed India
2025-07-16 23:41:43,010 - indian_job_scraper - INFO - Found 2 jobs from Indeed India via SerpAPI
2025-07-16 23:46:14,816 - indian_job_search_manager - INFO - Starting Indian job portal search - 2025-07-16 23:46:14
2025-07-16 23:46:14,816 - indian_job_scraper - INFO - Starting Indian job portal search - 2025-07-16 23:46:14
2025-07-16 23:46:14,818 - indian_job_scraper - INFO - Searching Indeed India...
2025-07-16 23:46:14,818 - indian_job_scraper - INFO - Searching Indeed India with SerpAPI: ui ux designer jobs in india site:in.indeed.com
2025-07-16 23:46:15,447 - indian_job_scraper - INFO - SerpAPI returned 6 results for Indeed India
2025-07-16 23:46:16,450 - indian_job_scraper - INFO - Found 6 jobs from Indeed India via SerpAPI
2025-07-16 23:46:16,452 - indian_job_scraper - INFO - Found 6 valid jobs from Indeed India
2025-07-16 23:46:19,647 - indian_job_scraper - INFO - Searching Naukri.com...
2025-07-16 23:46:19,647 - indian_job_scraper - INFO - Searching Naukri with SerpAPI: ui ux designer jobs site:naukri.com
2025-07-16 23:46:21,374 - indian_job_scraper - INFO - SerpAPI returned 5 results for Naukri
2025-07-16 23:46:22,377 - indian_job_scraper - INFO - Found 5 jobs from Naukri via SerpAPI
2025-07-16 23:46:22,378 - indian_job_scraper - INFO - Found 5 valid jobs from Naukri.com
2025-07-16 23:46:25,542 - indian_job_scraper - INFO - Searching Internshala...
2025-07-16 23:46:25,542 - indian_job_scraper - INFO - Searching Internshala with SerpAPI: ui ux design internship site:internshala.com
2025-07-16 23:46:26,172 - indian_job_scraper - INFO - SerpAPI returned 4 results for Internshala
2025-07-16 23:46:27,173 - indian_job_scraper - INFO - Found 4 jobs from Internshala via SerpAPI
2025-07-16 23:46:27,175 - indian_job_scraper - INFO - Found 4 valid jobs from Internshala
2025-07-16 23:46:31,089 - indian_job_scraper - INFO - Total valid jobs found: 15
2025-07-16 23:46:31,089 - indian_job_search_manager - WARNING - Filtered out malformed URL: https://in.indeed.com/q-ui-ux-designer,infosys-jobs.html
2025-07-16 23:46:31,089 - indian_job_search_manager - WARNING - Filtered out malformed URL: https://in.indeed.com/q-ux-ui-designer-10-year-experience-jobs.html
2025-07-16 23:46:31,089 - indian_job_search_manager - WARNING - Filtered out malformed URL: https://in.indeed.com/q-ux-ui-designer-jobs.html
2025-07-16 23:46:31,089 - indian_job_search_manager - WARNING - Filtered out malformed URL: https://in.indeed.com/q-freelance-ux-ui-designer-jobs.html
2025-07-16 23:46:31,091 - indian_job_search_manager - WARNING - Filtered out malformed URL: https://in.indeed.com/q-tech-mahindra,-ui-ux-designer-jobs.html
2025-07-16 23:46:31,091 - indian_job_search_manager - WARNING - Filtered out malformed URL: https://www.naukri.com/ui-ux-designer-jobs
2025-07-16 23:46:31,091 - indian_job_search_manager - WARNING - Filtered out malformed URL: https://www.naukri.com/ux-designer-jobs-in-abroad
2025-07-16 23:46:31,091 - indian_job_search_manager - WARNING - Filtered out malformed URL: https://in.indeed.com/q-ui-ux-designer-2-years-experience-jobs.html
2025-07-16 23:46:31,091 - indian_job_search_manager - WARNING - Filtered out malformed URL: https://www.naukri.com/ui-ux-designer-jobs-in-india-remote
2025-07-16 23:46:31,092 - indian_job_search_manager - WARNING - Filtered out malformed URL: https://www.naukri.com/senior-ui-ux-designer-jobs
2025-07-16 23:46:31,092 - indian_job_search_manager - WARNING - Filtered out malformed URL: https://www.naukri.com/user-experience-designer-jobs-in-usa
2025-07-16 23:46:31,092 - indian_job_search_manager - INFO - Found 4 jobs with verified Indian portal URLs
2025-07-16 23:46:31,096 - indian_job_scraper - INFO - Searching Internshala with SerpAPI: ui ux design internship site:internshala.com
2025-07-16 23:46:31,096 - indian_job_scraper - INFO - Searching Internshala with SerpAPI: ui ux design internship site:internshala.com
2025-07-16 23:46:31,713 - indian_job_scraper - INFO - SerpAPI returned 3 results for Internshala
2025-07-16 23:46:31,713 - indian_job_scraper - INFO - SerpAPI returned 3 results for Internshala
2025-07-16 23:46:32,715 - indian_job_scraper - INFO - Found 3 jobs from Internshala via SerpAPI
2025-07-16 23:46:32,715 - indian_job_scraper - INFO - Found 3 jobs from Internshala via SerpAPI
2025-07-16 23:46:32,720 - indian_job_scraper - INFO - Searching Indeed India with SerpAPI: ui ux designer jobs in india site:in.indeed.com
2025-07-16 23:46:32,720 - indian_job_scraper - INFO - Searching Indeed India with SerpAPI: ui ux designer jobs in india site:in.indeed.com
2025-07-16 23:46:32,720 - indian_job_scraper - INFO - Searching Indeed India with SerpAPI: ui ux designer jobs in india site:in.indeed.com
2025-07-16 23:46:33,355 - indian_job_scraper - INFO - SerpAPI returned 3 results for Indeed India
2025-07-16 23:46:33,355 - indian_job_scraper - INFO - SerpAPI returned 3 results for Indeed India
2025-07-16 23:46:33,355 - indian_job_scraper - INFO - SerpAPI returned 3 results for Indeed India
2025-07-16 23:46:34,357 - indian_job_scraper - INFO - Found 3 jobs from Indeed India via SerpAPI
2025-07-16 23:46:34,357 - indian_job_scraper - INFO - Found 3 jobs from Indeed India via SerpAPI
2025-07-16 23:46:34,357 - indian_job_scraper - INFO - Found 3 jobs from Indeed India via SerpAPI
2025-07-16 23:46:34,363 - indian_job_scraper - INFO - Searching Naukri with SerpAPI: ui ux designer jobs site:naukri.com
2025-07-16 23:46:34,363 - indian_job_scraper - INFO - Searching Naukri with SerpAPI: ui ux designer jobs site:naukri.com
2025-07-16 23:46:34,363 - indian_job_scraper - INFO - Searching Naukri with SerpAPI: ui ux designer jobs site:naukri.com
2025-07-16 23:46:34,363 - indian_job_scraper - INFO - Searching Naukri with SerpAPI: ui ux designer jobs site:naukri.com
2025-07-16 23:46:34,978 - indian_job_scraper - INFO - SerpAPI returned 3 results for Naukri
2025-07-16 23:46:34,978 - indian_job_scraper - INFO - SerpAPI returned 3 results for Naukri
2025-07-16 23:46:34,978 - indian_job_scraper - INFO - SerpAPI returned 3 results for Naukri
2025-07-16 23:46:34,978 - indian_job_scraper - INFO - SerpAPI returned 3 results for Naukri
2025-07-16 23:46:35,980 - indian_job_scraper - INFO - Found 3 jobs from Naukri via SerpAPI
2025-07-16 23:46:35,980 - indian_job_scraper - INFO - Found 3 jobs from Naukri via SerpAPI
2025-07-16 23:46:35,980 - indian_job_scraper - INFO - Found 3 jobs from Naukri via SerpAPI
2025-07-16 23:46:35,980 - indian_job_scraper - INFO - Found 3 jobs from Naukri via SerpAPI
2025-07-16 23:52:43,740 - indian_job_scraper - INFO - Searching Indeed India with SerpAPI: site:in.indeed.com/viewjob ui ux designer
2025-07-16 23:52:45,795 - indian_job_scraper - INFO - SerpAPI returned 3 results for Indeed India
2025-07-16 23:52:46,797 - indian_job_scraper - INFO - Found 3 jobs from Indeed India via SerpAPI
2025-07-16 23:52:46,798 - indian_job_scraper - INFO - Searching Naukri with SerpAPI: site:naukri.com/job-listings ui ux designer
2025-07-16 23:52:47,752 - indian_job_search_manager - INFO - Starting Indian job portal search - 2025-07-16 23:52:47
2025-07-16 23:52:47,752 - indian_job_scraper - INFO - Starting Indian job portal search - 2025-07-16 23:52:47
2025-07-16 23:52:47,752 - indian_job_scraper - INFO - Searching Indeed India...
2025-07-16 23:52:47,752 - indian_job_scraper - INFO - Searching Indeed India with SerpAPI: site:in.indeed.com/viewjob ui ux designer
2025-07-16 23:52:48,536 - indian_job_scraper - INFO - SerpAPI returned 3 results for Naukri
2025-07-16 23:52:49,537 - indian_job_scraper - INFO - Found 3 jobs from Naukri via SerpAPI
2025-07-16 23:52:49,539 - indian_job_scraper - INFO - Searching Internshala with SerpAPI: site:internshala.com/internship/detail ui ux design
2025-07-16 23:52:50,946 - indian_job_scraper - INFO - SerpAPI returned 6 results for Indeed India
2025-07-16 23:52:51,491 - indian_job_scraper - INFO - SerpAPI returned 3 results for Internshala
2025-07-16 23:52:51,949 - indian_job_scraper - INFO - Found 6 jobs from Indeed India via SerpAPI
2025-07-16 23:52:51,950 - indian_job_scraper - INFO - Found 6 valid jobs from Indeed India
2025-07-16 23:52:52,492 - indian_job_scraper - INFO - Found 3 jobs from Internshala via SerpAPI
2025-07-16 23:52:55,079 - indian_job_scraper - INFO - Searching Naukri.com...
2025-07-16 23:52:55,079 - indian_job_scraper - INFO - Searching Naukri with SerpAPI: site:naukri.com/job-listings ui ux designer
2025-07-16 23:52:56,661 - indian_job_scraper - INFO - SerpAPI returned 5 results for Naukri
2025-07-16 23:52:57,663 - indian_job_scraper - INFO - Found 5 jobs from Naukri via SerpAPI
2025-07-16 23:52:57,665 - indian_job_scraper - INFO - Found 5 valid jobs from Naukri.com
2025-07-16 23:53:00,678 - indian_job_scraper - INFO - Searching Internshala...
2025-07-16 23:53:00,678 - indian_job_scraper - INFO - Searching Internshala with SerpAPI: site:internshala.com/internship/detail ui ux design
2025-07-16 23:53:02,982 - indian_job_scraper - INFO - SerpAPI returned 4 results for Internshala
2025-07-16 23:53:03,983 - indian_job_scraper - INFO - Found 4 jobs from Internshala via SerpAPI
2025-07-16 23:53:03,986 - indian_job_scraper - INFO - Found 4 valid jobs from Internshala
2025-07-16 23:53:06,493 - indian_job_scraper - INFO - Total valid jobs found: 15
2025-07-16 23:53:06,493 - indian_job_search_manager - INFO - Found 15 jobs with verified Indian portal URLs
2025-07-16 23:53:06,505 - indian_job_scraper - INFO - Searching Internshala with SerpAPI: site:internshala.com/internship/detail ui ux design
2025-07-16 23:53:06,505 - indian_job_scraper - INFO - Searching Internshala with SerpAPI: site:internshala.com/internship/detail ui ux design
2025-07-16 23:53:07,150 - indian_job_scraper - INFO - SerpAPI returned 3 results for Internshala
2025-07-16 23:53:07,150 - indian_job_scraper - INFO - SerpAPI returned 3 results for Internshala
2025-07-16 23:53:08,152 - indian_job_scraper - INFO - Found 3 jobs from Internshala via SerpAPI
2025-07-16 23:53:08,152 - indian_job_scraper - INFO - Found 3 jobs from Internshala via SerpAPI
2025-07-16 23:53:08,156 - indian_job_scraper - INFO - Searching Indeed India with SerpAPI: site:in.indeed.com/viewjob ui ux designer
2025-07-16 23:53:08,156 - indian_job_scraper - INFO - Searching Indeed India with SerpAPI: site:in.indeed.com/viewjob ui ux designer
2025-07-16 23:53:08,156 - indian_job_scraper - INFO - Searching Indeed India with SerpAPI: site:in.indeed.com/viewjob ui ux designer
2025-07-16 23:53:08,765 - indian_job_scraper - INFO - SerpAPI returned 3 results for Indeed India
2025-07-16 23:53:08,765 - indian_job_scraper - INFO - SerpAPI returned 3 results for Indeed India
2025-07-16 23:53:08,765 - indian_job_scraper - INFO - SerpAPI returned 3 results for Indeed India
2025-07-16 23:53:09,767 - indian_job_scraper - INFO - Found 3 jobs from Indeed India via SerpAPI
2025-07-16 23:53:09,767 - indian_job_scraper - INFO - Found 3 jobs from Indeed India via SerpAPI
2025-07-16 23:53:09,767 - indian_job_scraper - INFO - Found 3 jobs from Indeed India via SerpAPI
2025-07-16 23:53:09,772 - indian_job_scraper - INFO - Searching Naukri with SerpAPI: site:naukri.com/job-listings ui ux designer
2025-07-16 23:53:09,772 - indian_job_scraper - INFO - Searching Naukri with SerpAPI: site:naukri.com/job-listings ui ux designer
2025-07-16 23:53:09,772 - indian_job_scraper - INFO - Searching Naukri with SerpAPI: site:naukri.com/job-listings ui ux designer
2025-07-16 23:53:09,772 - indian_job_scraper - INFO - Searching Naukri with SerpAPI: site:naukri.com/job-listings ui ux designer
2025-07-16 23:53:10,402 - indian_job_scraper - INFO - SerpAPI returned 3 results for Naukri
2025-07-16 23:53:10,402 - indian_job_scraper - INFO - SerpAPI returned 3 results for Naukri
2025-07-16 23:53:10,402 - indian_job_scraper - INFO - SerpAPI returned 3 results for Naukri
2025-07-16 23:53:10,402 - indian_job_scraper - INFO - SerpAPI returned 3 results for Naukri
2025-07-16 23:53:11,403 - indian_job_scraper - INFO - Found 3 jobs from Naukri via SerpAPI
2025-07-16 23:53:11,403 - indian_job_scraper - INFO - Found 3 jobs from Naukri via SerpAPI
2025-07-16 23:53:11,403 - indian_job_scraper - INFO - Found 3 jobs from Naukri via SerpAPI
2025-07-16 23:53:11,403 - indian_job_scraper - INFO - Found 3 jobs from Naukri via SerpAPI
2025-07-17 00:01:58,176 - api_server - INFO - Starting Job AI API Server...
2025-07-17 00:02:01,029 - api_server - INFO - Starting Job AI API Server...
2025-07-17 00:02:27,145 - api_server - INFO - Database returned 0 jobs
2025-07-17 00:02:27,146 - api_server - INFO - API returning 0 jobs
2025-07-17 00:02:27,211 - api_server - INFO - Database returned 0 jobs
2025-07-17 00:02:27,213 - api_server - INFO - API returning 0 jobs
2025-07-17 00:02:43,457 - job_search_manager - INFO - Starting job search...
2025-07-17 00:02:43,457 - indian_job_search_manager - INFO - Starting Indian job portal search - 2025-07-17 00:02:43
2025-07-17 00:02:43,457 - indian_job_scraper - INFO - Starting Indian job portal search - 2025-07-17 00:02:43
2025-07-17 00:02:43,458 - indian_job_scraper - INFO - Searching Indeed India...
2025-07-17 00:02:43,458 - indian_job_scraper - INFO - Searching Indeed India with SerpAPI: site:in.indeed.com/viewjob ui ux designer
2025-07-17 00:02:44,230 - indian_job_scraper - INFO - SerpAPI returned 6 results for Indeed India
2025-07-17 00:02:45,233 - indian_job_scraper - INFO - Found 6 jobs from Indeed India via SerpAPI
2025-07-17 00:02:45,236 - indian_job_scraper - INFO - Found 6 valid jobs from Indeed India
2025-07-17 00:02:48,800 - indian_job_scraper - INFO - Searching Naukri.com...
2025-07-17 00:02:48,800 - indian_job_scraper - INFO - Searching Naukri with SerpAPI: site:naukri.com/job-listings ui ux designer
2025-07-17 00:02:49,519 - indian_job_scraper - INFO - SerpAPI returned 5 results for Naukri
2025-07-17 00:02:50,521 - indian_job_scraper - INFO - Found 5 jobs from Naukri via SerpAPI
2025-07-17 00:02:50,522 - indian_job_scraper - INFO - Found 5 valid jobs from Naukri.com
2025-07-17 00:02:53,755 - indian_job_scraper - INFO - Searching Internshala...
2025-07-17 00:02:53,756 - indian_job_scraper - INFO - Searching Internshala with SerpAPI: site:internshala.com/internship/detail ui ux design
2025-07-17 00:02:54,408 - indian_job_scraper - INFO - SerpAPI returned 4 results for Internshala
2025-07-17 00:02:55,410 - indian_job_scraper - INFO - Found 4 jobs from Internshala via SerpAPI
2025-07-17 00:02:55,412 - indian_job_scraper - INFO - Found 4 valid jobs from Internshala
2025-07-17 00:02:58,210 - indian_job_scraper - INFO - Total valid jobs found: 15
2025-07-17 00:02:58,211 - indian_job_search_manager - INFO - Found 15 jobs with verified Indian portal URLs
2025-07-17 00:02:58,393 - job_search_manager - INFO - Indian job search completed. Found 15 jobs, saved 15 to database.
2025-07-17 00:02:58,394 - api_server - INFO - Indian job search completed. Found 15 jobs from Indian portals.
2025-07-17 00:02:59,519 - api_server - INFO - Database returned 15 jobs
2025-07-17 00:02:59,520 - api_server - INFO - API returning 15 jobs
2025-07-17 00:02:59,520 - api_server - INFO - Job 1: UI/UX Design Work From Home Part Time Internship - Company
2025-07-17 00:02:59,521 - api_server - INFO - Job 2: UI/UX Design Work From Home Internship at HighScores - Company
2025-07-17 00:02:59,521 - api_server - INFO - Job 3: UI/UX Design work from home job/internship at UNIVOC - Univoc As A Ui/Ux Design Intern At Univoc
2025-07-17 00:03:13,129 - api_server - INFO - Starting auto-apply process for job: UI/UX Design Work From Home Part Time Internship at Company
2025-07-17 00:03:20,135 - resume_generator - ERROR - Error customizing resume: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 39
}
]
2025-07-17 00:03:25,142 - job_applier - INFO - Starting auto-apply for UI/UX Design Work From Home Part Time Internship at Company
2025-07-17 00:03:26,485 - job_applier - INFO - Navigating to: https://internshala.com/internship/detail/work-from-home-part-time-ui-ux-design-internship-at-admybrandcom1752483791
2025-07-17 00:03:38,323 - job_applier - ERROR - Error analyzing form: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 21
}
]
2025-07-17 00:06:05,141 - api_server - INFO - Analyzing job: UI/UX Design Work From Home Part Time Internship at Company
2025-07-17 00:06:06,810 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 52
}
]
2025-07-17 00:06:06,810 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 52
}
]
2025-07-17 00:06:14,853 - api_server - INFO - Analyzing job: UI/UX Design Work From Home Internship at HighScores at Company
2025-07-17 00:06:16,642 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 43
}
]
2025-07-17 00:06:16,642 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 43
}
]
2025-07-17 00:06:16,642 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 43
}
]
2025-07-17 00:06:20,421 - api_server - INFO - Analyzing job: UI/UX Designer - Remote at Company
2025-07-17 00:06:22,087 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-07-17 00:06:22,087 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-07-17 00:06:22,087 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-07-17 00:06:22,087 - job_analyzer - ERROR - Error analyzing job match: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash-exp"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-07-17 00:09:43,613 - api_server - INFO - Starting Job AI API Server...
2025-07-17 00:10:12,880 - api_server - INFO - Starting Job AI API Server...
2025-07-17 00:11:23,653 - api_server - INFO - Starting Job AI API Server...
2025-07-17 00:11:39,747 - api_server - INFO - Starting Job AI API Server...
2025-07-17 00:11:54,873 - api_server - INFO - Starting Job AI API Server...
2025-07-17 00:12:08,987 - api_server - INFO - Starting Job AI API Server...
2025-07-17 00:13:19,004 - indian_job_search_manager - INFO - Starting Indian job portal search - 2025-07-17 00:13:19
2025-07-17 00:13:19,004 - indian_job_scraper - INFO - Starting Indian job portal search - 2025-07-17 00:13:19
2025-07-17 00:13:19,005 - indian_job_scraper - INFO - Searching Indeed India...
2025-07-17 00:13:19,005 - indian_job_scraper - INFO - Searching Indeed India with SerpAPI: site:in.indeed.com/viewjob ui ux designer
2025-07-17 00:13:19,706 - indian_job_scraper - INFO - SerpAPI returned 6 results for Indeed India
2025-07-17 00:13:19,711 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:19,713 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:19,714 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:19,715 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:19,715 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:19,716 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:20,717 - indian_job_scraper - INFO - Searching Indeed India with SerpAPI: site:in.indeed.com/viewjob graphic designer india
2025-07-17 00:13:28,969 - indian_job_scraper - INFO - SerpAPI returned 6 results for Indeed India
2025-07-17 00:13:28,970 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:28,971 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:28,972 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:28,972 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:28,973 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:28,973 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:29,974 - indian_job_scraper - INFO - Searching Indeed India with SerpAPI: site:in.indeed.com/viewjob product designer bangalore
2025-07-17 00:13:36,652 - indian_job_scraper - INFO - SerpAPI returned 6 results for Indeed India
2025-07-17 00:13:36,653 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:36,654 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:36,654 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:36,655 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:36,655 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:36,656 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:37,658 - indian_job_scraper - INFO - Searching Indeed India with SerpAPI: site:in.indeed.com/viewjob design intern
2025-07-17 00:13:40,525 - indian_job_scraper - INFO - SerpAPI returned 6 results for Indeed India
2025-07-17 00:13:40,526 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:40,527 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:40,527 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:40,529 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:40,530 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:40,531 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:41,532 - indian_job_scraper - INFO - Found 0 jobs from Indeed India via SerpAPI
2025-07-17 00:13:41,534 - indian_job_scraper - WARNING - No jobs found from Indeed India
2025-07-17 00:13:44,446 - indian_job_scraper - INFO - Searching Naukri.com...
2025-07-17 00:13:44,446 - indian_job_scraper - INFO - Searching Naukri with SerpAPI: site:naukri.com/job-listings ui ux designer
2025-07-17 00:13:45,010 - indian_job_scraper - INFO - SerpAPI returned 5 results for Naukri
2025-07-17 00:13:45,011 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:45,011 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:45,012 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:45,012 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:45,013 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:46,014 - indian_job_scraper - INFO - Searching Naukri with SerpAPI: site:naukri.com/job-listings graphic designer
2025-07-17 00:13:48,115 - indian_job_scraper - INFO - SerpAPI returned 4 results for Naukri
2025-07-17 00:13:48,115 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:48,116 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:48,117 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:48,117 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:49,118 - indian_job_scraper - INFO - Searching Naukri with SerpAPI: site:naukri.com/job-listings product designer india
2025-07-17 00:13:53,291 - indian_job_scraper - INFO - SerpAPI returned 5 results for Naukri
2025-07-17 00:13:53,292 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:53,293 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:53,293 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:53,294 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:53,294 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:54,295 - indian_job_scraper - INFO - Searching Naukri with SerpAPI: site:naukri.com inurl:job-listings design
2025-07-17 00:13:56,528 - indian_job_scraper - INFO - SerpAPI returned 5 results for Naukri
2025-07-17 00:13:56,528 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:56,529 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:56,529 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:56,530 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:56,530 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:13:57,532 - indian_job_scraper - INFO - Found 0 jobs from Naukri via SerpAPI
2025-07-17 00:13:57,534 - indian_job_scraper - WARNING - No jobs found from Naukri.com
2025-07-17 00:14:00,125 - indian_job_scraper - INFO - Searching Internshala...
2025-07-17 00:14:00,125 - indian_job_scraper - INFO - Searching Internshala with SerpAPI: site:internshala.com/internship/detail ui ux design
2025-07-17 00:14:00,694 - indian_job_scraper - INFO - SerpAPI returned 4 results for Internshala
2025-07-17 00:14:00,695 - indian_job_scraper - WARNING - Error parsing Internshala SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:14:00,696 - indian_job_scraper - WARNING - Error parsing Internshala SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:14:00,696 - indian_job_scraper - WARNING - Error parsing Internshala SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:14:00,696 - indian_job_scraper - WARNING - Error parsing Internshala SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:14:01,697 - indian_job_scraper - INFO - Searching Internshala with SerpAPI: site:internshala.com/internship/detail graphic design
2025-07-17 00:14:04,722 - indian_job_scraper - INFO - SerpAPI returned 4 results for Internshala
2025-07-17 00:14:04,724 - indian_job_scraper - WARNING - Error parsing Internshala SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:14:04,724 - indian_job_scraper - WARNING - Error parsing Internshala SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:14:04,724 - indian_job_scraper - WARNING - Error parsing Internshala SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:14:04,725 - indian_job_scraper - WARNING - Error parsing Internshala SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:14:05,727 - indian_job_scraper - INFO - Searching Internshala with SerpAPI: site:internshala.com inurl:internship/detail design
2025-07-17 00:14:07,676 - indian_job_scraper - INFO - SerpAPI returned 4 results for Internshala
2025-07-17 00:14:07,677 - indian_job_scraper - WARNING - Error parsing Internshala SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:14:07,677 - indian_job_scraper - WARNING - Error parsing Internshala SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:14:07,678 - indian_job_scraper - WARNING - Error parsing Internshala SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:14:07,678 - indian_job_scraper - WARNING - Error parsing Internshala SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:14:08,680 - indian_job_scraper - INFO - Found 0 jobs from Internshala via SerpAPI
2025-07-17 00:14:08,682 - indian_job_scraper - WARNING - No jobs found from Internshala
2025-07-17 00:14:11,213 - indian_job_scraper - INFO - Total valid jobs found: 0
2025-07-17 00:14:11,213 - indian_job_search_manager - INFO - Found 0 jobs with verified Indian portal URLs
2025-07-17 00:19:10,416 - api_server - INFO - Starting Job AI API Server...
2025-07-17 00:19:13,591 - api_server - INFO - Starting Job AI API Server...
2025-07-17 00:19:15,227 - api_server - INFO - Cleared 0 jobs from database. 0 jobs remaining.
2025-07-17 00:19:22,815 - job_search_manager - INFO - Starting job search...
2025-07-17 00:19:22,819 - indian_job_search_manager - INFO - Starting Indian job portal search - 2025-07-17 00:19:22
2025-07-17 00:19:22,820 - indian_job_scraper - INFO - Starting Indian job portal search - 2025-07-17 00:19:22
2025-07-17 00:19:22,821 - indian_job_scraper - INFO - Searching Indeed India...
2025-07-17 00:19:22,821 - indian_job_scraper - INFO - Searching Indeed India with SerpAPI: site:in.indeed.com/viewjob ui ux designer
2025-07-17 00:19:23,701 - indian_job_scraper - INFO - SerpAPI returned 6 results for Indeed India
2025-07-17 00:19:23,705 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:23,709 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:23,710 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:23,711 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:23,712 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:23,713 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:24,713 - indian_job_scraper - INFO - Searching Indeed India with SerpAPI: site:in.indeed.com/viewjob graphic designer india
2025-07-17 00:19:25,430 - indian_job_scraper - INFO - SerpAPI returned 6 results for Indeed India
2025-07-17 00:19:25,431 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:25,432 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:25,433 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:25,434 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:25,434 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:25,435 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:26,436 - indian_job_scraper - INFO - Searching Indeed India with SerpAPI: site:in.indeed.com/viewjob product designer bangalore
2025-07-17 00:19:27,238 - indian_job_scraper - INFO - SerpAPI returned 6 results for Indeed India
2025-07-17 00:19:27,239 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:27,240 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:27,240 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:27,241 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:27,242 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:27,242 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:28,243 - indian_job_scraper - INFO - Searching Indeed India with SerpAPI: site:in.indeed.com/viewjob design intern
2025-07-17 00:19:28,942 - indian_job_scraper - INFO - SerpAPI returned 6 results for Indeed India
2025-07-17 00:19:28,943 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:28,945 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:28,945 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:28,946 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:28,947 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:28,947 - indian_job_scraper - WARNING - Error parsing Indeed SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:29,948 - indian_job_scraper - INFO - Found 0 jobs from Indeed India via SerpAPI
2025-07-17 00:19:29,950 - indian_job_scraper - WARNING - No jobs found from Indeed India
2025-07-17 00:19:32,837 - indian_job_scraper - INFO - Searching Naukri.com...
2025-07-17 00:19:32,838 - indian_job_scraper - INFO - Searching Naukri with SerpAPI: site:naukri.com/job-listings ui ux designer
2025-07-17 00:19:33,539 - indian_job_scraper - INFO - SerpAPI returned 5 results for Naukri
2025-07-17 00:19:33,539 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:33,540 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:33,542 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:33,542 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:33,543 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:34,543 - indian_job_scraper - INFO - Searching Naukri with SerpAPI: site:naukri.com/job-listings graphic designer
2025-07-17 00:19:35,244 - indian_job_scraper - INFO - SerpAPI returned 4 results for Naukri
2025-07-17 00:19:35,244 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:35,245 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:35,245 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:35,246 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:36,247 - indian_job_scraper - INFO - Searching Naukri with SerpAPI: site:naukri.com/job-listings product designer india
2025-07-17 00:19:37,193 - indian_job_scraper - INFO - SerpAPI returned 5 results for Naukri
2025-07-17 00:19:37,194 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:37,195 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:37,195 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:37,196 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:37,196 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:38,198 - indian_job_scraper - INFO - Searching Naukri with SerpAPI: site:naukri.com inurl:job-listings design
2025-07-17 00:19:38,888 - indian_job_scraper - INFO - SerpAPI returned 5 results for Naukri
2025-07-17 00:19:38,889 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:38,889 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:38,890 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:38,890 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:38,891 - indian_job_scraper - WARNING - Error parsing Naukri SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:39,892 - indian_job_scraper - INFO - Found 0 jobs from Naukri via SerpAPI
2025-07-17 00:19:39,893 - indian_job_scraper - WARNING - No jobs found from Naukri.com
2025-07-17 00:19:43,859 - indian_job_scraper - INFO - Searching Internshala...
2025-07-17 00:19:43,860 - indian_job_scraper - INFO - Searching Internshala with SerpAPI: site:internshala.com/internship/detail ui ux design
2025-07-17 00:19:44,530 - indian_job_scraper - INFO - SerpAPI returned 4 results for Internshala
2025-07-17 00:19:44,531 - indian_job_scraper - WARNING - Error parsing Internshala SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:44,531 - indian_job_scraper - WARNING - Error parsing Internshala SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:44,532 - indian_job_scraper - WARNING - Error parsing Internshala SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:44,532 - indian_job_scraper - WARNING - Error parsing Internshala SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:45,533 - indian_job_scraper - INFO - Searching Internshala with SerpAPI: site:internshala.com/internship/detail graphic design
2025-07-17 00:19:46,158 - indian_job_scraper - INFO - SerpAPI returned 4 results for Internshala
2025-07-17 00:19:46,159 - indian_job_scraper - WARNING - Error parsing Internshala SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:46,159 - indian_job_scraper - WARNING - Error parsing Internshala SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:46,159 - indian_job_scraper - WARNING - Error parsing Internshala SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:46,161 - indian_job_scraper - WARNING - Error parsing Internshala SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:47,162 - indian_job_scraper - INFO - Searching Internshala with SerpAPI: site:internshala.com inurl:internship/detail design
2025-07-17 00:19:47,806 - indian_job_scraper - INFO - SerpAPI returned 4 results for Internshala
2025-07-17 00:19:47,807 - indian_job_scraper - WARNING - Error parsing Internshala SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:47,807 - indian_job_scraper - WARNING - Error parsing Internshala SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:47,808 - indian_job_scraper - WARNING - Error parsing Internshala SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:47,808 - indian_job_scraper - WARNING - Error parsing Internshala SerpAPI result: 'IndianJobScraper' object has no attribute '_extract_or_generate_date'
2025-07-17 00:19:48,809 - indian_job_scraper - INFO - Found 0 jobs from Internshala via SerpAPI
2025-07-17 00:19:48,811 - indian_job_scraper - WARNING - No jobs found from Internshala
2025-07-17 00:19:51,625 - indian_job_scraper - INFO - Total valid jobs found: 0
2025-07-17 00:19:51,625 - indian_job_search_manager - INFO - Found 0 jobs with verified Indian portal URLs
2025-07-17 00:19:51,627 - job_search_manager - INFO - Indian job search completed. Found 0 jobs, saved 0 to database.
2025-07-17 00:19:51,627 - api_server - INFO - Indian job search completed. Found 0 jobs from Indian portals.
2025-07-17 00:23:34,865 - api_server - INFO - Starting Job AI API Server...
2025-07-17 00:23:49,955 - api_server - INFO - Starting Job AI API Server...
2025-07-17 00:24:05,031 - api_server - INFO - Starting Job AI API Server...
2025-07-17 00:25:41,939 - indian_job_scraper - INFO - Searching Indeed India with SerpAPI: site:in.indeed.com/viewjob ui ux designer
2025-07-17 00:25:45,154 - indian_job_scraper - INFO - SerpAPI returned 1 results for Indeed India
2025-07-17 00:25:46,161 - indian_job_scraper - INFO - Searching Indeed India with SerpAPI: site:in.indeed.com/viewjob graphic designer india
2025-07-17 00:25:49,264 - indian_job_scraper - INFO - SerpAPI returned 1 results for Indeed India
2025-07-17 00:25:50,267 - indian_job_scraper - INFO - Found 2 jobs from Indeed India via SerpAPI
2025-07-17 00:26:53,847 - indian_job_scraper - INFO - Searching Indeed India with SerpAPI: site:in.indeed.com/viewjob ui ux designer
2025-07-17 00:26:55,132 - indian_job_scraper - INFO - SerpAPI returned 1 results for Indeed India
2025-07-17 00:26:56,138 - indian_job_scraper - INFO - Searching Indeed India with SerpAPI: site:in.indeed.com/viewjob graphic designer india
2025-07-17 00:26:57,302 - indian_job_scraper - INFO - SerpAPI returned 1 results for Indeed India
2025-07-17 00:26:58,304 - indian_job_scraper - INFO - Found 2 jobs from Indeed India via SerpAPI
2025-07-17 00:29:00,380 - indian_job_search_manager - INFO - Starting Indian job portal search - 2025-07-17 00:29:00
2025-07-17 00:29:00,381 - indian_job_scraper - INFO - Starting Indian job portal search - 2025-07-17 00:29:00
2025-07-17 00:29:00,381 - indian_job_scraper - INFO - Searching Indeed India...
2025-07-17 00:29:00,381 - indian_job_scraper - INFO - Searching Indeed India with SerpAPI: site:in.indeed.com/viewjob ui ux designer
2025-07-17 00:29:00,779 - indian_job_scraper - INFO - SerpAPI returned 6 results for Indeed India
2025-07-17 00:29:01,786 - indian_job_scraper - INFO - Found 6 jobs from Indeed India via SerpAPI
2025-07-17 00:29:01,787 - indian_job_scraper - INFO - Found 6 valid jobs from Indeed India
2025-07-17 00:29:04,065 - indian_job_scraper - INFO - Searching Naukri.com...
2025-07-17 00:29:04,066 - indian_job_scraper - INFO - Searching Naukri with SerpAPI: site:naukri.com/job-listings ui ux designer
2025-07-17 00:29:04,402 - indian_job_scraper - INFO - SerpAPI returned 5 results for Naukri
2025-07-17 00:29:05,405 - indian_job_scraper - INFO - Found 5 jobs from Naukri via SerpAPI
2025-07-17 00:29:05,405 - indian_job_scraper - INFO - Found 5 valid jobs from Naukri.com
2025-07-17 00:29:07,664 - indian_job_scraper - INFO - Searching Internshala...
2025-07-17 00:29:07,664 - indian_job_scraper - INFO - Searching Internshala with SerpAPI: site:internshala.com/internship/detail ui ux design
2025-07-17 00:29:07,983 - indian_job_scraper - INFO - SerpAPI returned 4 results for Internshala
2025-07-17 00:29:08,837 - api_server - INFO - Starting Job AI API Server...
2025-07-17 00:29:08,986 - indian_job_scraper - INFO - Found 4 jobs from Internshala via SerpAPI
2025-07-17 00:29:08,986 - indian_job_scraper - INFO - Found 4 valid jobs from Internshala
2025-07-17 00:29:11,018 - indian_job_scraper - INFO - Total valid jobs found: 15
2025-07-17 00:29:11,019 - indian_job_search_manager - INFO - Found 15 jobs with verified Indian portal URLs
2025-07-17 00:29:11,784 - api_server - INFO - Starting Job AI API Server...
2025-07-17 00:29:36,980 - api_server - INFO - Starting Job AI API Server...
2025-07-17 00:29:50,102 - api_server - INFO - Cleared 0 jobs from database. 0 jobs remaining.
2025-07-17 00:29:53,961 - job_search_manager - INFO - Starting job search...
2025-07-17 00:29:53,961 - indian_job_search_manager - INFO - Starting Indian job portal search - 2025-07-17 00:29:53
2025-07-17 00:29:53,962 - indian_job_scraper - INFO - Starting Indian job portal search - 2025-07-17 00:29:53
2025-07-17 00:29:53,962 - indian_job_scraper - INFO - Searching Indeed India...
2025-07-17 00:29:53,962 - indian_job_scraper - INFO - Searching Indeed India with SerpAPI: site:in.indeed.com/viewjob ui ux designer
2025-07-17 00:29:54,623 - indian_job_scraper - INFO - SerpAPI returned 6 results for Indeed India
2025-07-17 00:29:55,632 - indian_job_scraper - INFO - Found 6 jobs from Indeed India via SerpAPI
2025-07-17 00:29:55,633 - indian_job_scraper - INFO - Found 6 valid jobs from Indeed India
2025-07-17 00:29:58,831 - api_server - INFO - Starting Job AI API Server...
2025-07-17 00:30:26,161 - api_server - INFO - Starting Job AI API Server...
2025-07-17 00:30:58,963 - view_jobs - INFO - No jobs found in database
2025-07-17 00:36:23,183 - api_server - INFO - Starting Job AI API Server...
2025-07-17 00:36:26,044 - api_server - INFO - Starting Job AI API Server...
2025-07-17 00:36:29,387 - job_search_manager - INFO - Starting job search...
2025-07-17 00:36:29,387 - indian_job_search_manager - INFO - Starting Indian job portal search - 2025-07-17 00:36:29
2025-07-17 00:36:29,387 - indian_job_scraper - INFO - Starting Indian job portal search - 2025-07-17 00:36:29
2025-07-17 00:36:29,388 - indian_job_scraper - INFO - Searching Indeed India...
2025-07-17 00:36:29,388 - indian_job_scraper - INFO - Searching Indeed India with SerpAPI: site:in.indeed.com/viewjob ui ux designer
2025-07-17 00:36:30,557 - indian_job_scraper - INFO - SerpAPI returned 6 results for Indeed India
2025-07-17 00:36:31,564 - indian_job_scraper - INFO - Found 6 jobs from Indeed India via SerpAPI
2025-07-17 00:36:31,566 - indian_job_scraper - INFO - Found 6 valid jobs from Indeed India
2025-07-17 00:36:35,141 - indian_job_scraper - INFO - Searching Naukri.com...
2025-07-17 00:36:35,141 - indian_job_scraper - INFO - Searching Naukri with SerpAPI: site:naukri.com/job-listings ui ux designer
2025-07-17 00:36:35,773 - indian_job_scraper - INFO - SerpAPI returned 5 results for Naukri
2025-07-17 00:36:36,780 - indian_job_scraper - INFO - Found 5 jobs from Naukri via SerpAPI
2025-07-17 00:36:36,781 - indian_job_scraper - INFO - Found 5 valid jobs from Naukri.com
2025-07-17 00:36:38,908 - indian_job_scraper - INFO - Searching Internshala...
2025-07-17 00:36:38,908 - indian_job_scraper - INFO - Searching Internshala with SerpAPI: site:internshala.com/internship/detail ui ux design
2025-07-17 00:36:39,522 - indian_job_scraper - INFO - SerpAPI returned 4 results for Internshala
2025-07-17 00:36:40,527 - indian_job_scraper - INFO - Found 4 jobs from Internshala via SerpAPI
2025-07-17 00:36:40,529 - indian_job_scraper - INFO - Found 4 valid jobs from Internshala
2025-07-17 00:36:42,588 - indian_job_scraper - INFO - Total valid jobs found: 15
2025-07-17 00:36:42,588 - indian_job_search_manager - INFO - Found 15 jobs with verified Indian portal URLs
2025-07-17 00:36:42,736 - job_search_manager - INFO - Indian job search completed. Found 15 jobs, saved 15 to database.
2025-07-17 00:36:42,736 - api_server - INFO - Indian job search completed. Found 15 jobs from Indian portals.
2025-07-17 00:37:11,163 - view_jobs - INFO - Found 15 jobs in database:
2025-07-17 00:37:43,708 - api_server - INFO - Starting Job AI API Server...
2025-07-17 00:37:46,562 - api_server - INFO - Starting Job AI API Server...
