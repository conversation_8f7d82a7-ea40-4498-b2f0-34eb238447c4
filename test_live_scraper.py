#!/usr/bin/env python3
"""
Test script to verify the live job scraper works and returns real URLs.
"""

import sys
import os
sys.path.append('src')

from src.scrapers.live_job_scraper import LiveJobSearchManager
from src.utils.logger import setup_logger

def test_live_scraper():
    """Test the live job scraper"""
    print("="*60)
    print("TESTING LIVE JOB SCRAPER")
    print("="*60)
    
    # Initialize live scraper
    live_manager = LiveJobSearchManager()
    
    try:
        # Search for jobs
        print("🔍 Searching for jobs with real URLs...")
        jobs = live_manager.search_all_jobs()
        
        if not jobs:
            print("❌ No jobs found")
            return
        
        print(f"✅ Found {len(jobs)} jobs")
        print("\n📋 Job Details:")
        print("-" * 60)
        
        real_url_count = 0
        fake_url_count = 0
        
        # Analyze each job
        for i, job in enumerate(jobs, 1):
            print(f"{i:2d}. {job.get('title', 'N/A')}")
            print(f"    Company: {job.get('company', 'N/A')}")
            print(f"    Source: {job.get('source', 'N/A')}")
            print(f"    Location: {job.get('location', 'N/A')}")
            print(f"    Posted: {job.get('posted_date', 'N/A')}")
            
            # Check URL validity
            apply_url = job.get('apply_url', '')
            real_domains = [
                'internshala.com', 'naukri.com', 'in.indeed.com', 'remoteok.io',
                'indeed.com', 'github.com', 'gitlab.com', 'atlassian.com',
                'shopify.com', 'vercel.com', 'linear.app', 'supabase.com',
                'framer.com', 'webflow.com', 'dribbble.com', 'discord.com',
                'twitch.tv', 'redditinc.com', 'dropbox.com', 'slack.com'
            ]
            
            is_real_url = any(domain in apply_url for domain in real_domains)
            
            if is_real_url:
                print(f"    ✅ URL: {apply_url}")
                real_url_count += 1
            else:
                print(f"    ❌ URL: {apply_url}")
                fake_url_count += 1
            
            print()
        
        print("-" * 60)
        print(f"📊 URL Analysis:")
        print(f"   ✅ Real URLs: {real_url_count}")
        print(f"   ❌ Fake URLs: {fake_url_count}")
        print(f"   📈 Real URL Percentage: {(real_url_count/len(jobs)*100):.1f}%")
        
        if real_url_count > 0:
            print("\n🎉 SUCCESS: Live scraper is working and finding real URLs!")
        else:
            print("\n⚠️  WARNING: No real URLs found. Check scraper implementation.")
        
    except Exception as e:
        print(f"❌ Error testing live scraper: {e}")
        import traceback
        traceback.print_exc()

def test_remoteok_specifically():
    """Test RemoteOK API specifically"""
    print("\n" + "="*60)
    print("TESTING REMOTEOK API SPECIFICALLY")
    print("="*60)
    
    try:
        from src.scrapers.live_job_scraper import LiveJobScraper
        
        scraper = LiveJobScraper()
        jobs = scraper._search_remoteok_real(5)
        
        print(f"Found {len(jobs)} jobs from RemoteOK")
        
        for i, job in enumerate(jobs, 1):
            print(f"{i}. {job.get('title')} at {job.get('company')}")
            print(f"   URL: {job.get('apply_url')}")
            print(f"   Posted: {job.get('posted_date')}")
            print()
        
        if jobs:
            print("✅ RemoteOK API is working!")
        else:
            print("❌ RemoteOK API returned no results")
            
    except Exception as e:
        print(f"❌ Error testing RemoteOK: {e}")

def test_indeed_specifically():
    """Test Indeed scraping specifically"""
    print("\n" + "="*60)
    print("TESTING INDEED SCRAPING SPECIFICALLY")
    print("="*60)

    try:
        from src.scrapers.live_job_scraper import LiveJobScraper

        scraper = LiveJobScraper()
        jobs = scraper._search_indeed_real(3)

        print(f"Found {len(jobs)} jobs from Indeed")

        for i, job in enumerate(jobs, 1):
            print(f"{i}. {job.get('title')} at {job.get('company')}")
            print(f"   URL: {job.get('apply_url')}")
            print(f"   Posted: {job.get('posted_date')}")
            print(f"   Location: {job.get('location')}")
            print()

        if jobs:
            print("✅ Indeed scraping is working!")

            # Verify URLs are real Indeed URLs
            real_indeed_urls = 0
            for job in jobs:
                if 'indeed.com/viewjob?jk=' in job.get('apply_url', ''):
                    real_indeed_urls += 1

            print(f"✅ Real Indeed URLs: {real_indeed_urls}/{len(jobs)}")
        else:
            print("❌ Indeed scraping returned no results")
            print("This might be due to anti-bot measures or rate limiting")

    except Exception as e:
        print(f"❌ Error testing Indeed: {e}")
        import traceback
        traceback.print_exc()

def test_indian_portals_specifically():
    """Test Indian job portals specifically"""
    print("\n" + "="*60)
    print("TESTING INDIAN JOB PORTALS SPECIFICALLY")
    print("="*60)

    try:
        from src.scrapers.indian_job_scraper import IndianJobSearchManager

        manager = IndianJobSearchManager()
        jobs = manager.search_all_jobs()

        print(f"Found {len(jobs)} jobs from Indian portals")

        for i, job in enumerate(jobs, 1):
            print(f"{i}. {job.get('title')} at {job.get('company')}")
            print(f"   URL: {job.get('apply_url')}")
            print(f"   Source: {job.get('source')}")
            print(f"   Posted: {job.get('posted_date')}")
            print(f"   Location: {job.get('location')}")
            print()

        if jobs:
            print("✅ Indian job portals are working!")

            # Verify URLs are real Indian portal URLs
            real_indian_urls = 0
            for job in jobs:
                apply_url = job.get('apply_url', '')
                indian_domains = ['internshala.com', 'naukri.com', 'in.indeed.com']
                if any(domain in apply_url for domain in indian_domains):
                    real_indian_urls += 1

            print(f"✅ Real Indian portal URLs: {real_indian_urls}/{len(jobs)}")
        else:
            print("❌ Indian job portals returned no results")

    except Exception as e:
        print(f"❌ Error testing Indian portals: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_live_scraper()
    test_remoteok_specifically()
    test_indeed_specifically()
    test_indian_portals_specifically()
