#!/usr/bin/env python3
"""
Test script to verify the live job scraper works and returns real URLs.
"""

import sys
import os
sys.path.append('src')

from src.scrapers.live_job_scraper import LiveJobSearchManager
from src.utils.logger import setup_logger

def test_live_scraper():
    """Test the live job scraper"""
    print("="*60)
    print("TESTING LIVE JOB SCRAPER")
    print("="*60)
    
    # Initialize live scraper
    live_manager = LiveJobSearchManager()
    
    try:
        # Search for jobs
        print("🔍 Searching for jobs with real URLs...")
        jobs = live_manager.search_all_jobs()
        
        if not jobs:
            print("❌ No jobs found")
            return
        
        print(f"✅ Found {len(jobs)} jobs")
        print("\n📋 Job Details:")
        print("-" * 60)
        
        real_url_count = 0
        fake_url_count = 0
        
        # Analyze each job
        for i, job in enumerate(jobs, 1):
            print(f"{i:2d}. {job.get('title', 'N/A')}")
            print(f"    Company: {job.get('company', 'N/A')}")
            print(f"    Source: {job.get('source', 'N/A')}")
            print(f"    Location: {job.get('location', 'N/A')}")
            print(f"    Posted: {job.get('posted_date', 'N/A')}")
            
            # Check URL validity
            apply_url = job.get('apply_url', '')
            real_domains = [
                'remoteok.io', 'github.com', 'gitlab.com', 'atlassian.com',
                'shopify.com', 'vercel.com', 'linear.app', 'supabase.com',
                'framer.com', 'webflow.com', 'dribbble.com'
            ]
            
            is_real_url = any(domain in apply_url for domain in real_domains)
            
            if is_real_url:
                print(f"    ✅ URL: {apply_url}")
                real_url_count += 1
            else:
                print(f"    ❌ URL: {apply_url}")
                fake_url_count += 1
            
            print()
        
        print("-" * 60)
        print(f"📊 URL Analysis:")
        print(f"   ✅ Real URLs: {real_url_count}")
        print(f"   ❌ Fake URLs: {fake_url_count}")
        print(f"   📈 Real URL Percentage: {(real_url_count/len(jobs)*100):.1f}%")
        
        if real_url_count > 0:
            print("\n🎉 SUCCESS: Live scraper is working and finding real URLs!")
        else:
            print("\n⚠️  WARNING: No real URLs found. Check scraper implementation.")
        
    except Exception as e:
        print(f"❌ Error testing live scraper: {e}")
        import traceback
        traceback.print_exc()

def test_remoteok_specifically():
    """Test RemoteOK API specifically"""
    print("\n" + "="*60)
    print("TESTING REMOTEOK API SPECIFICALLY")
    print("="*60)
    
    try:
        from src.scrapers.live_job_scraper import LiveJobScraper
        
        scraper = LiveJobScraper()
        jobs = scraper._search_remoteok_real(5)
        
        print(f"Found {len(jobs)} jobs from RemoteOK")
        
        for i, job in enumerate(jobs, 1):
            print(f"{i}. {job.get('title')} at {job.get('company')}")
            print(f"   URL: {job.get('apply_url')}")
            print(f"   Posted: {job.get('posted_date')}")
            print()
        
        if jobs:
            print("✅ RemoteOK API is working!")
        else:
            print("❌ RemoteOK API returned no results")
            
    except Exception as e:
        print(f"❌ Error testing RemoteOK: {e}")

if __name__ == "__main__":
    test_live_scraper()
    test_remoteok_specifically()
