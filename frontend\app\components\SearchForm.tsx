import { MagnifyingGlassIcon } from '@heroicons/react/24/outline'

interface SearchFormProps {
  onSearch: () => void
  searching: boolean
}

export default function SearchForm({ onSearch, searching }: SearchFormProps) {
  return (
    <div className="card">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            🇮🇳 Search Indian Job Portals
          </h3>
          <p className="text-gray-600 text-sm">
            Search Internshala, Indeed India, and Naukri.com for real UI/UX design jobs with authentic URLs
          </p>
        </div>
        <button
          onClick={onSearch}
          disabled={searching}
          className="btn-primary flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {searching ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Searching Indian Portals...
            </>
          ) : (
            <>
              <MagnifyingGlassIcon className="h-4 w-4 mr-2" />
              Search Real Jobs
            </>
          )}
        </button>
      </div>
    </div>
  )
}