#!/usr/bin/env python3
"""
Test script specifically for Indian job portals.
Tests Internshala, Indeed India, and Naukri.com scraping.
"""

import sys
import os
sys.path.append('src')

from src.scrapers.indian_job_scraper import IndianJobSearchManager, IndianJobScraper
from src.utils.logger import setup_logger

def test_indian_job_manager():
    """Test the complete Indian job search manager"""
    print("="*60)
    print("TESTING INDIAN JOB SEARCH MANAGER")
    print("="*60)
    
    try:
        manager = IndianJobSearchManager()
        jobs = manager.search_all_jobs()
        
        print(f"✅ Found {len(jobs)} jobs from Indian portals")
        
        if not jobs:
            print("❌ No jobs found")
            return
        
        print("\n📋 Job Details:")
        print("-" * 60)
        
        # Analyze jobs by source
        sources = {}
        indian_url_count = 0
        
        for i, job in enumerate(jobs, 1):
            source = job.get('source', 'Unknown')
            sources[source] = sources.get(source, 0) + 1
            
            # Check if URL is from Indian portals
            apply_url = job.get('apply_url', '')
            indian_domains = ['internshala.com', 'naukri.com', 'in.indeed.com']
            is_indian_url = any(domain in apply_url for domain in indian_domains)
            
            if is_indian_url:
                indian_url_count += 1
                url_status = "✅"
            else:
                url_status = "❌"
            
            print(f"{i:2d}. {job.get('title', 'N/A')}")
            print(f"    Company: {job.get('company', 'N/A')}")
            print(f"    Source: {source}")
            print(f"    Location: {job.get('location', 'N/A')}")
            print(f"    Posted: {job.get('posted_date', 'N/A')}")
            print(f"    {url_status} URL: {apply_url}")
            print()
        
        print("-" * 60)
        print(f"📊 Statistics:")
        print(f"   Total Jobs: {len(jobs)}")
        print(f"   Sources: {dict(sources)}")
        print(f"   ✅ Indian Portal URLs: {indian_url_count}")
        print(f"   ❌ Non-Indian URLs: {len(jobs) - indian_url_count}")
        print(f"   📈 Indian URL Percentage: {(indian_url_count/len(jobs)*100):.1f}%")
        
        if indian_url_count == len(jobs):
            print("\n🎉 SUCCESS: All jobs have Indian portal URLs!")
        else:
            print(f"\n⚠️  WARNING: {len(jobs) - indian_url_count} jobs have non-Indian URLs")
        
    except Exception as e:
        print(f"❌ Error testing Indian job manager: {e}")
        import traceback
        traceback.print_exc()

def test_internshala_specifically():
    """Test Internshala scraping with SerpAPI"""
    print("\n" + "="*60)
    print("TESTING INTERNSHALA WITH SERPAPI")
    print("="*60)

    try:
        scraper = IndianJobScraper()
        jobs = scraper._search_internshala_serp(3)

        print(f"Found {len(jobs)} jobs from Internshala via SerpAPI")

        for i, job in enumerate(jobs, 1):
            print(f"{i}. {job.get('title')} at {job.get('company')}")
            print(f"   URL: {job.get('apply_url')}")
            print(f"   Location: {job.get('location')}")
            print(f"   Posted: {job.get('posted_date')}")

            # Verify URL format
            url = job.get('apply_url', '')
            if 'internshala.com' in url:
                print(f"   ✅ Valid Internshala URL")
            else:
                print(f"   ❌ Invalid URL format")
            print()

        if jobs:
            print("✅ Internshala SerpAPI scraping is working!")
        else:
            print("❌ Internshala SerpAPI scraping returned no results")

    except Exception as e:
        print(f"❌ Error testing Internshala SerpAPI: {e}")
        import traceback
        traceback.print_exc()

def test_indeed_india_specifically():
    """Test Indeed India scraping with SerpAPI"""
    print("\n" + "="*60)
    print("TESTING INDEED INDIA WITH SERPAPI")
    print("="*60)

    try:
        scraper = IndianJobScraper()
        jobs = scraper._search_indeed_india_serp(3)

        print(f"Found {len(jobs)} jobs from Indeed India via SerpAPI")

        for i, job in enumerate(jobs, 1):
            print(f"{i}. {job.get('title')} at {job.get('company')}")
            print(f"   URL: {job.get('apply_url')}")
            print(f"   Location: {job.get('location')}")
            print(f"   Posted: {job.get('posted_date')}")

            # Verify URL format
            url = job.get('apply_url', '')
            if 'in.indeed.com' in url:
                print(f"   ✅ Valid Indeed India URL")
            else:
                print(f"   ❌ Invalid URL format")
            print()

        if jobs:
            print("✅ Indeed India SerpAPI scraping is working!")
        else:
            print("❌ Indeed India SerpAPI scraping returned no results")

    except Exception as e:
        print(f"❌ Error testing Indeed India SerpAPI: {e}")
        import traceback
        traceback.print_exc()

def test_naukri_specifically():
    """Test Naukri.com scraping with SerpAPI"""
    print("\n" + "="*60)
    print("TESTING NAUKRI.COM WITH SERPAPI")
    print("="*60)

    try:
        scraper = IndianJobScraper()
        jobs = scraper._search_naukri_serp(3)

        print(f"Found {len(jobs)} jobs from Naukri.com via SerpAPI")

        for i, job in enumerate(jobs, 1):
            print(f"{i}. {job.get('title')} at {job.get('company')}")
            print(f"   URL: {job.get('apply_url')}")
            print(f"   Location: {job.get('location')}")
            print(f"   Posted: {job.get('posted_date')}")

            # Verify URL format
            url = job.get('apply_url', '')
            if 'naukri.com' in url:
                print(f"   ✅ Valid Naukri.com URL")
            else:
                print(f"   ❌ Invalid URL format")
            print()

        if jobs:
            print("✅ Naukri.com SerpAPI scraping is working!")
        else:
            print("❌ Naukri.com SerpAPI scraping returned no results")

    except Exception as e:
        print(f"❌ Error testing Naukri.com SerpAPI: {e}")
        import traceback
        traceback.print_exc()

def test_serpapi_directly():
    """Test SerpAPI directly to verify it's working"""
    print("\n" + "="*60)
    print("TESTING SERPAPI DIRECTLY")
    print("="*60)

    try:
        import requests

        api_key = "3ab5546b36dad40c4f784ee2b596773952a701f0bcba8173dc311864e3c83510"
        url = "https://serpapi.com/search"

        params = {
            "engine": "google",
            "q": "ui ux designer jobs in india site:in.indeed.com",
            "api_key": api_key,
            "num": 5
        }

        print("Making SerpAPI request...")
        response = requests.get(url, params=params, timeout=30)

        if response.status_code == 200:
            results = response.json()
            organic_results = results.get("organic_results", [])

            print(f"✅ SerpAPI returned {len(organic_results)} results")

            for i, result in enumerate(organic_results[:3], 1):
                print(f"{i}. {result.get('title', 'N/A')}")
                print(f"   URL: {result.get('link', 'N/A')}")
                print(f"   Snippet: {result.get('snippet', 'N/A')[:100]}...")
                print()

            if organic_results:
                print("✅ SerpAPI is working correctly!")
            else:
                print("⚠️  SerpAPI returned no organic results")

        else:
            print(f"❌ SerpAPI request failed with status {response.status_code}")
            print(f"Response: {response.text}")

    except Exception as e:
        print(f"❌ Error testing SerpAPI directly: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main test function"""
    print("🇮🇳 INDIAN JOB PORTAL TESTING SUITE")
    print("Testing Internshala, Indeed India, and Naukri.com")
    print()
    
    # Test SerpAPI directly first
    test_serpapi_directly()

    # Test complete manager
    test_indian_job_manager()

    # Test individual portals with SerpAPI
    test_internshala_specifically()
    test_indeed_india_specifically()
    test_naukri_specifically()
    
    print("\n" + "="*60)
    print("TESTING COMPLETE")
    print("="*60)
    print("If you see ✅ for all tests, the Indian job scraper is working!")
    print("If you see ❌, check the error messages above.")

if __name__ == "__main__":
    main()
