#!/usr/bin/env python3
"""
Test script specifically for Indian job portals.
Tests Internshala, Indeed India, and Naukri.com scraping.
"""

import sys
import os
sys.path.append('src')

from src.scrapers.indian_job_scraper import IndianJobSearchManager, IndianJobScraper
from src.utils.logger import setup_logger

def test_indian_job_manager():
    """Test the complete Indian job search manager"""
    print("="*60)
    print("TESTING INDIAN JOB SEARCH MANAGER")
    print("="*60)
    
    try:
        manager = IndianJobSearchManager()
        jobs = manager.search_all_jobs()
        
        print(f"✅ Found {len(jobs)} jobs from Indian portals")
        
        if not jobs:
            print("❌ No jobs found")
            return
        
        print("\n📋 Job Details:")
        print("-" * 60)
        
        # Analyze jobs by source
        sources = {}
        indian_url_count = 0
        
        for i, job in enumerate(jobs, 1):
            source = job.get('source', 'Unknown')
            sources[source] = sources.get(source, 0) + 1
            
            # Check if URL is from Indian portals
            apply_url = job.get('apply_url', '')
            indian_domains = ['internshala.com', 'naukri.com', 'in.indeed.com']
            is_indian_url = any(domain in apply_url for domain in indian_domains)
            
            if is_indian_url:
                indian_url_count += 1
                url_status = "✅"
            else:
                url_status = "❌"
            
            print(f"{i:2d}. {job.get('title', 'N/A')}")
            print(f"    Company: {job.get('company', 'N/A')}")
            print(f"    Source: {source}")
            print(f"    Location: {job.get('location', 'N/A')}")
            print(f"    Posted: {job.get('posted_date', 'N/A')}")
            print(f"    {url_status} URL: {apply_url}")
            print()
        
        print("-" * 60)
        print(f"📊 Statistics:")
        print(f"   Total Jobs: {len(jobs)}")
        print(f"   Sources: {dict(sources)}")
        print(f"   ✅ Indian Portal URLs: {indian_url_count}")
        print(f"   ❌ Non-Indian URLs: {len(jobs) - indian_url_count}")
        print(f"   📈 Indian URL Percentage: {(indian_url_count/len(jobs)*100):.1f}%")
        
        if indian_url_count == len(jobs):
            print("\n🎉 SUCCESS: All jobs have Indian portal URLs!")
        else:
            print(f"\n⚠️  WARNING: {len(jobs) - indian_url_count} jobs have non-Indian URLs")
        
    except Exception as e:
        print(f"❌ Error testing Indian job manager: {e}")
        import traceback
        traceback.print_exc()

def test_internshala_specifically():
    """Test Internshala scraping specifically"""
    print("\n" + "="*60)
    print("TESTING INTERNSHALA SPECIFICALLY")
    print("="*60)
    
    try:
        scraper = IndianJobScraper()
        jobs = scraper._search_internshala(5)
        
        print(f"Found {len(jobs)} jobs from Internshala")
        
        for i, job in enumerate(jobs, 1):
            print(f"{i}. {job.get('title')} at {job.get('company')}")
            print(f"   URL: {job.get('apply_url')}")
            print(f"   Location: {job.get('location')}")
            print(f"   Posted: {job.get('posted_date')}")
            
            # Verify URL format
            url = job.get('apply_url', '')
            if 'internshala.com' in url:
                print(f"   ✅ Valid Internshala URL")
            else:
                print(f"   ❌ Invalid URL format")
            print()
        
        if jobs:
            print("✅ Internshala scraping is working!")
        else:
            print("❌ Internshala scraping returned no results")
            
    except Exception as e:
        print(f"❌ Error testing Internshala: {e}")
        import traceback
        traceback.print_exc()

def test_indeed_india_specifically():
    """Test Indeed India scraping specifically"""
    print("\n" + "="*60)
    print("TESTING INDEED INDIA SPECIFICALLY")
    print("="*60)
    
    try:
        scraper = IndianJobScraper()
        jobs = scraper._search_indeed_india(3)
        
        print(f"Found {len(jobs)} jobs from Indeed India")
        
        for i, job in enumerate(jobs, 1):
            print(f"{i}. {job.get('title')} at {job.get('company')}")
            print(f"   URL: {job.get('apply_url')}")
            print(f"   Location: {job.get('location')}")
            print(f"   Posted: {job.get('posted_date')}")
            
            # Verify URL format
            url = job.get('apply_url', '')
            if 'in.indeed.com' in url:
                print(f"   ✅ Valid Indeed India URL")
            else:
                print(f"   ❌ Invalid URL format")
            print()
        
        if jobs:
            print("✅ Indeed India scraping is working!")
        else:
            print("❌ Indeed India scraping returned no results")
            print("This might be due to anti-bot measures")
            
    except Exception as e:
        print(f"❌ Error testing Indeed India: {e}")
        import traceback
        traceback.print_exc()

def test_naukri_specifically():
    """Test Naukri.com scraping specifically"""
    print("\n" + "="*60)
    print("TESTING NAUKRI.COM SPECIFICALLY")
    print("="*60)
    
    try:
        scraper = IndianJobScraper()
        jobs = scraper._search_naukri(4)
        
        print(f"Found {len(jobs)} jobs from Naukri.com")
        
        for i, job in enumerate(jobs, 1):
            print(f"{i}. {job.get('title')} at {job.get('company')}")
            print(f"   URL: {job.get('apply_url')}")
            print(f"   Location: {job.get('location')}")
            print(f"   Posted: {job.get('posted_date')}")
            
            # Verify URL format
            url = job.get('apply_url', '')
            if 'naukri.com' in url:
                print(f"   ✅ Valid Naukri.com URL")
            else:
                print(f"   ❌ Invalid URL format")
            print()
        
        if jobs:
            print("✅ Naukri.com scraping is working!")
        else:
            print("❌ Naukri.com scraping returned no results")
            
    except Exception as e:
        print(f"❌ Error testing Naukri.com: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main test function"""
    print("🇮🇳 INDIAN JOB PORTAL TESTING SUITE")
    print("Testing Internshala, Indeed India, and Naukri.com")
    print()
    
    # Test complete manager
    test_indian_job_manager()
    
    # Test individual portals
    test_internshala_specifically()
    test_indeed_india_specifically()
    test_naukri_specifically()
    
    print("\n" + "="*60)
    print("TESTING COMPLETE")
    print("="*60)
    print("If you see ✅ for all tests, the Indian job scraper is working!")
    print("If you see ❌, check the error messages above.")

if __name__ == "__main__":
    main()
