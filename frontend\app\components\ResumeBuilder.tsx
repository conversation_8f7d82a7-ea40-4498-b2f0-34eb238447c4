'use client'
import { useState, useEffect } from 'react'
import { DocumentTextIcon, SparklesIcon, ArrowDownTrayIcon } from '@heroicons/react/24/outline'

interface ResumeData {
  contact_info: {
    name: string
    email: string
    phone: string
    location: string
    portfolio_url?: string
    linkedin?: string
    github?: string
  }
  professional_summary: string
  highlighted_skills: string[]
  experience: Array<{
    title: string
    company: string
    duration: string
    type?: string
    achievements: string[]
  }>
  projects: Array<{
    name: string
    role?: string
    description: string
    technologies: string[]
    achievements: string[]
  }>
  education: {
    degree: string
    institution: string
    duration: string
    status?: string
    relevant_coursework?: string[]
  }
  certifications: string[]
  ai_expertise?: {
    prompt_engineering: string[]
    ai_tools: string[]
    automation: string[]
  }
  unique_value_props?: string[]
  customized_for?: string
  match_score?: number
  ai_emphasis_level?: string
  customization_notes?: string
}

interface JobInfo {
  title: string
  company: string
  match_score: number
}

export default function ResumeBuilder({ jobId }: { jobId?: string }) {
  const [resumeData, setResumeData] = useState<ResumeData | null>(null)
  const [jobInfo, setJobInfo] = useState<JobInfo | null>(null)
  const [analysis, setAnalysis] = useState<any>(null)
  const [isCustomizing, setIsCustomizing] = useState(false)
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const customizeForJob = async () => {
    if (!jobId) {
      setError('No job ID provided for customization')
      return
    }

    setIsCustomizing(true)
    setError(null)
    
    try {
      const response = await fetch('/api/resume/customize', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ jobId })
      })
      
      const data = await response.json()
      
      if (data.success) {
        setResumeData(data.resume)
        setAnalysis(data.analysis)
        setJobInfo(data.job_info)
      } else {
        setError(data.error || 'Failed to customize resume')
      }
    } catch (error) {
      console.error('Error customizing resume:', error)
      setError('Network error while customizing resume')
    } finally {
      setIsCustomizing(false)
    }
  }

  const generateAndDownloadPDF = async () => {
    if (!resumeData) {
      setError('No resume data available for PDF generation')
      return
    }

    setIsGeneratingPDF(true)
    setError(null)

    try {
      const response = await fetch('/api/resume/generate-pdf', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          resumeData, 
          jobId: jobId || 'general' 
        })
      })
      
      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        
        // Create a meaningful filename
        const jobTitle = resumeData.customized_for || 'general'
        const fileName = `${resumeData.contact_info?.name || 'Resume'}_${jobTitle.replace(/[^a-zA-Z0-9]/g, '_')}.pdf`
        a.download = fileName
        
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        window.URL.revokeObjectURL(url)
      } else {
        const errorData = await response.json()
        setError(errorData.error || 'Failed to generate PDF')
      }
    } catch (error) {
      console.error('Error generating PDF:', error)
      setError('Network error while generating PDF')
    } finally {
      setIsGeneratingPDF(false)
    }
  }

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <DocumentTextIcon className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">AI Resume Builder</h1>
            <p className="text-gray-600">Create ATS-optimized resumes for specific jobs</p>
          </div>
        </div>
        
        {resumeData?.match_score && (
          <div className="text-right">
            <div className="text-sm text-gray-600">Job Match Score</div>
            <div className={`text-2xl font-bold ${
              resumeData.match_score >= 80 ? 'text-green-600' : 
              resumeData.match_score >= 60 ? 'text-yellow-600' : 'text-red-600'
            }`}>
              {resumeData.match_score}%
            </div>
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-800">{error}</p>
        </div>
      )}

      {/* Controls */}
      <div className="flex space-x-4 mb-6">
        <button
          onClick={customizeForJob}
          disabled={isCustomizing || !jobId}
          className="btn-primary flex items-center space-x-2"
        >
          <SparklesIcon className="h-5 w-5" />
          <span>{isCustomizing ? 'Customizing...' : 'AI Customize Resume'}</span>
        </button>
        
        <button
          onClick={generateAndDownloadPDF}
          disabled={!resumeData || isGeneratingPDF}
          className="btn-secondary flex items-center space-x-2"
        >
          <ArrowDownTrayIcon className="h-5 w-5" />
          <span>{isGeneratingPDF ? 'Generating...' : 'Download PDF'}</span>
        </button>
      </div>

      {/* Job Info */}
      {jobInfo && (
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="font-semibold text-blue-900 mb-2">Customizing for:</h3>
          <p className="text-blue-800">{jobInfo.title} at {jobInfo.company}</p>
          {resumeData?.ai_emphasis_level && (
            <p className="text-sm text-blue-600 mt-1">
              AI Emphasis Level: <span className="capitalize">{resumeData.ai_emphasis_level}</span>
            </p>
          )}
        </div>
      )}

      {/* Resume Preview */}
      {resumeData && (
        <div className="resume-preview bg-gray-50 p-6 rounded-lg">
          <h2 className="text-xl font-bold mb-4 text-gray-900">Resume Preview</h2>

          {/* Contact Information */}
          {resumeData.contact_info && (
            <div className="mb-6 p-4 bg-white rounded-lg border">
              <h3 className="text-lg font-semibold text-gray-800 mb-3">Contact Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                <div><strong>Name:</strong> {resumeData.contact_info.name}</div>
                <div><strong>Email:</strong> {resumeData.contact_info.email}</div>
                <div><strong>Phone:</strong> {resumeData.contact_info.phone}</div>
                <div><strong>Location:</strong> {resumeData.contact_info.location}</div>
                {resumeData.contact_info.portfolio_url && (
                  <div><strong>Portfolio:</strong>
                    <a href={resumeData.contact_info.portfolio_url} target="_blank" rel="noopener noreferrer"
                       className="text-blue-600 hover:text-blue-800 ml-1">
                      {resumeData.contact_info.portfolio_url}
                    </a>
                  </div>
                )}
                {resumeData.contact_info.linkedin && (
                  <div><strong>LinkedIn:</strong>
                    <a href={resumeData.contact_info.linkedin} target="_blank" rel="noopener noreferrer"
                       className="text-blue-600 hover:text-blue-800 ml-1">
                      LinkedIn Profile
                    </a>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Professional Summary */}
          {resumeData.professional_summary && (
            <div className="mb-6 p-4 bg-white rounded-lg border">
              <h3 className="text-lg font-semibold text-gray-800 mb-2">Professional Summary</h3>
              <p className="text-gray-700 leading-relaxed">{resumeData.professional_summary}</p>
            </div>
          )}

          {/* Core Skills */}
          {resumeData.highlighted_skills && resumeData.highlighted_skills.length > 0 && (
            <div className="mb-6 p-4 bg-white rounded-lg border">
              <h3 className="text-lg font-semibold text-gray-800 mb-3">Core Skills</h3>
              <div className="flex flex-wrap gap-2">
                {resumeData.highlighted_skills.map((skill, index) => (
                  <span key={index} className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                    {skill}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* AI Expertise Section */}
          {resumeData.ai_expertise && (
            <div className="mb-6 p-4 bg-purple-50 rounded-lg border border-purple-200">
              <h3 className="text-lg font-semibold text-purple-800 mb-3">🤖 AI Expertise & Automation</h3>

              {resumeData.ai_expertise.prompt_engineering && resumeData.ai_expertise.prompt_engineering.length > 0 && (
                <div className="mb-3">
                  <h4 className="font-medium text-purple-700 mb-1">Prompt Engineering</h4>
                  <ul className="list-disc list-inside text-sm text-purple-600">
                    {resumeData.ai_expertise.prompt_engineering.map((skill, index) => (
                      <li key={index}>{skill}</li>
                    ))}
                  </ul>
                </div>
              )}

              {resumeData.ai_expertise.ai_tools && resumeData.ai_expertise.ai_tools.length > 0 && (
                <div className="mb-3">
                  <h4 className="font-medium text-purple-700 mb-1">AI Tools Mastery</h4>
                  <div className="flex flex-wrap gap-1">
                    {resumeData.ai_expertise.ai_tools.map((tool, index) => (
                      <span key={index} className="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded">
                        {tool}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {resumeData.ai_expertise.automation && resumeData.ai_expertise.automation.length > 0 && (
                <div>
                  <h4 className="font-medium text-purple-700 mb-1">Workflow Automation</h4>
                  <ul className="list-disc list-inside text-sm text-purple-600">
                    {resumeData.ai_expertise.automation.map((skill, index) => (
                      <li key={index}>{skill}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}

          {/* Experience */}
          {resumeData.experience && resumeData.experience.length > 0 && (
            <div className="mb-6 p-4 bg-white rounded-lg border">
              <h3 className="text-lg font-semibold text-gray-800 mb-3">Professional Experience</h3>
              {resumeData.experience.map((exp, index) => (
                <div key={index} className="mb-6 last:mb-0 pb-4 last:pb-0 border-b last:border-b-0 border-gray-200">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="font-semibold text-gray-900 text-lg">{exp.title}</h4>
                      <p className="text-gray-700 font-medium">{exp.company}</p>
                      {exp.type && <p className="text-sm text-gray-600">{exp.type}</p>}
                    </div>
                    <span className="text-sm text-gray-600 font-medium">{exp.duration}</span>
                  </div>
                  {exp.achievements && exp.achievements.length > 0 && (
                    <div>
                      <h5 className="font-medium text-gray-800 mb-2">Key Achievements:</h5>
                      <ul className="list-disc list-inside text-sm text-gray-700 space-y-1">
                        {exp.achievements.map((achievement: string, i: number) => (
                          <li key={i}>{achievement}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}

          {/* Projects */}
          {resumeData.projects && resumeData.projects.length > 0 && (
            <div className="mb-6 p-4 bg-white rounded-lg border">
              <h3 className="text-lg font-semibold text-gray-800 mb-3">Key Projects</h3>
              {resumeData.projects.map((project, index) => (
                <div key={index} className="mb-6 last:mb-0 pb-4 last:pb-0 border-b last:border-b-0 border-gray-200">
                  <div className="mb-2">
                    <h4 className="font-semibold text-gray-900 text-lg">{project.name}</h4>
                    {project.role && <p className="text-sm text-gray-600 font-medium">{project.role}</p>}
                  </div>
                  <p className="text-gray-700 text-sm mb-3 leading-relaxed">{project.description}</p>

                  {project.technologies && project.technologies.length > 0 && (
                    <div className="mb-3">
                      <h5 className="text-xs font-medium text-gray-600 mb-1">Technologies:</h5>
                      <div className="flex flex-wrap gap-1">
                        {project.technologies.map((tech: string, i: number) => (
                          <span key={i} className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">
                            {tech}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {project.achievements && project.achievements.length > 0 && (
                    <div>
                      <h5 className="text-xs font-medium text-gray-600 mb-1">Impact & Results:</h5>
                      <ul className="list-disc list-inside text-sm text-gray-700 space-y-1">
                        {project.achievements.map((achievement: string, i: number) => (
                          <li key={i}>{achievement}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}

          {/* Education */}
          {resumeData.education && (
            <div className="mb-6 p-4 bg-white rounded-lg border">
              <h3 className="text-lg font-semibold text-gray-800 mb-3">Education</h3>
              <div className="mb-2">
                <h4 className="font-semibold text-gray-900 text-lg">{resumeData.education.degree}</h4>
                <p className="text-gray-700 font-medium">{resumeData.education.institution}</p>
                <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                  <span>{resumeData.education.duration}</span>
                  {resumeData.education.status && <span className="font-medium">({resumeData.education.status})</span>}
                </div>
              </div>
              {resumeData.education.relevant_coursework && resumeData.education.relevant_coursework.length > 0 && (
                <div className="mt-3">
                  <h5 className="text-sm font-medium text-gray-700 mb-1">Relevant Coursework:</h5>
                  <div className="flex flex-wrap gap-1">
                    {resumeData.education.relevant_coursework.map((course, index) => (
                      <span key={index} className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded">
                        {course}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Certifications */}
          {resumeData.certifications && resumeData.certifications.length > 0 && (
            <div className="mb-6 p-4 bg-white rounded-lg border">
              <h3 className="text-lg font-semibold text-gray-800 mb-3">Certifications</h3>
              <ul className="space-y-2">
                {resumeData.certifications.map((cert, index) => (
                  <li key={index} className="flex items-center text-sm text-gray-700">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                    {cert}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Unique Value Propositions */}
          {resumeData.unique_value_props && resumeData.unique_value_props.length > 0 && (
            <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
              <h3 className="text-lg font-semibold text-gray-800 mb-3">🌟 Unique Value Propositions</h3>
              <ul className="space-y-2">
                {resumeData.unique_value_props.map((prop, index) => (
                  <li key={index} className="flex items-start text-sm text-gray-700">
                    <span className="text-blue-500 mr-2 mt-1">✨</span>
                    <span>{prop}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Customization Notes */}
          {resumeData.customization_notes && (
            <div className="mb-6 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
              <h3 className="text-sm font-semibold text-yellow-800 mb-2">📝 Customization Notes</h3>
              <p className="text-sm text-yellow-700">{resumeData.customization_notes}</p>
            </div>
          )}
        </div>
      )}
      
      {!resumeData && !isCustomizing && (
        <div className="text-center py-12">
          <DocumentTextIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Resume Data</h3>
          <p className="text-gray-600">
            {jobId ? 'Click "AI Customize Resume" to generate a job-specific resume' : 'Select a job to customize your resume'}
          </p>
        </div>
      )}
    </div>
  )
}
