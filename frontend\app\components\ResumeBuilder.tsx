'use client'
import { useState, useEffect } from 'react'
import { DocumentTextIcon, SparklesIcon, ArrowDownTrayIcon } from '@heroicons/react/24/outline'

interface ResumeData {
  professional_summary: string
  highlighted_skills: string[]
  experience: any[]
  projects: any[]
  education: any
  contact_info: any
  customized_for?: string
  match_score?: number
  ai_emphasis_level?: string
}

interface JobInfo {
  title: string
  company: string
  match_score: number
}

export default function ResumeBuilder({ jobId }: { jobId?: string }) {
  const [resumeData, setResumeData] = useState<ResumeData | null>(null)
  const [jobInfo, setJobInfo] = useState<JobInfo | null>(null)
  const [analysis, setAnalysis] = useState<any>(null)
  const [isCustomizing, setIsCustomizing] = useState(false)
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const customizeForJob = async () => {
    if (!jobId) {
      setError('No job ID provided for customization')
      return
    }

    setIsCustomizing(true)
    setError(null)
    
    try {
      const response = await fetch('/api/resume/customize', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ jobId })
      })
      
      const data = await response.json()
      
      if (data.success) {
        setResumeData(data.resume)
        setAnalysis(data.analysis)
        setJobInfo(data.job_info)
      } else {
        setError(data.error || 'Failed to customize resume')
      }
    } catch (error) {
      console.error('Error customizing resume:', error)
      setError('Network error while customizing resume')
    } finally {
      setIsCustomizing(false)
    }
  }

  const generateAndDownloadPDF = async () => {
    if (!resumeData) {
      setError('No resume data available for PDF generation')
      return
    }

    setIsGeneratingPDF(true)
    setError(null)

    try {
      const response = await fetch('/api/resume/generate-pdf', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          resumeData, 
          jobId: jobId || 'general' 
        })
      })
      
      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        
        // Create a meaningful filename
        const jobTitle = resumeData.customized_for || 'general'
        const fileName = `${resumeData.contact_info?.name || 'Resume'}_${jobTitle.replace(/[^a-zA-Z0-9]/g, '_')}.pdf`
        a.download = fileName
        
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        window.URL.revokeObjectURL(url)
      } else {
        const errorData = await response.json()
        setError(errorData.error || 'Failed to generate PDF')
      }
    } catch (error) {
      console.error('Error generating PDF:', error)
      setError('Network error while generating PDF')
    } finally {
      setIsGeneratingPDF(false)
    }
  }

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <DocumentTextIcon className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">AI Resume Builder</h1>
            <p className="text-gray-600">Create ATS-optimized resumes for specific jobs</p>
          </div>
        </div>
        
        {resumeData?.match_score && (
          <div className="text-right">
            <div className="text-sm text-gray-600">Job Match Score</div>
            <div className={`text-2xl font-bold ${
              resumeData.match_score >= 80 ? 'text-green-600' : 
              resumeData.match_score >= 60 ? 'text-yellow-600' : 'text-red-600'
            }`}>
              {resumeData.match_score}%
            </div>
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-800">{error}</p>
        </div>
      )}

      {/* Controls */}
      <div className="flex space-x-4 mb-6">
        <button
          onClick={customizeForJob}
          disabled={isCustomizing || !jobId}
          className="btn-primary flex items-center space-x-2"
        >
          <SparklesIcon className="h-5 w-5" />
          <span>{isCustomizing ? 'Customizing...' : 'AI Customize Resume'}</span>
        </button>
        
        <button
          onClick={generateAndDownloadPDF}
          disabled={!resumeData || isGeneratingPDF}
          className="btn-secondary flex items-center space-x-2"
        >
          <ArrowDownTrayIcon className="h-5 w-5" />
          <span>{isGeneratingPDF ? 'Generating...' : 'Download PDF'}</span>
        </button>
      </div>

      {/* Job Info */}
      {jobInfo && (
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="font-semibold text-blue-900 mb-2">Customizing for:</h3>
          <p className="text-blue-800">{jobInfo.title} at {jobInfo.company}</p>
          {resumeData?.ai_emphasis_level && (
            <p className="text-sm text-blue-600 mt-1">
              AI Emphasis Level: <span className="capitalize">{resumeData.ai_emphasis_level}</span>
            </p>
          )}
        </div>
      )}

      {/* Resume Preview */}
      {resumeData && (
        <div className="resume-preview bg-gray-50 p-6 rounded-lg">
          <h2 className="text-xl font-bold mb-4 text-gray-900">Resume Preview</h2>
          
          {/* Professional Summary */}
          {resumeData.professional_summary && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-2">Professional Summary</h3>
              <p className="text-gray-700 leading-relaxed">{resumeData.professional_summary}</p>
            </div>
          )}
          
          {/* Skills */}
          {resumeData.highlighted_skills && resumeData.highlighted_skills.length > 0 && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-2">Core Skills</h3>
              <div className="flex flex-wrap gap-2">
                {resumeData.highlighted_skills.map((skill, index) => (
                  <span key={index} className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                    {skill}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Experience */}
          {resumeData.experience && resumeData.experience.length > 0 && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-2">Experience</h3>
              {resumeData.experience.map((exp, index) => (
                <div key={index} className="mb-4 p-4 bg-white rounded border">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-semibold text-gray-900">{exp.title}</h4>
                    <span className="text-sm text-gray-600">{exp.duration}</span>
                  </div>
                  <p className="text-gray-700 mb-2">{exp.company}</p>
                  {exp.achievements && (
                    <ul className="list-disc list-inside text-sm text-gray-600">
                      {exp.achievements.map((achievement: string, i: number) => (
                        <li key={i}>{achievement}</li>
                      ))}
                    </ul>
                  )}
                </div>
              ))}
            </div>
          )}

          {/* Projects */}
          {resumeData.projects && resumeData.projects.length > 0 && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-2">Key Projects</h3>
              {resumeData.projects.map((project, index) => (
                <div key={index} className="mb-4 p-4 bg-white rounded border">
                  <h4 className="font-semibold text-gray-900 mb-1">{project.name}</h4>
                  <p className="text-gray-700 text-sm mb-2">{project.description}</p>
                  {project.technologies && (
                    <div className="flex flex-wrap gap-1">
                      {project.technologies.map((tech: string, i: number) => (
                        <span key={i} className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                          {tech}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}

          {/* Education */}
          {resumeData.education && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-2">Education</h3>
              <div className="p-4 bg-white rounded border">
                <h4 className="font-semibold text-gray-900">{resumeData.education.degree}</h4>
                <p className="text-gray-700">{resumeData.education.institution}</p>
                <p className="text-sm text-gray-600">{resumeData.education.duration}</p>
              </div>
            </div>
          )}
        </div>
      )}
      
      {!resumeData && !isCustomizing && (
        <div className="text-center py-12">
          <DocumentTextIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Resume Data</h3>
          <p className="text-gray-600">
            {jobId ? 'Click "AI Customize Resume" to generate a job-specific resume' : 'Select a job to customize your resume'}
          </p>
        </div>
      )}
    </div>
  )
}
