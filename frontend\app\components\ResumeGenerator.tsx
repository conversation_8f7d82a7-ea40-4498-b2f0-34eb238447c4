'use client'

import { useState } from 'react'
import { DocumentTextIcon, DownloadIcon, EyeIcon } from '@heroicons/react/24/outline'

interface ResumeData {
  personal_info: {
    name: string
    email: string
    phone: string
    location: string
    linkedin: string
    portfolio: string
  }
  summary: string
  skills: string[]
  experience: Array<{
    title: string
    company: string
    duration: string
    location: string
    responsibilities: string[]
  }>
  education: Array<{
    degree: string
    institution: string
    year: string
    location: string
    gpa: string
  }>
  projects: Array<{
    name: string
    description: string
    technologies: string[]
    duration: string
  }>
}

export default function ResumeGenerator() {
  const [loading, setLoading] = useState(false)
  const [resume, setResume] = useState<ResumeData | null>(null)
  const [templateStyle, setTemplateStyle] = useState('modern')
  const [error, setError] = useState<string | null>(null)

  const generateResume = async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/resume/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ template_style: templateStyle })
      })

      const data = await response.json()

      if (data.success) {
        setResume(data.resume)
      } else {
        setError(data.error || 'Failed to generate resume')
      }
    } catch (error) {
      console.error('Error generating resume:', error)
      setError('Network error while generating resume')
    } finally {
      setLoading(false)
    }
  }

  const downloadResume = () => {
    if (!resume) return

    const resumeText = formatResumeAsText(resume)
    const blob = new Blob([resumeText], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${resume.personal_info.name.replace(/\s+/g, '_')}_Resume.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const formatResumeAsText = (resumeData: ResumeData): string => {
    const { personal_info, summary, skills, experience, education, projects } = resumeData

    return `
${personal_info.name}
${personal_info.email} | ${personal_info.phone}
${personal_info.location}
LinkedIn: ${personal_info.linkedin}
Portfolio: ${personal_info.portfolio}

PROFESSIONAL SUMMARY
${summary}

TECHNICAL SKILLS
${skills.join(' • ')}

EXPERIENCE
${experience.map(exp => `
${exp.title} | ${exp.company}
${exp.duration} | ${exp.location}
${exp.responsibilities.map(resp => `• ${resp}`).join('\n')}
`).join('\n')}

EDUCATION
${education.map(edu => `
${edu.degree}
${edu.institution} | ${edu.year}
${edu.location} | GPA: ${edu.gpa}
`).join('\n')}

PROJECTS
${projects.map(proj => `
${proj.name}
${proj.description}
Technologies: ${proj.technologies.join(', ')}
Duration: ${proj.duration}
`).join('\n')}
`.trim()
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <DocumentTextIcon className="h-6 w-6 mr-2 text-blue-500" />
            Resume Generator
          </h2>
          <p className="text-gray-600 mt-1">
            Generate a professional resume based on your profile
          </p>
        </div>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600">{error}</p>
        </div>
      )}

      {/* Template Selection */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Resume Template
        </label>
        <select
          value={templateStyle}
          onChange={(e) => setTemplateStyle(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="modern">Modern</option>
          <option value="classic">Classic</option>
          <option value="minimal">Minimal</option>
          <option value="creative">Creative</option>
        </select>
      </div>

      {/* Generate Button */}
      <div className="mb-6">
        <button
          onClick={generateResume}
          disabled={loading}
          className="w-full bg-blue-500 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
        >
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Generating Resume...
            </>
          ) : (
            <>
              <DocumentTextIcon className="h-4 w-4 mr-2" />
              Generate Resume
            </>
          )}
        </button>
      </div>

      {/* Resume Preview */}
      {resume && (
        <div className="border-t pt-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <EyeIcon className="h-5 w-5 mr-2" />
              Resume Preview
            </h3>
            <button
              onClick={downloadResume}
              className="bg-green-500 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-600 flex items-center"
            >
              <DownloadIcon className="h-4 w-4 mr-2" />
              Download
            </button>
          </div>

          <div className="bg-gray-50 p-6 rounded-lg border">
            <div className="space-y-6">
              {/* Header */}
              <div className="text-center border-b pb-4">
                <h1 className="text-2xl font-bold text-gray-900">{resume.personal_info.name}</h1>
                <div className="text-gray-600 mt-2">
                  {resume.personal_info.email} | {resume.personal_info.phone}
                </div>
                <div className="text-gray-600">
                  {resume.personal_info.location}
                </div>
                <div className="text-blue-600 mt-1">
                  {resume.personal_info.linkedin} | {resume.personal_info.portfolio}
                </div>
              </div>

              {/* Summary */}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-2">Professional Summary</h2>
                <p className="text-gray-700">{resume.summary}</p>
              </div>

              {/* Skills */}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-2">Technical Skills</h2>
                <div className="flex flex-wrap gap-2">
                  {resume.skills.map((skill, index) => (
                    <span
                      key={index}
                      className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </div>

              {/* Experience */}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-2">Experience</h2>
                {resume.experience.map((exp, index) => (
                  <div key={index} className="mb-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium text-gray-900">{exp.title}</h3>
                        <p className="text-gray-600">{exp.company}</p>
                      </div>
                      <div className="text-right text-sm text-gray-500">
                        <p>{exp.duration}</p>
                        <p>{exp.location}</p>
                      </div>
                    </div>
                    <ul className="mt-2 space-y-1">
                      {exp.responsibilities.map((resp, respIndex) => (
                        <li key={respIndex} className="text-gray-700 text-sm">
                          • {resp}
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>

              {/* Education */}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-2">Education</h2>
                {resume.education.map((edu, index) => (
                  <div key={index} className="mb-2">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium text-gray-900">{edu.degree}</h3>
                        <p className="text-gray-600">{edu.institution}</p>
                      </div>
                      <div className="text-right text-sm text-gray-500">
                        <p>{edu.year}</p>
                        <p>GPA: {edu.gpa}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Projects */}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-2">Projects</h2>
                {resume.projects.map((project, index) => (
                  <div key={index} className="mb-3">
                    <h3 className="font-medium text-gray-900">{project.name}</h3>
                    <p className="text-gray-700 text-sm mt-1">{project.description}</p>
                    <p className="text-gray-600 text-sm mt-1">
                      <strong>Technologies:</strong> {project.technologies.join(', ')}
                    </p>
                    <p className="text-gray-500 text-sm">Duration: {project.duration}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
