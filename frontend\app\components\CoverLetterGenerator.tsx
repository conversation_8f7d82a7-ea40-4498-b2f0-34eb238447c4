'use client'

import { useState } from 'react'
import { EnvelopeIcon, DownloadIcon, EyeIcon } from '@heroicons/react/24/outline'

interface CoverLetterData {
  content: string
  job_title: string
  company_name: string
  relevant_skills: string[]
  generated_at: string
}

export default function CoverLetterGenerator() {
  const [loading, setLoading] = useState(false)
  const [coverLetter, setCoverLetter] = useState<CoverLetterData | null>(null)
  const [error, setError] = useState<string | null>(null)
  
  // Form data
  const [jobTitle, setJobTitle] = useState('')
  const [companyName, setCompanyName] = useState('')
  const [jobDescription, setJobDescription] = useState('')

  const generateCoverLetter = async () => {
    if (!jobTitle.trim() || !companyName.trim()) {
      setError('Job title and company name are required')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/cover-letter/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          job_title: jobTitle.trim(),
          company_name: companyName.trim(),
          job_description: jobDescription.trim()
        })
      })

      const data = await response.json()

      if (data.success) {
        setCoverLetter(data.cover_letter)
      } else {
        setError(data.error || 'Failed to generate cover letter')
      }
    } catch (error) {
      console.error('Error generating cover letter:', error)
      setError('Network error while generating cover letter')
    } finally {
      setLoading(false)
    }
  }

  const downloadCoverLetter = () => {
    if (!coverLetter) return

    const blob = new Blob([coverLetter.content], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `Cover_Letter_${coverLetter.company_name.replace(/\s+/g, '_')}_${coverLetter.job_title.replace(/\s+/g, '_')}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const clearForm = () => {
    setJobTitle('')
    setCompanyName('')
    setJobDescription('')
    setCoverLetter(null)
    setError(null)
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <EnvelopeIcon className="h-6 w-6 mr-2 text-blue-500" />
            Cover Letter Generator
          </h2>
          <p className="text-gray-600 mt-1">
            Generate personalized cover letters for specific job applications
          </p>
        </div>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600">{error}</p>
        </div>
      )}

      {/* Form */}
      <div className="space-y-6 mb-6">
        {/* Job Title */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Job Title *
          </label>
          <input
            type="text"
            value={jobTitle}
            onChange={(e) => setJobTitle(e.target.value)}
            placeholder="e.g., UI/UX Designer"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>

        {/* Company Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Company Name *
          </label>
          <input
            type="text"
            value={companyName}
            onChange={(e) => setCompanyName(e.target.value)}
            placeholder="e.g., Google"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>

        {/* Job Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Job Description (Optional)
          </label>
          <textarea
            value={jobDescription}
            onChange={(e) => setJobDescription(e.target.value)}
            placeholder="Paste the job description here to get a more tailored cover letter..."
            rows={6}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <p className="text-sm text-gray-500 mt-1">
            Adding the job description helps create a more personalized cover letter
          </p>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex space-x-4 mb-6">
        <button
          onClick={generateCoverLetter}
          disabled={loading || !jobTitle.trim() || !companyName.trim()}
          className="flex-1 bg-blue-500 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
        >
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Generating Cover Letter...
            </>
          ) : (
            <>
              <EnvelopeIcon className="h-4 w-4 mr-2" />
              Generate Cover Letter
            </>
          )}
        </button>

        <button
          onClick={clearForm}
          className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50"
        >
          Clear Form
        </button>
      </div>

      {/* Cover Letter Preview */}
      {coverLetter && (
        <div className="border-t pt-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <EyeIcon className="h-5 w-5 mr-2" />
              Cover Letter Preview
            </h3>
            <div className="flex space-x-2">
              <button
                onClick={downloadCoverLetter}
                className="bg-green-500 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-600 flex items-center"
              >
                <DownloadIcon className="h-4 w-4 mr-2" />
                Download
              </button>
            </div>
          </div>

          {/* Job Info */}
          <div className="bg-blue-50 p-4 rounded-lg mb-4">
            <div className="flex flex-wrap gap-4 text-sm">
              <div>
                <span className="font-medium text-blue-900">Position:</span>
                <span className="text-blue-700 ml-1">{coverLetter.job_title}</span>
              </div>
              <div>
                <span className="font-medium text-blue-900">Company:</span>
                <span className="text-blue-700 ml-1">{coverLetter.company_name}</span>
              </div>
              <div>
                <span className="font-medium text-blue-900">Generated:</span>
                <span className="text-blue-700 ml-1">
                  {new Date(coverLetter.generated_at).toLocaleDateString()}
                </span>
              </div>
            </div>
            
            {/* Relevant Skills */}
            {coverLetter.relevant_skills.length > 0 && (
              <div className="mt-3">
                <span className="font-medium text-blue-900 text-sm">Highlighted Skills:</span>
                <div className="flex flex-wrap gap-1 mt-1">
                  {coverLetter.relevant_skills.map((skill, index) => (
                    <span
                      key={index}
                      className="bg-blue-200 text-blue-800 px-2 py-1 rounded text-xs"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Cover Letter Content */}
          <div className="bg-gray-50 p-6 rounded-lg border">
            <div className="whitespace-pre-line text-gray-800 leading-relaxed">
              {coverLetter.content}
            </div>
          </div>

          {/* Tips */}
          <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h4 className="font-medium text-yellow-800 mb-2">💡 Tips for your cover letter:</h4>
            <ul className="text-sm text-yellow-700 space-y-1">
              <li>• Review and customize the content to match your personal writing style</li>
              <li>• Research the company and add specific details about why you want to work there</li>
              <li>• Proofread for any errors before sending</li>
              <li>• Keep it concise - ideally one page</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  )
}
