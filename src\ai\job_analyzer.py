import json
from src.utils.gemini_client import GeminiClient
from src.utils.logger import setup_logger

class JobAnalyzer:
    def __init__(self):
        self.gemini = GeminiClient()
        self.logger = setup_logger('job_analyzer')

    def analyze_job_match(self, job_data, user_profile):
        """Analyze job compatibility and generate application strategy"""
        try:
            # Extract key information from job data
            job_title = job_data.get('title', 'N/A')
            company = job_data.get('company', 'N/A')
            requirements = job_data.get('requirements', job_data.get('description', 'N/A'))
            description = job_data.get('description', 'N/A')

            # Check if this is an AI-related role
            ai_keywords = ['ai', 'artificial intelligence', 'machine learning', 'automation', 'prompt engineering', 'chatgpt', 'generative ai']
            is_ai_role = any(keyword in description.lower() or keyword in requirements.lower() for keyword in ai_keywords)

            prompt = f"""
            Analyze this job for application automation and compatibility:

            Job Title: {job_title}
            Company: {company}
            Job Description: {description}
            Requirements: {requirements}

            User Profile Summary:
            - Name: {user_profile.get('name', 'N/A')}
            - Experience Level: {user_profile.get('experience_level', 'N/A')}
            - Core Skills: {', '.join(user_profile.get('skills', [])[:15])}
            - AI Expertise: {user_profile.get('ai_expertise', {}).get('prompt_engineering', [])}
            - Unique Value Props: {user_profile.get('unique_value_propositions', [])}

            IMPORTANT: This candidate is an AI-Enhanced Designer with strong prompt engineering skills.
            AI Role Detected: {is_ai_role}

            Provide detailed analysis as JSON:
            {{
                "match_score": 85,
                "compatibility_level": "high/medium/low",
                "key_skills_match": ["list", "of", "matching", "skills"],
                "missing_skills": ["skills", "to", "highlight", "or", "learn"],
                "ai_relevance": "high/medium/low/none",
                "application_strategy": "detailed strategy for this specific role",
                "custom_resume_focus": ["areas", "to", "emphasize", "in", "resume"],
                "cover_letter_tone": "professional/enthusiastic/technical/creative",
                "recommended_keywords": ["ats", "keywords", "to", "include"],
                "application_priority": "high/medium/low",
                "estimated_competition": "high/medium/low",
                "salary_match": "within/above/below expectations",
                "next_steps": ["immediate", "action", "items"]
            }}

            Focus on AI capabilities if this is an AI-related role.
            Return ONLY the JSON object, no additional text.
            """

            response = self.gemini.model.generate_content(prompt)
            analysis = self._parse_analysis_response(response.text)

            if analysis:
                # Add computed fields
                analysis['is_ai_role'] = is_ai_role
                analysis['job_id'] = job_data.get('job_id', '')
                analysis['analyzed_at'] = job_data.get('scraped_at', '')
                return analysis
            else:
                return self._get_fallback_analysis(job_data, user_profile, is_ai_role)

        except Exception as e:
            self.logger.error(f"Error analyzing job match: {e}")
            return self._get_fallback_analysis(job_data, user_profile, False)

    def _parse_analysis_response(self, response_text):
        """Parse Gemini response and extract analysis JSON"""
        try:
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1

            if start_idx != -1 and end_idx != -1:
                json_str = response_text[start_idx:end_idx]
                analysis = json.loads(json_str)
                return analysis
            else:
                self.logger.warning("No JSON found in analysis response")
                return None

        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse analysis JSON: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Error parsing analysis response: {e}")
            return None

    def _get_fallback_analysis(self, job_data, user_profile, is_ai_role):
        """Generate fallback analysis if AI analysis fails"""
        return {
            "match_score": 70,
            "compatibility_level": "medium",
            "key_skills_match": ["UI/UX Design", "Figma", "Adobe Creative Suite"],
            "missing_skills": ["Check job requirements"],
            "ai_relevance": "high" if is_ai_role else "low",
            "application_strategy": "Emphasize design skills and AI capabilities if relevant",
            "custom_resume_focus": ["Design Experience", "Technical Skills", "AI Expertise"],
            "cover_letter_tone": "professional",
            "recommended_keywords": ["UI/UX", "Design", "Figma", "Adobe"],
            "application_priority": "medium",
            "estimated_competition": "medium",
            "salary_match": "within expectations",
            "next_steps": ["Customize resume", "Prepare portfolio", "Apply"],
            "is_ai_role": is_ai_role,
            "job_id": job_data.get('job_id', ''),
            "analyzed_at": job_data.get('scraped_at', '')
        }