from flask import Flask, jsonify, request
from flask_cors import CORS
from src.utils.database import JobDatabase
from src.job_searcher import JobSearchManager
from src.utils.logger import setup_logger
from src.ai.job_analyzer import JobAnalyzer
from src.ai.resume_generator import AIResumeGenerator
from src.automation.job_applier import AutoJobApplier
from src.utils.config import Config
from src.utils.gemini_client import GeminiClient
from src.utils.pdf_generator import ProfessionalPDFGenerator
import threading
import time
import asyncio
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from flask import send_file
import io

app = Flask(__name__)
CORS(app)

logger = setup_logger('api_server')
db = JobDatabase()

# Global variable to track search status
search_status = {
    'is_searching': False,
    'last_search_time': None,
    'jobs_found': 0
}

@app.route('/api/jobs', methods=['GET'])
def get_jobs():
    """Get all jobs from database"""
    try:
        jobs = db.get_all_jobs()
        logger.info(f"Database returned {len(jobs)} jobs")
        logger.info(f"API returning {len(jobs)} jobs")
        
        # Debug: log first few jobs
        for i, job in enumerate(jobs[:3]):
            logger.info(f"Job {i+1}: {job.get('title', 'No title')} - {job.get('company', 'No company')}")
        
        return jsonify({
            'success': True,
            'jobs': jobs,
            'count': len(jobs)
        })
    except Exception as e:
        logger.error(f"Error fetching jobs: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/search', methods=['POST'])
def start_search():
    """Start job search in background"""
    try:
        if search_status['is_searching']:
            return jsonify({
                'success': False,
                'message': 'Search already in progress'
            }), 400
        
        def run_search():
            global search_status
            search_status['is_searching'] = True
            search_status['last_search_time'] = time.time()
            
            try:
                # Use real job scraper instead of fake Gemini jobs
                search_manager = JobSearchManager(use_real_jobs=True)
                result = search_manager.start_search()

                if result['success']:
                    search_status['jobs_found'] = search_manager.search_results['jobs_found']
                    logger.info(f"Real job search completed. Found {search_status['jobs_found']} jobs.")
                else:
                    logger.error(f"Search failed: {result['message']}")
                    search_status['jobs_found'] = 0

            except Exception as e:
                logger.error(f"Error during search: {e}")
                search_status['jobs_found'] = 0
            finally:
                search_status['is_searching'] = False
        
        # Run search in background thread
        thread = threading.Thread(target=run_search)
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'success': True,
            'message': 'Job search started'
        })
    except Exception as e:
        logger.error(f"Error starting search: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/search/status', methods=['GET'])
def get_search_status():
    """Get current search status"""
    return jsonify({
        'success': True,
        'status': search_status
    })

@app.route('/api/stats', methods=['GET'])
def get_stats():
    """Get job statistics"""
    try:
        jobs = db.get_all_jobs()
        stats = {
            'total': len(jobs),
            'new': len([j for j in jobs if j.get('status') == 'new']),
            'applied': len([j for j in jobs if j.get('status') == 'applied']),
            'analyzed': len([j for j in jobs if j.get('status') == 'analyzed'])
        }
        return jsonify({
            'success': True,
            'stats': stats
        })
    except Exception as e:
        logger.error(f"Error fetching stats: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/jobs/<job_id>/analyze', methods=['POST'])
def analyze_job(job_id):
    """Analyze job compatibility and generate application strategy"""
    try:
        job = db.get_job_by_id(job_id)
        if not job:
            return jsonify({'success': False, 'error': 'Job not found'}), 404

        logger.info(f"Analyzing job: {job.get('title')} at {job.get('company')}")

        analyzer = JobAnalyzer()
        analysis = analyzer.analyze_job_match(job, Config.USER_PROFILE)

        if analysis:
            # Store analysis in database for future reference
            db.update_job_analysis(job_id, analysis)

            # Generate application recommendations
            recommendations = {
                'should_apply': analysis.get('match_score', 0) >= 60,
                'priority_level': analysis.get('application_priority', 'medium'),
                'estimated_success_rate': _calculate_success_rate(analysis),
                'preparation_time': _estimate_prep_time(analysis),
                'next_actions': analysis.get('next_steps', [])
            }

            return jsonify({
                'success': True,
                'analysis': analysis,
                'recommendations': recommendations,
                'job_info': {
                    'title': job.get('title'),
                    'company': job.get('company'),
                    'source': job.get('source'),
                    'posted_date': job.get('posted_date')
                }
            })
        else:
            return jsonify({'success': False, 'error': 'Failed to analyze job'}), 500

    except Exception as e:
        logger.error(f"Error analyzing job {job_id}: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

def _calculate_success_rate(analysis):
    """Calculate estimated application success rate"""
    match_score = analysis.get('match_score', 0)
    competition = analysis.get('estimated_competition', 'medium')
    ai_relevance = analysis.get('ai_relevance', 'low')

    base_rate = match_score * 0.6  # Base rate from match score

    # Adjust for competition
    if competition == 'low':
        base_rate += 15
    elif competition == 'high':
        base_rate -= 10

    # Boost for AI-relevant roles (candidate's strength)
    if ai_relevance in ['high', 'medium']:
        base_rate += 10

    return min(max(base_rate, 10), 85)  # Cap between 10-85%

def _estimate_prep_time(analysis):
    """Estimate preparation time needed"""
    match_score = analysis.get('match_score', 0)
    missing_skills = len(analysis.get('missing_skills', []))

    if match_score >= 80:
        return "30-45 minutes"
    elif match_score >= 60:
        return "1-2 hours"
    else:
        return "2-4 hours"

@app.route('/api/jobs/analyze-batch', methods=['POST'])
def analyze_jobs_batch():
    """Analyze multiple jobs for compatibility and prioritization"""
    try:
        data = request.json
        job_ids = data.get('jobIds', [])

        if not job_ids:
            return jsonify({'success': False, 'error': 'No job IDs provided'}), 400

        logger.info(f"Analyzing batch of {len(job_ids)} jobs")

        analyzer = JobAnalyzer()
        results = []

        for job_id in job_ids:
            try:
                job = db.get_job_by_id(job_id)
                if job:
                    analysis = analyzer.analyze_job_match(job, Config.USER_PROFILE)
                    if analysis:
                        results.append({
                            'job_id': job_id,
                            'title': job.get('title'),
                            'company': job.get('company'),
                            'match_score': analysis.get('match_score', 0),
                            'priority': analysis.get('application_priority', 'medium'),
                            'ai_relevance': analysis.get('ai_relevance', 'low'),
                            'should_apply': analysis.get('match_score', 0) >= 60,
                            'analysis': analysis
                        })
            except Exception as e:
                logger.warning(f"Failed to analyze job {job_id}: {e}")
                continue

        # Sort by match score and priority
        results.sort(key=lambda x: (
            x['match_score'],
            {'high': 3, 'medium': 2, 'low': 1}.get(x['priority'], 1)
        ), reverse=True)

        # Generate batch recommendations
        high_priority = [r for r in results if r['match_score'] >= 80]
        medium_priority = [r for r in results if 60 <= r['match_score'] < 80]
        ai_relevant = [r for r in results if r['ai_relevance'] in ['high', 'medium']]

        batch_summary = {
            'total_analyzed': len(results),
            'high_priority_count': len(high_priority),
            'medium_priority_count': len(medium_priority),
            'ai_relevant_count': len(ai_relevant),
            'recommended_applications': len([r for r in results if r['should_apply']]),
            'top_matches': results[:5]  # Top 5 matches
        }

        return jsonify({
            'success': True,
            'results': results,
            'summary': batch_summary
        })

    except Exception as e:
        logger.error(f"Error in batch analysis: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/jobs/<job_id>/apply', methods=['POST'])
def auto_apply_job(job_id):
    """Automatically apply to job"""
    try:
        job = db.get_job_by_id(job_id)
        if not job:
            return jsonify({'success': False, 'error': 'Job not found'}), 404

        logger.info(f"Starting auto-apply process for job: {job.get('title')} at {job.get('company')}")

        # Analyze job compatibility first
        analyzer = JobAnalyzer()
        analysis = analyzer.analyze_job_match(job, Config.USER_PROFILE)

        # Generate customized resume based on analysis
        resume_gen = AIResumeGenerator()
        custom_resume = resume_gen.customize_resume(Config.USER_PROFILE, job, analysis)

        # Generate cover letter
        gemini_client = GeminiClient()
        cover_letter = gemini_client.generate_cover_letter(job, Config.USER_PROFILE)

        # Auto apply using asyncio
        applier = AutoJobApplier()
        result = asyncio.run(applier.auto_apply(job, custom_resume, cover_letter))

        # Update job status based on result
        if result.get('success'):
            db.update_job_status(job_id, 'applied')
            status_message = 'Application process completed successfully'
        else:
            db.update_job_status(job_id, 'failed')
            status_message = 'Application process failed'

        return jsonify({
            'success': result.get('success', False),
            'message': status_message,
            'result': result,
            'analysis': analysis,
            'custom_resume_generated': custom_resume is not None
        })
    except Exception as e:
        logger.error(f"Error in auto_apply_job: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/resume/customize', methods=['POST'])
def customize_resume():
    """Generate job-specific resume"""
    try:
        data = request.json
        job_id = data.get('jobId')

        if not job_id:
            return jsonify({'success': False, 'error': 'Job ID is required'}), 400

        job = db.get_job_by_id(job_id)
        if not job:
            return jsonify({'success': False, 'error': 'Job not found'}), 404

        logger.info(f"Customizing resume for job: {job.get('title')} at {job.get('company')}")

        # Analyze job first
        analyzer = JobAnalyzer()
        analysis = analyzer.analyze_job_match(job, Config.USER_PROFILE)

        # Generate customized resume
        resume_gen = AIResumeGenerator()
        customized = resume_gen.customize_resume(Config.USER_PROFILE, job, analysis)

        # Make it ATS-friendly
        if customized:
            job_requirements = job.get('requirements', job.get('description', ''))
            ats_optimized = resume_gen.make_ats_friendly(customized, job_requirements)

            return jsonify({
                'success': True,
                'resume': ats_optimized,
                'analysis': analysis,
                'job_info': {
                    'title': job.get('title'),
                    'company': job.get('company'),
                    'match_score': analysis.get('match_score', 0)
                }
            })
        else:
            return jsonify({'success': False, 'error': 'Failed to generate customized resume'}), 500

    except Exception as e:
        logger.error(f"Error customizing resume: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/resume/generate-pdf', methods=['POST'])
def generate_resume_pdf():
    """Generate professional PDF resume"""
    try:
        data = request.json
        resume_data = data.get('resumeData')
        job_id = data.get('jobId', 'general')

        if not resume_data:
            return jsonify({'success': False, 'error': 'Resume data is required'}), 400

        logger.info(f"Generating PDF resume for job: {job_id}")

        # Create PDF buffer
        buffer = io.BytesIO()
        pdf_generator = ProfessionalPDFGenerator(buffer)

        # Generate the PDF
        pdf_generator.create_resume_pdf(resume_data)
        buffer.seek(0)

        # Determine filename
        candidate_name = resume_data.get('contact_info', {}).get('name', 'Candidate')
        job_title = resume_data.get('customized_for', job_id)
        filename = f"{candidate_name.replace(' ', '_')}_Resume_{job_title.replace(' ', '_')}.pdf"

        return send_file(
            buffer,
            as_attachment=True,
            download_name=filename,
            mimetype='application/pdf'
        )

    except Exception as e:
        logger.error(f"Error generating PDF resume: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

if __name__ == '__main__':
    logger.info("Starting Job AI API Server...")
    app.run(host='0.0.0.0', port=8000, debug=True)







