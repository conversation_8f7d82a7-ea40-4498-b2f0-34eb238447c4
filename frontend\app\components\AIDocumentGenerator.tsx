'use client'

import { useState } from 'react'
import { 
  SparklesIcon, 
  DocumentTextIcon, 
  EnvelopeIcon, 
  DownloadIcon,
  ClipboardDocumentIcon,
  EyeIcon,
  UserIcon
} from '@heroicons/react/24/outline'

interface JobInfo {
  job_title: string
  company_name: string
  job_description: string
  key_requirements: string[]
  key_skills: string[]
  location: string
  job_type: string
}

interface GeneratedDocuments {
  job_info: JobInfo
  cover_letter: string
  resume: string
  generated_at: string
}

export default function AIDocumentGenerator() {
  const [jobText, setJobText] = useState('')
  const [loading, setLoading] = useState(false)
  const [documents, setDocuments] = useState<GeneratedDocuments | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [activeView, setActiveView] = useState<'cover_letter' | 'resume'>('cover_letter')

  const generateDocuments = async () => {
    if (!jobText.trim()) {
      setError('Please paste a job posting')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ job_text: jobText.trim() })
      })

      const data = await response.json()

      if (data.success) {
        setDocuments(data)
      } else {
        setError(data.error || 'Failed to generate documents')
      }
    } catch (error) {
      console.error('Error generating documents:', error)
      setError('Network error while generating documents')
    } finally {
      setLoading(false)
    }
  }

  const downloadPDF = async (type: 'cover_letter' | 'resume') => {
    if (!documents) return

    try {
      const content = type === 'cover_letter' ? documents.cover_letter : documents.resume
      
      const response = await fetch('/api/generate-pdf', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: content,
          type: type,
          job_title: documents.job_info.job_title,
          company_name: documents.job_info.company_name
        })
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `${type}_${documents.job_info.company_name}_${documents.job_info.job_title}.pdf`.replace(/\s+/g, '_')
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        window.URL.revokeObjectURL(url)
      } else {
        setError('Failed to generate PDF')
      }
    } catch (error) {
      console.error('Error downloading PDF:', error)
      setError('Error downloading PDF')
    }
  }

  const clearAll = () => {
    setJobText('')
    setDocuments(null)
    setError(null)
  }

  return (
    <div className="space-y-8">
      {/* Input Section */}
      <div className="bg-white rounded-xl shadow-lg border p-8">
        <div className="flex items-center mb-6">
          <ClipboardDocumentIcon className="h-8 w-8 text-blue-500 mr-3" />
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Paste Job Posting</h2>
            <p className="text-gray-600">Copy and paste the entire job posting text below</p>
          </div>
        </div>

        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        <div className="space-y-4">
          <textarea
            value={jobText}
            onChange={(e) => setJobText(e.target.value)}
            placeholder="Paste the complete job posting here including:
• Job title
• Company name  
• Job description
• Requirements
• Qualifications
• Any other details from the job posting..."
            rows={12}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
          />
          
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-500">
              {jobText.length} characters • Paste complete job posting for best results
            </div>
            
            <div className="flex space-x-3">
              <button
                onClick={clearAll}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                Clear All
              </button>
              
              <button
                onClick={generateDocuments}
                disabled={loading || !jobText.trim()}
                className="px-8 py-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-lg font-medium hover:from-blue-600 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Generating with AI...
                  </>
                ) : (
                  <>
                    <SparklesIcon className="h-4 w-4 mr-2" />
                    Generate Documents
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Results Section */}
      {documents && (
        <div className="space-y-6">
          {/* Job Info Summary */}
          <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-xl border p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <UserIcon className="h-5 w-5 mr-2 text-green-600" />
              Extracted Job Information
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700">Position:</span>
                <p className="text-gray-900">{documents.job_info.job_title}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700">Company:</span>
                <p className="text-gray-900">{documents.job_info.company_name}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700">Location:</span>
                <p className="text-gray-900">{documents.job_info.location}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700">Job Type:</span>
                <p className="text-gray-900">{documents.job_info.job_type}</p>
              </div>
              <div className="md:col-span-2">
                <span className="font-medium text-gray-700">Key Skills:</span>
                <div className="flex flex-wrap gap-1 mt-1">
                  {documents.job_info.key_skills.slice(0, 8).map((skill, index) => (
                    <span key={index} className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Document Tabs */}
          <div className="bg-white rounded-xl shadow-lg border">
            <div className="border-b border-gray-200">
              <div className="flex">
                <button
                  onClick={() => setActiveView('cover_letter')}
                  className={`flex-1 px-6 py-4 text-center font-medium transition-colors ${
                    activeView === 'cover_letter'
                      ? 'bg-blue-50 text-blue-600 border-b-2 border-blue-500'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <EnvelopeIcon className="h-5 w-5 inline mr-2" />
                  Cover Letter
                </button>
                <button
                  onClick={() => setActiveView('resume')}
                  className={`flex-1 px-6 py-4 text-center font-medium transition-colors ${
                    activeView === 'resume'
                      ? 'bg-blue-50 text-blue-600 border-b-2 border-blue-500'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <DocumentTextIcon className="h-5 w-5 inline mr-2" />
                  ATS Resume
                </button>
              </div>
            </div>

            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                  <EyeIcon className="h-5 w-5 mr-2" />
                  {activeView === 'cover_letter' ? 'AI-Generated Cover Letter' : 'ATS-Optimized Resume'}
                </h3>
                
                <button
                  onClick={() => downloadPDF(activeView)}
                  className="bg-green-500 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-600 flex items-center"
                >
                  <DownloadIcon className="h-4 w-4 mr-2" />
                  Download PDF
                </button>
              </div>

              <div className="bg-gray-50 rounded-lg p-6 border">
                <div className="whitespace-pre-wrap text-gray-800 leading-relaxed font-mono text-sm">
                  {activeView === 'cover_letter' ? documents.cover_letter : documents.resume}
                </div>
              </div>

              {/* Tips */}
              <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <h4 className="font-medium text-yellow-800 mb-2">
                  💡 {activeView === 'cover_letter' ? 'Cover Letter Tips:' : 'Resume Tips:'}
                </h4>
                <ul className="text-sm text-yellow-700 space-y-1">
                  {activeView === 'cover_letter' ? (
                    <>
                      <li>• Review and personalize the content to match your writing style</li>
                      <li>• Add specific examples from your experience that relate to the role</li>
                      <li>• Research the company culture and add relevant details</li>
                      <li>• Proofread carefully before sending</li>
                    </>
                  ) : (
                    <>
                      <li>• This resume is optimized for ATS (Applicant Tracking Systems)</li>
                      <li>• Keywords from the job posting have been naturally integrated</li>
                      <li>• Review and adjust any details to ensure accuracy</li>
                      <li>• Save as PDF to preserve formatting when submitting</li>
                    </>
                  )}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Profile Info */}
      <div className="bg-white rounded-xl shadow-lg border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          📋 Your Profile Information
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium text-gray-700">Name:</span>
            <p className="text-gray-900">Tishbian Meshach S</p>
          </div>
          <div>
            <span className="font-medium text-gray-700">Location:</span>
            <p className="text-gray-900">Tuticorin, Tamil Nadu, India</p>
          </div>
          <div>
            <span className="font-medium text-gray-700">Experience:</span>
            <p className="text-gray-900">AI-Enhanced Designer with 4+ years</p>
          </div>
          <div>
            <span className="font-medium text-gray-700">Specialization:</span>
            <p className="text-gray-900">UI/UX Design + AI Automation</p>
          </div>
        </div>
        <p className="text-xs text-gray-500 mt-4">
          Profile details are loaded from config.py and used to personalize your documents
        </p>
      </div>
    </div>
  )
}
