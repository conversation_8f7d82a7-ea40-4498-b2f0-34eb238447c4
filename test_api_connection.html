<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .success { color: #22c55e; }
        .error { color: #ef4444; }
        .loading { color: #3b82f6; }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2563eb;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            max-height: 300px;
        }
    </style>
</head>
<body>
    <h1>Job AI - API Connection Test</h1>
    
    <div class="test-container">
        <h2>Backend Server Test</h2>
        <p>Testing direct connection to Flask backend (port 8000)</p>
        <button onclick="testBackendDirect()">Test Backend Direct</button>
        <div id="backend-result"></div>
    </div>

    <div class="test-container">
        <h2>Frontend Proxy Test</h2>
        <p>Testing API calls through Next.js proxy (port 3000)</p>
        <button onclick="testFrontendProxy()">Test Frontend Proxy</button>
        <div id="proxy-result"></div>
    </div>

    <div class="test-container">
        <h2>API Endpoints Test</h2>
        <p>Testing various API endpoints</p>
        <button onclick="testJobs()">Test /api/jobs</button>
        <button onclick="testStats()">Test /api/stats</button>
        <button onclick="testSearch()">Test /api/search/status</button>
        <div id="endpoints-result"></div>
    </div>

    <script>
        async function testBackendDirect() {
            const resultDiv = document.getElementById('backend-result');
            resultDiv.innerHTML = '<p class="loading">Testing direct backend connection...</p>';
            
            try {
                const response = await fetch('http://localhost:8000/api/jobs');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <p class="success">✅ Backend connection successful!</p>
                        <p>Found ${data.jobs?.length || 0} jobs</p>
                        <pre>${JSON.stringify(data, null, 2).substring(0, 500)}...</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `<p class="error">❌ Backend error: ${response.status}</p>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ Backend connection failed: ${error.message}</p>`;
            }
        }

        async function testFrontendProxy() {
            const resultDiv = document.getElementById('proxy-result');
            resultDiv.innerHTML = '<p class="loading">Testing frontend proxy...</p>';
            
            try {
                const response = await fetch('/api/jobs');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <p class="success">✅ Frontend proxy working!</p>
                        <p>Found ${data.jobs?.length || 0} jobs</p>
                        <pre>${JSON.stringify(data, null, 2).substring(0, 500)}...</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `<p class="error">❌ Proxy error: ${response.status}</p>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ Proxy connection failed: ${error.message}</p>`;
            }
        }

        async function testJobs() {
            await testEndpoint('/api/jobs', 'Jobs');
        }

        async function testStats() {
            await testEndpoint('/api/stats', 'Stats');
        }

        async function testSearch() {
            await testEndpoint('/api/search/status', 'Search Status');
        }

        async function testEndpoint(endpoint, name) {
            const resultDiv = document.getElementById('endpoints-result');
            resultDiv.innerHTML += `<p class="loading">Testing ${name} endpoint...</p>`;
            
            try {
                const response = await fetch(endpoint);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML += `
                        <p class="success">✅ ${name} endpoint working!</p>
                        <pre>${JSON.stringify(data, null, 2).substring(0, 300)}...</pre>
                    `;
                } else {
                    resultDiv.innerHTML += `<p class="error">❌ ${name} endpoint error: ${response.status}</p>`;
                }
            } catch (error) {
                resultDiv.innerHTML += `<p class="error">❌ ${name} endpoint failed: ${error.message}</p>`;
            }
        }

        // Auto-run tests when page loads
        window.onload = function() {
            console.log('API Connection Test Page Loaded');
        };
    </script>
</body>
</html>
