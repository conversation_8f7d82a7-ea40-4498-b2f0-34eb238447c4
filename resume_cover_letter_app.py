#!/usr/bin/env python3
"""
Resume and Cover Letter Generator Application
A focused app for generating personalized resumes and cover letters
"""

from flask import Flask, request, jsonify, render_template_string
from flask_cors import CORS
import json
import os
from datetime import datetime
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Sample user profile data
DEFAULT_PROFILE = {
    "personal_info": {
        "name": "Tishbian Meshach S",
        "email": "<EMAIL>",
        "phone": "+91 9876543210",
        "location": "Chennai, India",
        "linkedin": "linkedin.com/in/tishbian-meshach",
        "portfolio": "tishbian-portfolio.com"
    },
    "summary": "Passionate UI/UX Designer with expertise in creating user-centered designs and modern web interfaces. Skilled in Figma, Adobe Creative Suite, and front-end development.",
    "skills": [
        "UI/UX Design", "Figma", "Adobe XD", "Photoshop", "Illustrator",
        "HTML/CSS", "JavaScript", "React", "Wireframing", "Prototyping",
        "User Research", "Design Systems", "Responsive Design"
    ],
    "experience": [
        {
            "title": "UI/UX Design Intern",
            "company": "Tech Solutions Pvt Ltd",
            "duration": "Jun 2024 - Present",
            "location": "Chennai, India",
            "responsibilities": [
                "Designed user interfaces for web and mobile applications",
                "Conducted user research and created user personas",
                "Collaborated with development team to implement designs",
                "Created wireframes and interactive prototypes using Figma"
            ]
        }
    ],
    "education": [
        {
            "degree": "Bachelor of Technology in Computer Science",
            "institution": "Anna University",
            "year": "2022-2026",
            "location": "Chennai, India",
            "gpa": "8.5/10"
        }
    ],
    "projects": [
        {
            "name": "E-commerce Mobile App Design",
            "description": "Designed a complete mobile app interface for an e-commerce platform",
            "technologies": ["Figma", "Adobe XD", "Prototyping"],
            "duration": "3 months"
        },
        {
            "name": "Restaurant Website Redesign",
            "description": "Redesigned a restaurant website focusing on user experience and mobile responsiveness",
            "technologies": ["HTML", "CSS", "JavaScript", "Figma"],
            "duration": "2 months"
        }
    ]
}

class ResumeGenerator:
    def __init__(self):
        self.profile = DEFAULT_PROFILE
    
    def generate_resume(self, template_style="modern"):
        """Generate a resume based on profile data"""
        try:
            resume_data = {
                "personal_info": self.profile["personal_info"],
                "summary": self.profile["summary"],
                "skills": self.profile["skills"],
                "experience": self.profile["experience"],
                "education": self.profile["education"],
                "projects": self.profile["projects"],
                "template_style": template_style,
                "generated_at": datetime.now().isoformat()
            }
            
            return {
                "success": True,
                "resume": resume_data,
                "message": f"Resume generated successfully with {template_style} template"
            }
            
        except Exception as e:
            logger.error(f"Error generating resume: {e}")
            return {
                "success": False,
                "error": str(e)
            }

class CoverLetterGenerator:
    def __init__(self):
        self.profile = DEFAULT_PROFILE
    
    def generate_cover_letter(self, job_title, company_name, job_description=""):
        """Generate a personalized cover letter"""
        try:
            # Extract relevant skills based on job description
            relevant_skills = self._extract_relevant_skills(job_description)
            
            # Generate cover letter content
            cover_letter = self._create_cover_letter_content(
                job_title, company_name, job_description, relevant_skills
            )
            
            return {
                "success": True,
                "cover_letter": {
                    "content": cover_letter,
                    "job_title": job_title,
                    "company_name": company_name,
                    "relevant_skills": relevant_skills,
                    "generated_at": datetime.now().isoformat()
                },
                "message": "Cover letter generated successfully"
            }
            
        except Exception as e:
            logger.error(f"Error generating cover letter: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _extract_relevant_skills(self, job_description):
        """Extract relevant skills from job description"""
        if not job_description:
            return self.profile["skills"][:5]  # Return top 5 skills
        
        job_desc_lower = job_description.lower()
        relevant_skills = []
        
        for skill in self.profile["skills"]:
            if skill.lower() in job_desc_lower:
                relevant_skills.append(skill)
        
        # If no matches, return top skills
        if not relevant_skills:
            relevant_skills = self.profile["skills"][:5]
        
        return relevant_skills[:8]  # Limit to 8 skills
    
    def _create_cover_letter_content(self, job_title, company_name, job_description, relevant_skills):
        """Create the actual cover letter content"""
        personal_info = self.profile["personal_info"]
        
        cover_letter = f"""Dear Hiring Manager,

I am writing to express my strong interest in the {job_title} position at {company_name}. As a passionate UI/UX Designer with hands-on experience in creating user-centered designs, I am excited about the opportunity to contribute to your team.

{self.profile["summary"]}

My technical skills include {', '.join(relevant_skills[:5])}, which align perfectly with the requirements for this role. In my recent internship at Tech Solutions Pvt Ltd, I successfully designed user interfaces for web and mobile applications, conducted user research, and collaborated closely with development teams to implement designs.

Key highlights of my experience:
• Designed user interfaces for web and mobile applications
• Conducted user research and created user personas
• Created wireframes and interactive prototypes using Figma
• Collaborated with development teams to implement designs

I am particularly drawn to {company_name} because of your commitment to innovative design and user experience. I would welcome the opportunity to discuss how my skills and passion for design can contribute to your team's success.

Thank you for considering my application. I look forward to hearing from you.

Best regards,
{personal_info["name"]}
{personal_info["email"]}
{personal_info["phone"]}"""

        return cover_letter

# Initialize generators
resume_generator = ResumeGenerator()
cover_letter_generator = CoverLetterGenerator()

# API Routes
@app.route('/')
def home():
    """Home page"""
    return jsonify({
        "message": "Resume and Cover Letter Generator API",
        "endpoints": {
            "generate_resume": "/api/resume/generate",
            "generate_cover_letter": "/api/cover-letter/generate",
            "get_profile": "/api/profile",
            "update_profile": "/api/profile/update"
        }
    })

@app.route('/api/profile', methods=['GET'])
def get_profile():
    """Get user profile"""
    return jsonify({
        "success": True,
        "profile": DEFAULT_PROFILE
    })

@app.route('/api/profile/update', methods=['POST'])
def update_profile():
    """Update user profile"""
    try:
        data = request.get_json()
        # In a real app, you'd save this to a database
        # For now, we'll just return success
        return jsonify({
            "success": True,
            "message": "Profile updated successfully"
        })
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 400

@app.route('/api/resume/generate', methods=['POST'])
def generate_resume():
    """Generate a resume"""
    try:
        data = request.get_json() or {}
        template_style = data.get('template_style', 'modern')
        
        result = resume_generator.generate_resume(template_style)
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error in generate_resume: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/cover-letter/generate', methods=['POST'])
def generate_cover_letter():
    """Generate a cover letter"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                "success": False,
                "error": "Request data is required"
            }), 400
        
        job_title = data.get('job_title', '')
        company_name = data.get('company_name', '')
        job_description = data.get('job_description', '')
        
        if not job_title or not company_name:
            return jsonify({
                "success": False,
                "error": "job_title and company_name are required"
            }), 400
        
        result = cover_letter_generator.generate_cover_letter(
            job_title, company_name, job_description
        )
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error in generate_cover_letter: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

if __name__ == '__main__':
    print("🚀 Starting Resume and Cover Letter Generator")
    print("📝 Available endpoints:")
    print("   - GET  /api/profile")
    print("   - POST /api/profile/update")
    print("   - POST /api/resume/generate")
    print("   - POST /api/cover-letter/generate")
    print()
    
    app.run(debug=True, host='0.0.0.0', port=5000)
