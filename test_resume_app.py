#!/usr/bin/env python3
"""
Test script for the Resume and Cover Letter Generator
"""

import requests
import json

BASE_URL = "http://localhost:5000"

def test_profile_endpoint():
    """Test the profile endpoint"""
    print("🧪 Testing Profile Endpoint")
    print("-" * 40)
    
    try:
        response = requests.get(f"{BASE_URL}/api/profile")
        data = response.json()
        
        if response.status_code == 200 and data['success']:
            print("✅ Profile endpoint working")
            print(f"   Name: {data['profile']['personal_info']['name']}")
            print(f"   Skills: {len(data['profile']['skills'])} skills")
            print(f"   Experience: {len(data['profile']['experience'])} entries")
        else:
            print("❌ Profile endpoint failed")
            print(f"   Error: {data.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Error testing profile: {e}")

def test_resume_generation():
    """Test resume generation"""
    print("\n🧪 Testing Resume Generation")
    print("-" * 40)
    
    try:
        payload = {
            "template_style": "modern"
        }
        
        response = requests.post(
            f"{BASE_URL}/api/resume/generate",
            json=payload,
            headers={'Content-Type': 'application/json'}
        )
        
        data = response.json()
        
        if response.status_code == 200 and data['success']:
            print("✅ Resume generation working")
            resume = data['resume']
            print(f"   Template: {resume['template_style']}")
            print(f"   Name: {resume['personal_info']['name']}")
            print(f"   Skills: {len(resume['skills'])} skills")
            print(f"   Generated: {resume['generated_at']}")
        else:
            print("❌ Resume generation failed")
            print(f"   Error: {data.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Error testing resume generation: {e}")

def test_cover_letter_generation():
    """Test cover letter generation"""
    print("\n🧪 Testing Cover Letter Generation")
    print("-" * 40)
    
    try:
        payload = {
            "job_title": "UI/UX Designer",
            "company_name": "Google",
            "job_description": "We are looking for a talented UI/UX Designer with experience in Figma, user research, and prototyping to join our design team."
        }
        
        response = requests.post(
            f"{BASE_URL}/api/cover-letter/generate",
            json=payload,
            headers={'Content-Type': 'application/json'}
        )
        
        data = response.json()
        
        if response.status_code == 200 and data['success']:
            print("✅ Cover letter generation working")
            cover_letter = data['cover_letter']
            print(f"   Job Title: {cover_letter['job_title']}")
            print(f"   Company: {cover_letter['company_name']}")
            print(f"   Relevant Skills: {', '.join(cover_letter['relevant_skills'][:3])}...")
            print(f"   Content Length: {len(cover_letter['content'])} characters")
            print(f"   Generated: {cover_letter['generated_at']}")
            
            # Show first few lines of cover letter
            lines = cover_letter['content'].split('\n')[:3]
            print(f"   Preview: {lines[0][:50]}...")
            
        else:
            print("❌ Cover letter generation failed")
            print(f"   Error: {data.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Error testing cover letter generation: {e}")

def test_cover_letter_without_description():
    """Test cover letter generation without job description"""
    print("\n🧪 Testing Cover Letter Without Job Description")
    print("-" * 40)
    
    try:
        payload = {
            "job_title": "Product Designer",
            "company_name": "Microsoft"
        }
        
        response = requests.post(
            f"{BASE_URL}/api/cover-letter/generate",
            json=payload,
            headers={'Content-Type': 'application/json'}
        )
        
        data = response.json()
        
        if response.status_code == 200 and data['success']:
            print("✅ Cover letter without description working")
            cover_letter = data['cover_letter']
            print(f"   Job Title: {cover_letter['job_title']}")
            print(f"   Company: {cover_letter['company_name']}")
            print(f"   Default Skills Used: {', '.join(cover_letter['relevant_skills'][:3])}...")
        else:
            print("❌ Cover letter without description failed")
            print(f"   Error: {data.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Error testing cover letter without description: {e}")

def test_error_handling():
    """Test error handling"""
    print("\n🧪 Testing Error Handling")
    print("-" * 40)
    
    try:
        # Test missing required fields
        payload = {
            "job_title": "Designer"
            # Missing company_name
        }
        
        response = requests.post(
            f"{BASE_URL}/api/cover-letter/generate",
            json=payload,
            headers={'Content-Type': 'application/json'}
        )
        
        data = response.json()
        
        if response.status_code == 400 and not data['success']:
            print("✅ Error handling working")
            print(f"   Error message: {data['error']}")
        else:
            print("❌ Error handling not working properly")
            
    except Exception as e:
        print(f"❌ Error testing error handling: {e}")

def main():
    """Main test function"""
    print("🚀 RESUME & COVER LETTER GENERATOR TESTS")
    print("=" * 50)
    print("Make sure the server is running: python resume_cover_letter_app.py")
    print()
    
    # Test all endpoints
    test_profile_endpoint()
    test_resume_generation()
    test_cover_letter_generation()
    test_cover_letter_without_description()
    test_error_handling()
    
    print("\n" + "=" * 50)
    print("🎉 TESTING COMPLETE")
    print("=" * 50)
    print("✅ If all tests passed, the backend is working correctly!")
    print("🌐 Start the frontend with: cd frontend && npm run dev")

if __name__ == "__main__":
    main()
