/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZwYWdlJnBhZ2U9JTJGcGFnZSZhcHBQYXRocz0lMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGcGFnZS50c3gmYXBwRGlyPUMlM0ElNUNVc2VycyU1Q3d3d3N0JTVDT25lRHJpdmUlNUNEZXNrdG9wJTVDam9iLWFpJTVDZnJvbnRlbmQlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNVc2VycyU1Q3d3d3N0JTVDT25lRHJpdmUlNUNEZXNrdG9wJTVDam9iLWFpJTVDZnJvbnRlbmQmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxhQUFhLHNCQUFzQjtBQUNpRTtBQUNyQztBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakMsdUJBQXVCLHdJQUF5RztBQUNoSTtBQUNBLFNBQVM7QUFDVCxPQUFPO0FBQ1A7QUFDQSx5QkFBeUIsNElBQTJHO0FBQ3BJLG9CQUFvQiwwTkFBZ0Y7QUFDcEc7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ3VCO0FBQzZEO0FBQ3BGLDZCQUE2QixtQkFBbUI7QUFDaEQ7QUFDTztBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ3VEO0FBQ3ZEO0FBQ08sd0JBQXdCLDhHQUFrQjtBQUNqRDtBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9qb2ItYWktZnJvbnRlbmQvP2EwZTEiXSwic291cmNlc0NvbnRlbnQiOlsiXCJUVVJCT1BBQ0sgeyB0cmFuc2l0aW9uOiBuZXh0LXNzciB9XCI7XG5pbXBvcnQgeyBBcHBQYWdlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcGFnZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogWydfX1BBR0VfXycsIHt9LCB7XG4gICAgICAgICAgcGFnZTogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcd3d3c3RcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxqb2ItYWlcXFxcZnJvbnRlbmRcXFxcYXBwXFxcXHBhZ2UudHN4XCIpLCBcIkM6XFxcXFVzZXJzXFxcXHd3d3N0XFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcam9iLWFpXFxcXGZyb250ZW5kXFxcXGFwcFxcXFxwYWdlLnRzeFwiXSxcbiAgICAgICAgICBcbiAgICAgICAgfV1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgJ2xheW91dCc6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHd3d3N0XFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcam9iLWFpXFxcXGZyb250ZW5kXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIpLCBcIkM6XFxcXFVzZXJzXFxcXHd3d3N0XFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcam9iLWFpXFxcXGZyb250ZW5kXFxcXGFwcFxcXFxsYXlvdXQudHN4XCJdLFxuJ25vdC1mb3VuZCc6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIiksIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiXSxcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LmNoaWxkcmVuO1xuY29uc3QgcGFnZXMgPSBbXCJDOlxcXFxVc2Vyc1xcXFx3d3dzdFxcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXGpvYi1haVxcXFxmcm9udGVuZFxcXFxhcHBcXFxccGFnZS50c3hcIl07XG5leHBvcnQgeyB0cmVlLCBwYWdlcyB9O1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBHbG9iYWxFcnJvciB9IGZyb20gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnlcIjtcbmNvbnN0IF9fbmV4dF9hcHBfcmVxdWlyZV9fID0gX193ZWJwYWNrX3JlcXVpcmVfX1xuY29uc3QgX19uZXh0X2FwcF9sb2FkX2NodW5rX18gPSAoKSA9PiBQcm9taXNlLnJlc29sdmUoKVxuZXhwb3J0IGNvbnN0IG9yaWdpbmFsUGF0aG5hbWUgPSBcIi9wYWdlXCI7XG5leHBvcnQgY29uc3QgX19uZXh0X2FwcF9fID0ge1xuICAgIHJlcXVpcmU6IF9fbmV4dF9hcHBfcmVxdWlyZV9fLFxuICAgIGxvYWRDaHVuazogX19uZXh0X2FwcF9sb2FkX2NodW5rX19cbn07XG5leHBvcnQgKiBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL2VudHJ5LWJhc2VcIjtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFBhZ2VSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1BBR0UsXG4gICAgICAgIHBhZ2U6IFwiL3BhZ2VcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL1wiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcIlwiLFxuICAgICAgICBhcHBQYXRoczogW11cbiAgICB9LFxuICAgIHVzZXJsYW5kOiB7XG4gICAgICAgIGxvYWRlclRyZWU6IHRyZWVcbiAgICB9XG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXBhZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Capp%5Ccomponents%5CJobDashboard.tsx&server=true!":
/*!******************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Capp%5Ccomponents%5CJobDashboard.tsx&server=true! ***!
  \******************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/JobDashboard.tsx */ \"(ssr)/./app/components/JobDashboard.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDd3d3c3QlNUNPbmVEcml2ZSU1Q0Rlc2t0b3AlNUNqb2ItYWklNUNmcm9udGVuZCU1Q2FwcCU1Q2NvbXBvbmVudHMlNUNKb2JEYXNoYm9hcmQudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2pvYi1haS1mcm9udGVuZC8/Y2QyMSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHd3d3N0XFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcam9iLWFpXFxcXGZyb250ZW5kXFxcXGFwcFxcXFxjb21wb25lbnRzXFxcXEpvYkRhc2hib2FyZC50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Capp%5Ccomponents%5CJobDashboard.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Capp%5Cglobals.css&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Capp%5Cglobals.css&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/components/JobCard.tsx":
/*!************************************!*\
  !*** ./app/components/JobCard.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ JobCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CogIcon_LinkIcon_MapPinIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CalendarIcon,CogIcon,LinkIcon,MapPinIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BuildingOfficeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CogIcon_LinkIcon_MapPinIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CalendarIcon,CogIcon,LinkIcon,MapPinIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CogIcon_LinkIcon_MapPinIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CalendarIcon,CogIcon,LinkIcon,MapPinIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CogIcon_LinkIcon_MapPinIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CalendarIcon,CogIcon,LinkIcon,MapPinIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CogIcon_LinkIcon_MapPinIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CalendarIcon,CogIcon,LinkIcon,MapPinIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CogIcon_LinkIcon_MapPinIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CalendarIcon,CogIcon,LinkIcon,MapPinIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/LinkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction JobCard({ job, onJobUpdate }) {\n    const [applying, setApplying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [analyzing, setAnalyzing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [analysis, setAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showAnalysis, setShowAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const analyzeJob = async ()=>{\n        setAnalyzing(true);\n        try {\n            const response = await fetch(`/api/jobs/${job.job_id}/analyze`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            const data = await response.json();\n            if (data.success) {\n                setAnalysis(data.analysis);\n                setShowAnalysis(true);\n                // Update parent component if callback provided\n                onJobUpdate?.(job.job_id, {\n                    status: \"analyzed\",\n                    match_score: data.analysis.match_score\n                });\n            } else {\n                console.error(\"Analysis failed:\", data.error);\n            }\n        } catch (error) {\n            console.error(\"Error analyzing job:\", error);\n        } finally{\n            setAnalyzing(false);\n        }\n    };\n    const autoApply = async ()=>{\n        setApplying(true);\n        try {\n            const response = await fetch(`/api/jobs/${job.job_id}/apply`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            const result = await response.json();\n            if (result.success) {\n                // Show success message (you might want to add a toast library)\n                alert(\"Application submitted successfully!\");\n                onJobUpdate?.(job.job_id, {\n                    status: \"applied\"\n                });\n            } else {\n                alert(`Application failed: ${result.error || \"Unknown error\"}`);\n            }\n        } catch (error) {\n            console.error(\"Error applying to job:\", error);\n            alert(\"Error submitting application\");\n        } finally{\n            setApplying(false);\n        }\n    };\n    const customizeResume = async ()=>{\n        try {\n            const response = await fetch(\"/api/resume/customize\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    jobId: job.job_id\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                // Open resume in new tab or download\n                const pdfResponse = await fetch(\"/api/resume/generate-pdf\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        resumeData: data.resume,\n                        jobId: job.job_id\n                    })\n                });\n                if (pdfResponse.ok) {\n                    const blob = await pdfResponse.blob();\n                    const url = window.URL.createObjectURL(blob);\n                    const a = document.createElement(\"a\");\n                    a.href = url;\n                    a.download = `resume-${job.company}-${job.title}.pdf`;\n                    a.click();\n                    window.URL.revokeObjectURL(url);\n                }\n            }\n        } catch (error) {\n            console.error(\"Error customizing resume:\", error);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"new\":\n                return \"bg-green-100 text-green-800\";\n            case \"applied\":\n                return \"bg-blue-100 text-blue-800\";\n            case \"analyzed\":\n                return \"bg-yellow-100 text-yellow-800\";\n            case \"failed\":\n                return \"bg-red-100 text-red-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    const getMatchScoreColor = (score)=>{\n        if (score >= 80) return \"text-green-600 font-semibold\";\n        if (score >= 60) return \"text-yellow-600 font-medium\";\n        return \"text-red-600\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card hover:shadow-lg transition-shadow\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-start mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                children: job.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 text-sm text-gray-600 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CogIcon_LinkIcon_MapPinIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 15\n                                            }, this),\n                                            job.company\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CogIcon_LinkIcon_MapPinIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 15\n                                            }, this),\n                                            job.location\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CogIcon_LinkIcon_MapPinIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 15\n                                            }, this),\n                                            job.posted_date\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-end space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: `px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(job.status)}`,\n                                children: job.status\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-500\",\n                                children: [\n                                    \"Source: \",\n                                    job.source\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-700 mb-4 line-clamp-3\",\n                children: job.description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this),\n            showAnalysis && analysis && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-semibold text-blue-900 mb-2\",\n                        children: \"AI Analysis Results\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Match Score:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `ml-2 ${getMatchScoreColor(analysis.match_score)}`,\n                                        children: [\n                                            analysis.match_score,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Priority:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 capitalize\",\n                                        children: analysis.application_priority\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"AI Relevance:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 capitalize\",\n                                        children: analysis.ai_relevance\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Competition:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 capitalize\",\n                                        children: analysis.estimated_competition\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this),\n                    analysis.key_skills_match && analysis.key_skills_match.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-sm\",\n                                children: \"Matching Skills:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-1 mt-1\",\n                                children: analysis.key_skills_match.slice(0, 5).map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 bg-green-100 text-green-800 text-xs rounded\",\n                                        children: skill\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 13\n                    }, this),\n                    analysis.application_strategy && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-sm\",\n                                children: \"Strategy:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-700 mt-1\",\n                                children: analysis.application_strategy\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                lineNumber: 168,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            job.match_score > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: `text-sm ${getMatchScoreColor(job.match_score)}`,\n                                children: [\n                                    \"Match: \",\n                                    job.match_score,\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 13\n                            }, this),\n                            analysis && analysis.is_ai_role && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full\",\n                                children: \"AI Role\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: analyzeJob,\n                                disabled: analyzing,\n                                className: \"btn-secondary flex items-center text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CogIcon_LinkIcon_MapPinIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 13\n                                    }, this),\n                                    analyzing ? \"Analyzing...\" : \"AI Analyze\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: customizeResume,\n                                className: \"btn-secondary flex items-center text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CogIcon_LinkIcon_MapPinIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Custom Resume\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: autoApply,\n                                disabled: applying || job.status === \"applied\",\n                                className: \"btn-primary flex items-center text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CogIcon_LinkIcon_MapPinIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 13\n                                    }, this),\n                                    applying ? \"Applying...\" : job.status === \"applied\" ? \"Applied\" : \"Auto Apply\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this),\n                            job.apply_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: job.apply_url,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"btn-outline flex items-center text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CogIcon_LinkIcon_MapPinIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Manual Apply\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/JobCard.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/JobDashboard.tsx":
/*!*****************************************!*\
  !*** ./app/components/JobDashboard.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ JobDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _JobCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./JobCard */ \"(ssr)/./app/components/JobCard.tsx\");\n/* harmony import */ var _SearchForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SearchForm */ \"(ssr)/./app/components/SearchForm.tsx\");\n/* harmony import */ var _StatsCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./StatsCard */ \"(ssr)/./app/components/StatsCard.tsx\");\n/* harmony import */ var _ResumeBuilder__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ResumeBuilder */ \"(ssr)/./app/components/ResumeBuilder.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,DocumentTextIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,DocumentTextIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,DocumentTextIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction JobDashboard() {\n    const [jobs, setJobs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        total: 0,\n        new: 0,\n        applied: 0,\n        analyzed: 0\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searching, setSearching] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedJobId, setSelectedJobId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"jobs\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch jobs and stats on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchJobs();\n        fetchStats();\n    }, []);\n    const fetchJobs = async ()=>{\n        try {\n            const response = await fetch(\"/api/jobs\");\n            const data = await response.json();\n            if (data.success) {\n                setJobs(data.jobs);\n            } else {\n                setError(\"Failed to fetch jobs\");\n            }\n        } catch (error) {\n            console.error(\"Error fetching jobs:\", error);\n            setError(\"Network error while fetching jobs\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchStats = async ()=>{\n        try {\n            const response = await fetch(\"/api/stats\");\n            const data = await response.json();\n            if (data.success) {\n                setStats(data.stats);\n            }\n        } catch (error) {\n            console.error(\"Error fetching stats:\", error);\n        }\n    };\n    const startJobSearch = async ()=>{\n        setSearching(true);\n        setError(null);\n        try {\n            const response = await fetch(\"/api/search\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            const data = await response.json();\n            if (data.success) {\n                // Poll for search completion\n                pollSearchStatus();\n            } else {\n                setError(data.message || \"Failed to start job search\");\n                setSearching(false);\n            }\n        } catch (error) {\n            console.error(\"Error starting search:\", error);\n            setError(\"Network error while starting search\");\n            setSearching(false);\n        }\n    };\n    const pollSearchStatus = async ()=>{\n        const pollInterval = setInterval(async ()=>{\n            try {\n                const response = await fetch(\"/api/search/status\");\n                const data = await response.json();\n                if (data.success && !data.status.is_searching) {\n                    clearInterval(pollInterval);\n                    setSearching(false);\n                    // Refresh jobs and stats\n                    fetchJobs();\n                    fetchStats();\n                }\n            } catch (error) {\n                console.error(\"Error polling search status:\", error);\n                clearInterval(pollInterval);\n                setSearching(false);\n            }\n        }, 2000) // Poll every 2 seconds\n        ;\n    };\n    const handleJobUpdate = (jobId, updates)=>{\n        setJobs((prevJobs)=>prevJobs.map((job)=>job.job_id === jobId ? {\n                    ...job,\n                    ...updates\n                } : job));\n        // Refresh stats after job update\n        fetchStats();\n    };\n    const analyzeAllJobs = async ()=>{\n        const newJobs = jobs.filter((job)=>job.status === \"new\");\n        if (newJobs.length === 0) {\n            alert(\"No new jobs to analyze\");\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/jobs/analyze-batch\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    jobIds: newJobs.map((job)=>job.job_id)\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                // Update jobs with analysis results\n                const updatedJobs = jobs.map((job)=>{\n                    const result = data.results.find((r)=>r.job_id === job.job_id);\n                    return result ? {\n                        ...job,\n                        match_score: result.match_score,\n                        status: \"analyzed\"\n                    } : job;\n                });\n                setJobs(updatedJobs);\n                fetchStats();\n                alert(`Analyzed ${data.results.length} jobs successfully!`);\n            } else {\n                setError(\"Failed to analyze jobs\");\n            }\n        } catch (error) {\n            console.error(\"Error analyzing jobs:\", error);\n            setError(\"Network error while analyzing jobs\");\n        }\n    };\n    const getHighPriorityJobs = ()=>{\n        return jobs.filter((job)=>job.match_score >= 70).sort((a, b)=>b.match_score - a.match_score);\n    };\n    const getAIRelevantJobs = ()=>{\n        return jobs.filter((job)=>job.description.toLowerCase().includes(\"ai\") || job.description.toLowerCase().includes(\"artificial intelligence\") || job.description.toLowerCase().includes(\"automation\") || job.title.toLowerCase().includes(\"ai\"));\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                lineNumber: 185,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n            lineNumber: 184,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"Job AI Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"AI-powered job search and application automation for Tishbian Meshach S\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-800\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setError(null),\n                            className: \"text-red-600 hover:text-red-800 text-sm mt-2\",\n                            children: \"Dismiss\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex space-x-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(\"jobs\"),\n                                className: `py-2 px-1 border-b-2 font-medium text-sm ${activeTab === \"jobs\" ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-5 w-5 inline mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Jobs & AI Analysis\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(\"resume\"),\n                                className: `py-2 px-1 border-b-2 font-medium text-sm ${activeTab === \"resume\" ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-5 w-5 inline mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Resume Builder\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(\"analytics\"),\n                                className: `py-2 px-1 border-b-2 font-medium text-sm ${activeTab === \"analytics\" ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-5 w-5 inline mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Analytics\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 9\n                }, this),\n                activeTab === \"jobs\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatsCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    title: \"Total Jobs\",\n                                    value: stats.total,\n                                    color: \"blue\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatsCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    title: \"New Jobs\",\n                                    value: stats.new,\n                                    color: \"green\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatsCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    title: \"Analyzed\",\n                                    value: stats.analyzed,\n                                    color: \"purple\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatsCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    title: \"Applied\",\n                                    value: stats.applied,\n                                    color: \"blue\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SearchForm__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                onSearch: startJobSearch,\n                                searching: searching\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 flex space-x-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: analyzeAllJobs,\n                                className: \"btn-secondary flex items-center\",\n                                disabled: jobs.filter((job)=>job.status === \"new\").length === 0,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Analyze All New Jobs\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Quick Filters:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setJobs(jobs.sort((a, b)=>b.match_score - a.match_score)),\n                                        className: \"text-blue-600 hover:text-blue-800\",\n                                        children: \"High Match Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setJobs(getAIRelevantJobs()),\n                                        className: \"text-purple-600 hover:text-purple-800\",\n                                        children: [\n                                            \"AI-Related Jobs (\",\n                                            getAIRelevantJobs().length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: fetchJobs,\n                                        className: \"text-gray-600 hover:text-gray-800\",\n                                        children: \"Show All\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-6\",\n                            children: jobs.length > 0 ? jobs.map((job)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_JobCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    job: job,\n                                    onJobUpdate: handleJobUpdate\n                                }, job.job_id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 19\n                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"No Jobs Found\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Start a new search to find job opportunities\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                activeTab === \"resume\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ResumeBuilder__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    jobId: selectedJobId || undefined\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                    lineNumber: 330,\n                    columnNumber: 11\n                }, this),\n                activeTab === \"analytics\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold mb-4\",\n                            children: \"Job Analytics\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold mb-2\",\n                                            children: \"High Priority Jobs\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: getHighPriorityJobs().slice(0, 5).map((job)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center p-2 bg-gray-50 rounded\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: [\n                                                                job.title,\n                                                                \" at \",\n                                                                job.company\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-green-600\",\n                                                            children: [\n                                                                job.match_score,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, job.job_id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold mb-2\",\n                                            children: \"AI-Related Opportunities\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: getAIRelevantJobs().slice(0, 5).map((job)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center p-2 bg-purple-50 rounded\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: [\n                                                                job.title,\n                                                                \" at \",\n                                                                job.company\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-purple-600\",\n                                                            children: \"AI Role\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, job.job_id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                    lineNumber: 334,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n            lineNumber: 192,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n        lineNumber: 191,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/JobDashboard.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ResumeBuilder.tsx":
/*!******************************************!*\
  !*** ./app/components/ResumeBuilder.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ResumeBuilder)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,DocumentTextIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,DocumentTextIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,DocumentTextIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ResumeBuilder({ jobId }) {\n    const [resumeData, setResumeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [jobInfo, setJobInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [analysis, setAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isCustomizing, setIsCustomizing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGeneratingPDF, setIsGeneratingPDF] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const customizeForJob = async ()=>{\n        if (!jobId) {\n            setError(\"No job ID provided for customization\");\n            return;\n        }\n        setIsCustomizing(true);\n        setError(null);\n        try {\n            const response = await fetch(\"/api/resume/customize\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    jobId\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                setResumeData(data.resume);\n                setAnalysis(data.analysis);\n                setJobInfo(data.job_info);\n            } else {\n                setError(data.error || \"Failed to customize resume\");\n            }\n        } catch (error) {\n            console.error(\"Error customizing resume:\", error);\n            setError(\"Network error while customizing resume\");\n        } finally{\n            setIsCustomizing(false);\n        }\n    };\n    const generateAndDownloadPDF = async ()=>{\n        if (!resumeData) {\n            setError(\"No resume data available for PDF generation\");\n            return;\n        }\n        setIsGeneratingPDF(true);\n        setError(null);\n        try {\n            const response = await fetch(\"/api/resume/generate-pdf\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    resumeData,\n                    jobId: jobId || \"general\"\n                })\n            });\n            if (response.ok) {\n                const blob = await response.blob();\n                const url = window.URL.createObjectURL(blob);\n                const a = document.createElement(\"a\");\n                a.href = url;\n                // Create a meaningful filename\n                const jobTitle = resumeData.customized_for || \"general\";\n                const fileName = `${resumeData.contact_info?.name || \"Resume\"}_${jobTitle.replace(/[^a-zA-Z0-9]/g, \"_\")}.pdf`;\n                a.download = fileName;\n                document.body.appendChild(a);\n                a.click();\n                document.body.removeChild(a);\n                window.URL.revokeObjectURL(url);\n            } else {\n                const errorData = await response.json();\n                setError(errorData.error || \"Failed to generate PDF\");\n            }\n        } catch (error) {\n            console.error(\"Error generating PDF:\", error);\n            setError(\"Network error while generating PDF\");\n        } finally{\n            setIsGeneratingPDF(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"h-8 w-8 text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"AI Resume Builder\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Create ATS-optimized resumes for specific jobs\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    resumeData?.match_score && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-right\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Job Match Score\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `text-2xl font-bold ${resumeData.match_score >= 80 ? \"text-green-600\" : resumeData.match_score >= 60 ? \"text-yellow-600\" : \"text-red-600\"}`,\n                                children: [\n                                    resumeData.match_score,\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 p-4 bg-red-50 border border-red-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-800\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                lineNumber: 137,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: customizeForJob,\n                        disabled: isCustomizing || !jobId,\n                        className: \"btn-primary flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: isCustomizing ? \"Customizing...\" : \"AI Customize Resume\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: generateAndDownloadPDF,\n                        disabled: !resumeData || isGeneratingPDF,\n                        className: \"btn-secondary flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: isGeneratingPDF ? \"Generating...\" : \"Download PDF\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this),\n            jobInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-blue-900 mb-2\",\n                        children: \"Customizing for:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-blue-800\",\n                        children: [\n                            jobInfo.title,\n                            \" at \",\n                            jobInfo.company\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 11\n                    }, this),\n                    resumeData?.ai_emphasis_level && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-blue-600 mt-1\",\n                        children: [\n                            \"AI Emphasis Level: \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"capitalize\",\n                                children: resumeData.ai_emphasis_level\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 34\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                lineNumber: 165,\n                columnNumber: 9\n            }, this),\n            resumeData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"resume-preview bg-gray-50 p-6 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-4 text-gray-900\",\n                        children: \"Resume Preview\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, this),\n                    resumeData.professional_summary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800 mb-2\",\n                                children: \"Professional Summary\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-700 leading-relaxed\",\n                                children: resumeData.professional_summary\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 13\n                    }, this),\n                    resumeData.highlighted_skills && resumeData.highlighted_skills.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800 mb-2\",\n                                children: \"Core Skills\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: resumeData.highlighted_skills.map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full\",\n                                        children: skill\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 13\n                    }, this),\n                    resumeData.experience && resumeData.experience.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800 mb-2\",\n                                children: \"Experience\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 15\n                            }, this),\n                            resumeData.experience.map((exp, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 p-4 bg-white rounded border\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: exp.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: exp.duration\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 mb-2\",\n                                            children: exp.company\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 19\n                                        }, this),\n                                        exp.achievements && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"list-disc list-inside text-sm text-gray-600\",\n                                            children: exp.achievements.map((achievement, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: achievement\n                                                }, i, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 17\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 13\n                    }, this),\n                    resumeData.projects && resumeData.projects.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800 mb-2\",\n                                children: \"Key Projects\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 15\n                            }, this),\n                            resumeData.projects.map((project, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 p-4 bg-white rounded border\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-1\",\n                                            children: project.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 text-sm mb-2\",\n                                            children: project.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 19\n                                        }, this),\n                                        project.technologies && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-1\",\n                                            children: project.technologies.map((tech, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded\",\n                                                    children: tech\n                                                }, i, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 17\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 13\n                    }, this),\n                    resumeData.education && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800 mb-2\",\n                                children: \"Education\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 bg-white rounded border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-gray-900\",\n                                        children: resumeData.education.degree\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700\",\n                                        children: resumeData.education.institution\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: resumeData.education.duration\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                lineNumber: 178,\n                columnNumber: 9\n            }, this),\n            !resumeData && !isCustomizing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"No Resume Data\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: jobId ? 'Click \"AI Customize Resume\" to generate a job-specific resume' : \"Select a job to customize your resume\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                lineNumber: 263,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n        lineNumber: 111,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ResumeBuilder.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/SearchForm.tsx":
/*!***************************************!*\
  !*** ./app/components/SearchForm.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SearchForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n\n\nfunction SearchForm({ onSearch, searching }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                            children: \"Search for New Jobs\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\SearchForm.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 text-sm\",\n                            children: \"Run AI-powered search for design internships and entry-level positions\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\SearchForm.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\SearchForm.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onSearch,\n                    disabled: searching,\n                    className: \"btn-primary flex items-center disabled:opacity-50 disabled:cursor-not-allowed\",\n                    children: searching ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\SearchForm.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 15\n                            }, this),\n                            \"Searching...\"\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\SearchForm.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 15\n                            }, this),\n                            \"Start Search\"\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\SearchForm.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\SearchForm.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\SearchForm.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/SearchForm.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/StatsCard.tsx":
/*!**************************************!*\
  !*** ./app/components/StatsCard.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StatsCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction StatsCard({ title, value, color }) {\n    const colorClasses = {\n        blue: \"bg-blue-50 text-blue-700 border-blue-200\",\n        green: \"bg-green-50 text-green-700 border-green-200\",\n        purple: \"bg-purple-50 text-purple-700 border-purple-200\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `card border-l-4 ${colorClasses[color]}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm font-medium opacity-75\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\StatsCard.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-2xl font-bold\",\n                        children: value\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\StatsCard.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\StatsCard.tsx\",\n                lineNumber: 17,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\StatsCard.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\StatsCard.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9TdGF0c0NhcmQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFNZSxTQUFTQSxVQUFVLEVBQUVDLEtBQUssRUFBRUMsS0FBSyxFQUFFQyxLQUFLLEVBQWtCO0lBQ3ZFLE1BQU1DLGVBQWU7UUFDbkJDLE1BQU07UUFDTkMsT0FBTztRQUNQQyxRQUFRO0lBQ1Y7SUFFQSxxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVyxDQUFDLGdCQUFnQixFQUFFTCxZQUFZLENBQUNELE1BQU0sQ0FBQyxDQUFDO2tCQUN0RCw0RUFBQ0s7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7O2tDQUNDLDhEQUFDRTt3QkFBRUQsV0FBVTtrQ0FBa0NSOzs7Ozs7a0NBQy9DLDhEQUFDUzt3QkFBRUQsV0FBVTtrQ0FBc0JQOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSzdDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vam9iLWFpLWZyb250ZW5kLy4vYXBwL2NvbXBvbmVudHMvU3RhdHNDYXJkLnRzeD8yZTMyIl0sInNvdXJjZXNDb250ZW50IjpbImludGVyZmFjZSBTdGF0c0NhcmRQcm9wcyB7XG4gIHRpdGxlOiBzdHJpbmdcbiAgdmFsdWU6IG51bWJlclxuICBjb2xvcjogJ2JsdWUnIHwgJ2dyZWVuJyB8ICdwdXJwbGUnXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFN0YXRzQ2FyZCh7IHRpdGxlLCB2YWx1ZSwgY29sb3IgfTogU3RhdHNDYXJkUHJvcHMpIHtcbiAgY29uc3QgY29sb3JDbGFzc2VzID0ge1xuICAgIGJsdWU6ICdiZy1ibHVlLTUwIHRleHQtYmx1ZS03MDAgYm9yZGVyLWJsdWUtMjAwJyxcbiAgICBncmVlbjogJ2JnLWdyZWVuLTUwIHRleHQtZ3JlZW4tNzAwIGJvcmRlci1ncmVlbi0yMDAnLFxuICAgIHB1cnBsZTogJ2JnLXB1cnBsZS01MCB0ZXh0LXB1cnBsZS03MDAgYm9yZGVyLXB1cnBsZS0yMDAnLFxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17YGNhcmQgYm9yZGVyLWwtNCAke2NvbG9yQ2xhc3Nlc1tjb2xvcl19YH0+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSBvcGFjaXR5LTc1XCI+e3RpdGxlfTwvcD5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGRcIj57dmFsdWV9PC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59Il0sIm5hbWVzIjpbIlN0YXRzQ2FyZCIsInRpdGxlIiwidmFsdWUiLCJjb2xvciIsImNvbG9yQ2xhc3NlcyIsImJsdWUiLCJncmVlbiIsInB1cnBsZSIsImRpdiIsImNsYXNzTmFtZSIsInAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/components/StatsCard.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"759aab823e77\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9qb2ItYWktZnJvbnRlbmQvLi9hcHAvZ2xvYmFscy5jc3M/NjliMCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjc1OWFhYjgyM2U3N1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/components/JobDashboard.tsx":
/*!*****************************************!*\
  !*** ./app/components/JobDashboard.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\job-ai\frontend\app\components\JobDashboard.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Job AI - Automated Job Search\",\n    description: \"AI-powered job search and application automation\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"bg-white shadow-sm border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between h-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-bold text-gray-900\",\n                                            children: \"Job AI\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n                                            lineNumber: 24,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n                                        lineNumber: 23,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"Automated Job Search\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n                                            lineNumber: 27,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 19,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_JobDashboard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./components/JobDashboard */ \"(rsc)/./app/components/JobDashboard.tsx\");\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_JobDashboard__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\page.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBb0Q7QUFFckMsU0FBU0M7SUFDdEIscUJBQU8sOERBQUNELGdFQUFZQTs7Ozs7QUFDdEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9qb2ItYWktZnJvbnRlbmQvLi9hcHAvcGFnZS50c3g/NzYwMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgSm9iRGFzaGJvYXJkIGZyb20gJy4vY29tcG9uZW50cy9Kb2JEYXNoYm9hcmQnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhvbWUoKSB7XG4gIHJldHVybiA8Sm9iRGFzaGJvYXJkIC8+XG59XG5cblxuXG5cblxuXG4iXSwibmFtZXMiOlsiSm9iRGFzaGJvYXJkIiwiSG9tZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@heroicons"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();