/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/__barrel_optimize__?names=DocumentTextIcon,DownloadIcon,EyeIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*********************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=DocumentTextIcon,DownloadIcon,EyeIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DocumentTextIcon: () => (/* reexport safe */ _DocumentTextIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   EyeIcon: () => (/* reexport safe */ _EyeIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _DocumentTextIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./DocumentTextIcon.js */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _EyeIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./EyeIcon.js */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1Eb2N1bWVudFRleHRJY29uLERvd25sb2FkSWNvbixFeWVJY29uIT0hLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUNtRSIsInNvdXJjZXMiOlsid2VicGFjazovL2pvYi1haS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzPzFlMjkiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIERvY3VtZW50VGV4dEljb24gfSBmcm9tIFwiLi9Eb2N1bWVudFRleHRJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRXllSWNvbiB9IGZyb20gXCIuL0V5ZUljb24uanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/__barrel_optimize__?names=DocumentTextIcon,DownloadIcon,EyeIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "(ssr)/__barrel_optimize__?names=DownloadIcon,EnvelopeIcon,EyeIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*****************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=DownloadIcon,EnvelopeIcon,EyeIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EnvelopeIcon: () => (/* reexport safe */ _EnvelopeIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   EyeIcon: () => (/* reexport safe */ _EyeIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _EnvelopeIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./EnvelopeIcon.js */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _EyeIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./EyeIcon.js */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1Eb3dubG9hZEljb24sRW52ZWxvcGVJY29uLEV5ZUljb24hPSEuL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQzJEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vam9iLWFpLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanM/YjM2NyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRW52ZWxvcGVJY29uIH0gZnJvbSBcIi4vRW52ZWxvcGVJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRXllSWNvbiB9IGZyb20gXCIuL0V5ZUljb24uanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/__barrel_optimize__?names=DownloadIcon,EnvelopeIcon,EyeIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Capp%5Cpage.tsx&server=true!":
/*!*********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Capp%5Cpage.tsx&server=true! ***!
  \*********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDd3d3c3QlNUNPbmVEcml2ZSU1Q0Rlc2t0b3AlNUNqb2ItYWklNUNmcm9udGVuZCU1Q2FwcCU1Q3BhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2pvYi1haS1mcm9udGVuZC8/MWMyNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHd3d3N0XFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcam9iLWFpXFxcXGZyb250ZW5kXFxcXGFwcFxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Capp%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Capp%5Cglobals.css&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Capp%5Cglobals.css&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/components/CoverLetterGenerator.tsx":
/*!*************************************************!*\
  !*** ./app/components/CoverLetterGenerator.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CoverLetterGenerator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_DownloadIcon_EnvelopeIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=DownloadIcon,EnvelopeIcon,EyeIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_DownloadIcon_EnvelopeIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=DownloadIcon,EnvelopeIcon,EyeIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_DownloadIcon_EnvelopeIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=DownloadIcon,EnvelopeIcon,EyeIcon!=!@heroicons/react/24/outline */ \"(ssr)/__barrel_optimize__?names=DownloadIcon,EnvelopeIcon,EyeIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction CoverLetterGenerator() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [coverLetter, setCoverLetter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Form data\n    const [jobTitle, setJobTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [companyName, setCompanyName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [jobDescription, setJobDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const generateCoverLetter = async ()=>{\n        if (!jobTitle.trim() || !companyName.trim()) {\n            setError(\"Job title and company name are required\");\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        try {\n            const response = await fetch(\"/api/cover-letter/generate\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    job_title: jobTitle.trim(),\n                    company_name: companyName.trim(),\n                    job_description: jobDescription.trim()\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                setCoverLetter(data.cover_letter);\n            } else {\n                setError(data.error || \"Failed to generate cover letter\");\n            }\n        } catch (error) {\n            console.error(\"Error generating cover letter:\", error);\n            setError(\"Network error while generating cover letter\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const downloadCoverLetter = ()=>{\n        if (!coverLetter) return;\n        const blob = new Blob([\n            coverLetter.content\n        ], {\n            type: \"text/plain\"\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = `Cover_Letter_${coverLetter.company_name.replace(/\\s+/g, \"_\")}_${coverLetter.job_title.replace(/\\s+/g, \"_\")}.txt`;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n    };\n    const clearForm = ()=>{\n        setJobTitle(\"\");\n        setCompanyName(\"\");\n        setJobDescription(\"\");\n        setCoverLetter(null);\n        setError(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900 flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DownloadIcon_EnvelopeIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"h-6 w-6 mr-2 text-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this),\n                                \"Cover Letter Generator\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-1\",\n                            children: \"Generate personalized cover letters for specific job applications\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                lineNumber: 96,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"Job Title *\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: jobTitle,\n                                onChange: (e)=>setJobTitle(e.target.value),\n                                placeholder: \"e.g., UI/UX Designer\",\n                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"Company Name *\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: companyName,\n                                onChange: (e)=>setCompanyName(e.target.value),\n                                placeholder: \"e.g., Google\",\n                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"Job Description (Optional)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                value: jobDescription,\n                                onChange: (e)=>setJobDescription(e.target.value),\n                                placeholder: \"Paste the job description here to get a more tailored cover letter...\",\n                                rows: 6,\n                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500 mt-1\",\n                                children: \"Adding the job description helps create a more personalized cover letter\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: generateCoverLetter,\n                        disabled: loading || !jobTitle.trim() || !companyName.trim(),\n                        className: \"flex-1 bg-blue-500 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center\",\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, this),\n                                \"Generating Cover Letter...\"\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DownloadIcon_EnvelopeIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this),\n                                \"Generate Cover Letter\"\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: clearForm,\n                        className: \"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50\",\n                        children: \"Clear Form\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, this),\n            coverLetter && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t pt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DownloadIcon_EnvelopeIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-5 w-5 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Cover Letter Preview\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: downloadCoverLetter,\n                                    className: \"bg-green-500 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-600 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DownloadIcon_EnvelopeIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.DownloadIcon, {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Download\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-50 p-4 rounded-lg mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-4 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-blue-900\",\n                                                children: \"Position:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-700 ml-1\",\n                                                children: coverLetter.job_title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-blue-900\",\n                                                children: \"Company:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-700 ml-1\",\n                                                children: coverLetter.company_name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-blue-900\",\n                                                children: \"Generated:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-700 ml-1\",\n                                                children: new Date(coverLetter.generated_at).toLocaleDateString()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, this),\n                            coverLetter.relevant_skills.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-blue-900 text-sm\",\n                                        children: \"Highlighted Skills:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-1 mt-1\",\n                                        children: coverLetter.relevant_skills.map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-blue-200 text-blue-800 px-2 py-1 rounded text-xs\",\n                                                children: skill\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 p-6 rounded-lg border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"whitespace-pre-line text-gray-800 leading-relaxed\",\n                            children: coverLetter.content\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-medium text-yellow-800 mb-2\",\n                                children: \"\\uD83D\\uDCA1 Tips for your cover letter:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-sm text-yellow-700 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Review and customize the content to match your personal writing style\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Research the company and add specific details about why you want to work there\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Proofread for any errors before sending\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Keep it concise - ideally one page\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n                lineNumber: 181,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\CoverLetterGenerator.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/CoverLetterGenerator.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ProfileManager.tsx":
/*!*******************************************!*\
  !*** ./app/components/ProfileManager.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProfileManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PencilIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PencilIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PencilIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PencilIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PencilIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PencilIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PencilIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PencilIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ProfileManager() {\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [editing, setEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchProfile();\n    }, []);\n    const fetchProfile = async ()=>{\n        try {\n            const response = await fetch(\"/api/profile\");\n            const data = await response.json();\n            if (data.success) {\n                setProfile(data.profile);\n            } else {\n                setError(\"Failed to load profile\");\n            }\n        } catch (error) {\n            console.error(\"Error fetching profile:\", error);\n            setError(\"Network error while loading profile\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const saveProfile = async ()=>{\n        if (!profile) return;\n        setSaving(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            const response = await fetch(\"/api/profile/update\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(profile)\n            });\n            const data = await response.json();\n            if (data.success) {\n                setSuccess(\"Profile updated successfully!\");\n                setEditing(false);\n                setTimeout(()=>setSuccess(null), 3000);\n            } else {\n                setError(data.error || \"Failed to update profile\");\n            }\n        } catch (error) {\n            console.error(\"Error saving profile:\", error);\n            setError(\"Network error while saving profile\");\n        } finally{\n            setSaving(false);\n        }\n    };\n    const cancelEditing = ()=>{\n        setEditing(false);\n        fetchProfile() // Reload original data\n        ;\n    };\n    const updatePersonalInfo = (field, value)=>{\n        if (!profile) return;\n        setProfile({\n            ...profile,\n            personal_info: {\n                ...profile.personal_info,\n                [field]: value\n            }\n        });\n    };\n    const updateSummary = (value)=>{\n        if (!profile) return;\n        setProfile({\n            ...profile,\n            summary: value\n        });\n    };\n    const updateSkills = (value)=>{\n        if (!profile) return;\n        const skills = value.split(\",\").map((skill)=>skill.trim()).filter((skill)=>skill);\n        setProfile({\n            ...profile,\n            skills\n        });\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-sm border p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-2 text-gray-600\",\n                        children: \"Loading profile...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n            lineNumber: 135,\n            columnNumber: 7\n        }, this);\n    }\n    if (!profile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-sm border p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600\",\n                        children: \"Failed to load profile\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: fetchProfile,\n                        className: \"mt-4 bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n            lineNumber: 146,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PencilIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"h-6 w-6 mr-2 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Profile Management\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Manage your personal information for resume and cover letter generation\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: editing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: saveProfile,\n                                    disabled: saving,\n                                    className: \"bg-green-500 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-600 disabled:opacity-50 flex items-center\",\n                                    children: saving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Saving...\"\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PencilIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Save\"\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: cancelEditing,\n                                    disabled: saving,\n                                    className: \"border border-gray-300 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-50 disabled:opacity-50 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PencilIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Cancel\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setEditing(true),\n                            className: \"bg-blue-500 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-600 flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PencilIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, this),\n                                \"Edit Profile\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                lineNumber: 215,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-4 bg-green-50 border border-green-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-green-600\",\n                    children: success\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                lineNumber: 221,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                children: \"Personal Information\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 15\n                                            }, this),\n                                            editing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: profile.personal_info.name,\n                                                onChange: (e)=>updatePersonalInfo(\"name\", e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-900\",\n                                                children: profile.personal_info.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Email\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 15\n                                            }, this),\n                                            editing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"email\",\n                                                value: profile.personal_info.email,\n                                                onChange: (e)=>updatePersonalInfo(\"email\", e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-900\",\n                                                children: profile.personal_info.email\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Phone\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 15\n                                            }, this),\n                                            editing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"tel\",\n                                                value: profile.personal_info.phone,\n                                                onChange: (e)=>updatePersonalInfo(\"phone\", e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-900\",\n                                                children: profile.personal_info.phone\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Location\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 15\n                                            }, this),\n                                            editing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: profile.personal_info.location,\n                                                onChange: (e)=>updatePersonalInfo(\"location\", e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-900\",\n                                                children: profile.personal_info.location\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"LinkedIn\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 15\n                                            }, this),\n                                            editing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"url\",\n                                                value: profile.personal_info.linkedin,\n                                                onChange: (e)=>updatePersonalInfo(\"linkedin\", e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-900\",\n                                                children: profile.personal_info.linkedin\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Portfolio\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 15\n                                            }, this),\n                                            editing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"url\",\n                                                value: profile.personal_info.portfolio,\n                                                onChange: (e)=>updatePersonalInfo(\"portfolio\", e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-900\",\n                                                children: profile.personal_info.portfolio\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                children: \"Professional Summary\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 11\n                            }, this),\n                            editing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                value: profile.summary,\n                                onChange: (e)=>updateSummary(e.target.value),\n                                rows: 4,\n                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-900\",\n                                children: profile.summary\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                children: \"Skills\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 11\n                            }, this),\n                            editing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        value: profile.skills.join(\", \"),\n                                        onChange: (e)=>updateSkills(e.target.value),\n                                        rows: 3,\n                                        placeholder: \"Enter skills separated by commas\",\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 mt-1\",\n                                        children: \"Separate skills with commas\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: profile.skills.map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm\",\n                                        children: skill\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-50 p-4 rounded-lg border border-blue-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-medium text-blue-900 mb-2\",\n                                children: \"\\uD83D\\uDCDD Additional Sections\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-700 text-sm\",\n                                children: \"Experience, Education, and Projects sections are currently view-only. Full editing capabilities for these sections will be available in a future update.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                children: \"Experience\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 11\n                            }, this),\n                            profile.experience.map((exp, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 p-4 bg-gray-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: exp.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: exp.company\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right text-sm text-gray-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: exp.duration\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: exp.location\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                                            lineNumber: 381,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-1\",\n                                            children: exp.responsibilities.map((resp, respIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"text-gray-700 text-sm\",\n                                                    children: [\n                                                        \"• \",\n                                                        resp\n                                                    ]\n                                                }, respIndex, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 13\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ProfileManager.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ProfileManager.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ResumeGenerator.tsx":
/*!********************************************!*\
  !*** ./app/components/ResumeGenerator.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ResumeGenerator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_DocumentTextIcon_DownloadIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=DocumentTextIcon,DownloadIcon,EyeIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_DocumentTextIcon_DownloadIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=DocumentTextIcon,DownloadIcon,EyeIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_DocumentTextIcon_DownloadIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=DocumentTextIcon,DownloadIcon,EyeIcon!=!@heroicons/react/24/outline */ \"(ssr)/__barrel_optimize__?names=DocumentTextIcon,DownloadIcon,EyeIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ResumeGenerator() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resume, setResume] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [templateStyle, setTemplateStyle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"modern\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const generateResume = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            const response = await fetch(\"/api/resume/generate\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    template_style: templateStyle\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                setResume(data.resume);\n            } else {\n                setError(data.error || \"Failed to generate resume\");\n            }\n        } catch (error) {\n            console.error(\"Error generating resume:\", error);\n            setError(\"Network error while generating resume\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const downloadResume = ()=>{\n        if (!resume) return;\n        const resumeText = formatResumeAsText(resume);\n        const blob = new Blob([\n            resumeText\n        ], {\n            type: \"text/plain\"\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = `${resume.personal_info.name.replace(/\\s+/g, \"_\")}_Resume.txt`;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n    };\n    const formatResumeAsText = (resumeData)=>{\n        const { personal_info, summary, skills, experience, education, projects } = resumeData;\n        return `\n${personal_info.name}\n${personal_info.email} | ${personal_info.phone}\n${personal_info.location}\nLinkedIn: ${personal_info.linkedin}\nPortfolio: ${personal_info.portfolio}\n\nPROFESSIONAL SUMMARY\n${summary}\n\nTECHNICAL SKILLS\n${skills.join(\" • \")}\n\nEXPERIENCE\n${experience.map((exp)=>`\n${exp.title} | ${exp.company}\n${exp.duration} | ${exp.location}\n${exp.responsibilities.map((resp)=>`• ${resp}`).join(\"\\n\")}\n`).join(\"\\n\")}\n\nEDUCATION\n${education.map((edu)=>`\n${edu.degree}\n${edu.institution} | ${edu.year}\n${edu.location} | GPA: ${edu.gpa}\n`).join(\"\\n\")}\n\nPROJECTS\n${projects.map((proj)=>`\n${proj.name}\n${proj.description}\nTechnologies: ${proj.technologies.join(\", \")}\nDuration: ${proj.duration}\n`).join(\"\\n\")}\n`.trim();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900 flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentTextIcon_DownloadIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"h-6 w-6 mr-2 text-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this),\n                                \"Resume Generator\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-1\",\n                            children: \"Generate a professional resume based on your profile\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                lineNumber: 141,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                        children: \"Resume Template\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                        value: templateStyle,\n                        onChange: (e)=>setTemplateStyle(e.target.value),\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"modern\",\n                                children: \"Modern\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"classic\",\n                                children: \"Classic\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"minimal\",\n                                children: \"Minimal\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"creative\",\n                                children: \"Creative\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: generateResume,\n                    disabled: loading,\n                    className: \"w-full bg-blue-500 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center\",\n                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 15\n                            }, this),\n                            \"Generating Resume...\"\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentTextIcon_DownloadIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 15\n                            }, this),\n                            \"Generate Resume\"\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, this),\n            resume && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t pt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentTextIcon_DownloadIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-5 w-5 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Resume Preview\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: downloadResume,\n                                className: \"bg-green-500 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-600 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentTextIcon_DownloadIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.DownloadIcon, {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Download\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 p-6 rounded-lg border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center border-b pb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: resume.personal_info.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600 mt-2\",\n                                            children: [\n                                                resume.personal_info.email,\n                                                \" | \",\n                                                resume.personal_info.phone\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: resume.personal_info.location\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-blue-600 mt-1\",\n                                            children: [\n                                                resume.personal_info.linkedin,\n                                                \" | \",\n                                                resume.personal_info.portfolio\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"Professional Summary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700\",\n                                            children: resume.summary\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"Technical Skills\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: resume.skills.map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm\",\n                                                    children: skill\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"Experience\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 17\n                                        }, this),\n                                        resume.experience.map((exp, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-medium text-gray-900\",\n                                                                        children: exp.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                                                        lineNumber: 245,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: exp.company\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                                                        lineNumber: 246,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right text-sm text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: exp.duration\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                                                        lineNumber: 249,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: exp.location\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                                                        lineNumber: 250,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                                                lineNumber: 248,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"mt-2 space-y-1\",\n                                                        children: exp.responsibilities.map((resp, respIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"text-gray-700 text-sm\",\n                                                                children: [\n                                                                    \"• \",\n                                                                    resp\n                                                                ]\n                                                            }, respIndex, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 19\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"Education\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 17\n                                        }, this),\n                                        resume.education.map((edu, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: edu.degree\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: edu.institution\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                                                    lineNumber: 272,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right text-sm text-gray-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: edu.year\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                                                    lineNumber: 275,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"GPA: \",\n                                                                        edu.gpa\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                                                    lineNumber: 276,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 19\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"Projects\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 17\n                                        }, this),\n                                        resume.projects.map((project, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: project.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-700 text-sm mt-1\",\n                                                        children: project.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 text-sm mt-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Technologies:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" \",\n                                                            project.technologies.join(\", \")\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 text-sm\",\n                                                        children: [\n                                                            \"Duration: \",\n                                                            project.duration\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 19\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n                lineNumber: 186,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeGenerator.tsx\",\n        lineNumber: 127,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ResumeGenerator.tsx\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ResumeGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/ResumeGenerator */ \"(ssr)/./app/components/ResumeGenerator.tsx\");\n/* harmony import */ var _components_CoverLetterGenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/CoverLetterGenerator */ \"(ssr)/./app/components/CoverLetterGenerator.tsx\");\n/* harmony import */ var _components_ProfileManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/ProfileManager */ \"(ssr)/./app/components/ProfileManager.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Home() {\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"resume\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-2\",\n                            children: \"\\uD83D\\uDCDD Resume & Cover Letter Generator\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\page.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Create professional resumes and personalized cover letters with AI assistance\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\page.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\page.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-1 shadow-sm border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(\"resume\"),\n                                className: `px-6 py-2 rounded-md font-medium transition-colors ${activeTab === \"resume\" ? \"bg-blue-500 text-white\" : \"text-gray-600 hover:text-gray-900\"}`,\n                                children: \"\\uD83D\\uDCC4 Resume Generator\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(\"cover-letter\"),\n                                className: `px-6 py-2 rounded-md font-medium transition-colors ${activeTab === \"cover-letter\" ? \"bg-blue-500 text-white\" : \"text-gray-600 hover:text-gray-900\"}`,\n                                children: \"✉️ Cover Letter\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(\"profile\"),\n                                className: `px-6 py-2 rounded-md font-medium transition-colors ${activeTab === \"profile\" ? \"bg-blue-500 text-white\" : \"text-gray-600 hover:text-gray-900\"}`,\n                                children: \"\\uD83D\\uDC64 Profile\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\page.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        activeTab === \"resume\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResumeGenerator__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\page.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 38\n                        }, this),\n                        activeTab === \"cover-letter\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CoverLetterGenerator__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\page.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 44\n                        }, this),\n                        activeTab === \"profile\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProfileManager__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\page.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 39\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\page.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\page.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\page.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"759aab823e77\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9qb2ItYWktZnJvbnRlbmQvLi9hcHAvZ2xvYmFscy5jc3M/NjliMCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjc1OWFhYjgyM2U3N1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Job AI - Automated Job Search\",\n    description: \"AI-powered job search and application automation\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"bg-white shadow-sm border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between h-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-bold text-gray-900\",\n                                            children: \"Job AI\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n                                            lineNumber: 24,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n                                        lineNumber: 23,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"Automated Job Search\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n                                            lineNumber: 27,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 19,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\job-ai\frontend\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@heroicons","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();