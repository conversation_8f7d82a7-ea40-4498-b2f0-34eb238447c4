/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Capp%5Ccomponents%5CJobDashboard.tsx&server=true!":
/*!******************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Capp%5Ccomponents%5CJobDashboard.tsx&server=true! ***!
  \******************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/JobDashboard.tsx */ \"(ssr)/./app/components/JobDashboard.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDd3d3c3QlNUNPbmVEcml2ZSU1Q0Rlc2t0b3AlNUNqb2ItYWklNUNmcm9udGVuZCU1Q2FwcCU1Q2NvbXBvbmVudHMlNUNKb2JEYXNoYm9hcmQudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2pvYi1haS1mcm9udGVuZC8/Y2QyMSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHd3d3N0XFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcam9iLWFpXFxcXGZyb250ZW5kXFxcXGFwcFxcXFxjb21wb25lbnRzXFxcXEpvYkRhc2hib2FyZC50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Capp%5Ccomponents%5CJobDashboard.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Capp%5Cglobals.css&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Capp%5Cglobals.css&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/components/JobCard.tsx":
/*!************************************!*\
  !*** ./app/components/JobCard.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ JobCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CogIcon_LinkIcon_MapPinIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CalendarIcon,CogIcon,LinkIcon,MapPinIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BuildingOfficeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CogIcon_LinkIcon_MapPinIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CalendarIcon,CogIcon,LinkIcon,MapPinIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CogIcon_LinkIcon_MapPinIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CalendarIcon,CogIcon,LinkIcon,MapPinIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CogIcon_LinkIcon_MapPinIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CalendarIcon,CogIcon,LinkIcon,MapPinIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CogIcon_LinkIcon_MapPinIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CalendarIcon,CogIcon,LinkIcon,MapPinIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CogIcon_LinkIcon_MapPinIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CalendarIcon,CogIcon,LinkIcon,MapPinIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/LinkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction JobCard({ job, onJobUpdate }) {\n    const [applying, setApplying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [analyzing, setAnalyzing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [analysis, setAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showAnalysis, setShowAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const analyzeJob = async ()=>{\n        setAnalyzing(true);\n        try {\n            const response = await fetch(`/api/jobs/${job.job_id}/analyze`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            const data = await response.json();\n            if (data.success) {\n                setAnalysis(data.analysis);\n                setShowAnalysis(true);\n                // Update parent component if callback provided\n                onJobUpdate?.(job.job_id, {\n                    status: \"analyzed\",\n                    match_score: data.analysis.match_score\n                });\n            } else {\n                console.error(\"Analysis failed:\", data.error);\n            }\n        } catch (error) {\n            console.error(\"Error analyzing job:\", error);\n        } finally{\n            setAnalyzing(false);\n        }\n    };\n    const autoApply = async ()=>{\n        setApplying(true);\n        try {\n            const response = await fetch(`/api/jobs/${job.job_id}/apply`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            const result = await response.json();\n            if (result.success) {\n                // Show success message (you might want to add a toast library)\n                alert(\"Application submitted successfully!\");\n                onJobUpdate?.(job.job_id, {\n                    status: \"applied\"\n                });\n            } else {\n                alert(`Application failed: ${result.error || \"Unknown error\"}`);\n            }\n        } catch (error) {\n            console.error(\"Error applying to job:\", error);\n            alert(\"Error submitting application\");\n        } finally{\n            setApplying(false);\n        }\n    };\n    const customizeResume = async ()=>{\n        try {\n            const response = await fetch(\"/api/resume/customize\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    jobId: job.job_id\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                // Open resume in new tab or download\n                const pdfResponse = await fetch(\"/api/resume/generate-pdf\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        resumeData: data.resume,\n                        jobId: job.job_id\n                    })\n                });\n                if (pdfResponse.ok) {\n                    const blob = await pdfResponse.blob();\n                    const url = window.URL.createObjectURL(blob);\n                    const a = document.createElement(\"a\");\n                    a.href = url;\n                    a.download = `resume-${job.company}-${job.title}.pdf`;\n                    a.click();\n                    window.URL.revokeObjectURL(url);\n                }\n            }\n        } catch (error) {\n            console.error(\"Error customizing resume:\", error);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"new\":\n                return \"bg-green-100 text-green-800\";\n            case \"applied\":\n                return \"bg-blue-100 text-blue-800\";\n            case \"analyzed\":\n                return \"bg-yellow-100 text-yellow-800\";\n            case \"failed\":\n                return \"bg-red-100 text-red-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    const getMatchScoreColor = (score)=>{\n        if (score >= 80) return \"text-green-600 font-semibold\";\n        if (score >= 60) return \"text-yellow-600 font-medium\";\n        return \"text-red-600\";\n    };\n    const getSourceIcon = (source)=>{\n        switch(source){\n            case \"Internshala\":\n                return \"\\uD83C\\uDF93\";\n            case \"Indeed India\":\n                return \"\\uD83D\\uDD0D\";\n            case \"Naukri.com\":\n                return \"\\uD83D\\uDCBC\";\n            default:\n                return \"\\uD83C\\uDF10\";\n        }\n    };\n    const getSourceColor = (source)=>{\n        switch(source){\n            case \"Internshala\":\n                return \"bg-purple-100 text-purple-800\";\n            case \"Indeed India\":\n                return \"bg-blue-100 text-blue-800\";\n            case \"Naukri.com\":\n                return \"bg-orange-100 text-orange-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    const isRealJobURL = (url)=>{\n        return url.includes(\"/viewjob?jk=\") || url.includes(\"/job-listings-\") || url.includes(\"/internship/detail/\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card hover:shadow-lg transition-shadow\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-start mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                children: job.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 text-sm text-gray-600 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CogIcon_LinkIcon_MapPinIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 15\n                                            }, this),\n                                            job.company\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CogIcon_LinkIcon_MapPinIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 15\n                                            }, this),\n                                            job.location\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CogIcon_LinkIcon_MapPinIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 15\n                                            }, this),\n                                            job.posted_date\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-end space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: `px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(job.status)}`,\n                                children: job.status\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: `px-2 py-1 rounded-full text-xs font-medium ${getSourceColor(job.source)}`,\n                                children: [\n                                    getSourceIcon(job.source),\n                                    \" \",\n                                    job.source\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this),\n                            isRealJobURL(job.apply_url) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-green-600 font-medium\",\n                                children: \"✅ Real Job URL\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-700 mb-4 line-clamp-3\",\n                children: job.description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this),\n            showAnalysis && analysis && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-semibold text-blue-900 mb-2\",\n                        children: \"AI Analysis Results\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Match Score:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `ml-2 ${getMatchScoreColor(analysis.match_score)}`,\n                                        children: [\n                                            analysis.match_score,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Priority:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 capitalize\",\n                                        children: analysis.application_priority\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"AI Relevance:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 capitalize\",\n                                        children: analysis.ai_relevance\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Competition:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 capitalize\",\n                                        children: analysis.estimated_competition\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, this),\n                    analysis.key_skills_match && analysis.key_skills_match.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-sm\",\n                                children: \"Matching Skills:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-1 mt-1\",\n                                children: analysis.key_skills_match.slice(0, 5).map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 bg-green-100 text-green-800 text-xs rounded\",\n                                        children: skill\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 13\n                    }, this),\n                    analysis.application_strategy && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-sm\",\n                                children: \"Strategy:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-700 mt-1\",\n                                children: analysis.application_strategy\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                lineNumber: 197,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            job.match_score > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: `text-sm ${getMatchScoreColor(job.match_score)}`,\n                                children: [\n                                    \"Match: \",\n                                    job.match_score,\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this),\n                            analysis && analysis.is_ai_role && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full\",\n                                children: \"AI Role\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: analyzeJob,\n                                disabled: analyzing,\n                                className: \"btn-secondary flex items-center text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CogIcon_LinkIcon_MapPinIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 13\n                                    }, this),\n                                    analyzing ? \"Analyzing...\" : \"AI Analyze\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: customizeResume,\n                                className: \"btn-secondary flex items-center text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CogIcon_LinkIcon_MapPinIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Custom Resume\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: autoApply,\n                                disabled: applying || job.status === \"applied\",\n                                className: \"btn-primary flex items-center text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CogIcon_LinkIcon_MapPinIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 13\n                                    }, this),\n                                    applying ? \"Applying...\" : job.status === \"applied\" ? \"Applied\" : \"Auto Apply\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 11\n                            }, this),\n                            job.apply_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: isRealJobURL(job.apply_url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: job.apply_url,\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"btn-outline flex items-center text-sm bg-green-50 border-green-200 text-green-700 hover:bg-green-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CogIcon_LinkIcon_MapPinIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Apply on \",\n                                        job.source\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: job.apply_url,\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"btn-outline flex items-center text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CogIcon_LinkIcon_MapPinIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"View Job\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n                lineNumber: 242,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobCard.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/JobCard.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/JobDashboard.tsx":
/*!*****************************************!*\
  !*** ./app/components/JobDashboard.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ JobDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _JobCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./JobCard */ \"(ssr)/./app/components/JobCard.tsx\");\n/* harmony import */ var _SearchForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SearchForm */ \"(ssr)/./app/components/SearchForm.tsx\");\n/* harmony import */ var _StatsCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./StatsCard */ \"(ssr)/./app/components/StatsCard.tsx\");\n/* harmony import */ var _ResumeBuilder__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ResumeBuilder */ \"(ssr)/./app/components/ResumeBuilder.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,DocumentTextIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,DocumentTextIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,DocumentTextIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction JobDashboard() {\n    const [jobs, setJobs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        total: 0,\n        new: 0,\n        applied: 0,\n        analyzed: 0\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searching, setSearching] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedJobId, setSelectedJobId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"jobs\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch jobs and stats on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchJobs();\n        fetchStats();\n    }, []);\n    const fetchJobs = async ()=>{\n        try {\n            const response = await fetch(\"/api/jobs\");\n            const data = await response.json();\n            if (data.success) {\n                setJobs(data.jobs);\n            } else {\n                setError(\"Failed to fetch jobs\");\n            }\n        } catch (error) {\n            console.error(\"Error fetching jobs:\", error);\n            setError(\"Network error while fetching jobs\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchStats = async ()=>{\n        try {\n            const response = await fetch(\"/api/stats\");\n            const data = await response.json();\n            if (data.success) {\n                setStats(data.stats);\n            }\n        } catch (error) {\n            console.error(\"Error fetching stats:\", error);\n        }\n    };\n    const startJobSearch = async ()=>{\n        setSearching(true);\n        setError(null);\n        try {\n            const response = await fetch(\"/api/search\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            const data = await response.json();\n            if (data.success) {\n                // Poll for search completion\n                pollSearchStatus();\n            } else {\n                setError(data.message || \"Failed to start job search\");\n                setSearching(false);\n            }\n        } catch (error) {\n            console.error(\"Error starting search:\", error);\n            setError(\"Network error while starting search\");\n            setSearching(false);\n        }\n    };\n    const pollSearchStatus = async ()=>{\n        const pollInterval = setInterval(async ()=>{\n            try {\n                const response = await fetch(\"/api/search/status\");\n                const data = await response.json();\n                if (data.success && !data.status.is_searching) {\n                    clearInterval(pollInterval);\n                    setSearching(false);\n                    // Refresh jobs and stats\n                    fetchJobs();\n                    fetchStats();\n                }\n            } catch (error) {\n                console.error(\"Error polling search status:\", error);\n                clearInterval(pollInterval);\n                setSearching(false);\n            }\n        }, 2000) // Poll every 2 seconds\n        ;\n    };\n    const handleJobUpdate = (jobId, updates)=>{\n        setJobs((prevJobs)=>prevJobs.map((job)=>job.job_id === jobId ? {\n                    ...job,\n                    ...updates\n                } : job));\n        // Refresh stats after job update\n        fetchStats();\n    };\n    const analyzeAllJobs = async ()=>{\n        const newJobs = jobs.filter((job)=>job.status === \"new\");\n        if (newJobs.length === 0) {\n            alert(\"No new jobs to analyze\");\n            return;\n        }\n        setError(null);\n        try {\n            const response = await fetch(\"/api/jobs/analyze-batch\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    jobIds: newJobs.map((job)=>job.job_id)\n                })\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(\"Batch analysis error:\", errorText);\n                setError(`Analysis failed: ${response.status} ${response.statusText}`);\n                return;\n            }\n            const data = await response.json();\n            if (data.success) {\n                // Update jobs with analysis results\n                const updatedJobs = jobs.map((job)=>{\n                    const result = data.results.find((r)=>r.job_id === job.job_id);\n                    return result ? {\n                        ...job,\n                        match_score: result.match_score,\n                        status: \"analyzed\"\n                    } : job;\n                });\n                setJobs(updatedJobs);\n                fetchStats();\n                alert(`Analyzed ${data.results.length} jobs successfully!\\n\\nHigh Priority: ${data.summary.high_priority_count}\\nAI Relevant: ${data.summary.ai_relevant_count}`);\n            } else {\n                setError(data.error || \"Failed to analyze jobs\");\n            }\n        } catch (error) {\n            console.error(\"Error analyzing jobs:\", error);\n            setError(`Network error while analyzing jobs: ${error.message}`);\n        }\n    };\n    const getHighPriorityJobs = ()=>{\n        return jobs.filter((job)=>job.match_score >= 70).sort((a, b)=>b.match_score - a.match_score);\n    };\n    const getAIRelevantJobs = ()=>{\n        return jobs.filter((job)=>job.description.toLowerCase().includes(\"ai\") || job.description.toLowerCase().includes(\"artificial intelligence\") || job.description.toLowerCase().includes(\"automation\") || job.title.toLowerCase().includes(\"ai\"));\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                lineNumber: 194,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n            lineNumber: 193,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"Job AI Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"\\uD83C\\uDDEE\\uD83C\\uDDF3 Real job opportunities from Internshala, Indeed India, and Naukri.com with authentic URLs\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-800\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setError(null),\n                            className: \"text-red-600 hover:text-red-800 text-sm mt-2\",\n                            children: \"Dismiss\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex space-x-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(\"jobs\"),\n                                className: `py-2 px-1 border-b-2 font-medium text-sm ${activeTab === \"jobs\" ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-5 w-5 inline mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Jobs & AI Analysis\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(\"resume\"),\n                                className: `py-2 px-1 border-b-2 font-medium text-sm ${activeTab === \"resume\" ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-5 w-5 inline mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Resume Builder\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(\"analytics\"),\n                                className: `py-2 px-1 border-b-2 font-medium text-sm ${activeTab === \"analytics\" ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-5 w-5 inline mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Analytics\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 9\n                }, this),\n                activeTab === \"jobs\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatsCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    title: \"Total Jobs\",\n                                    value: stats.total,\n                                    color: \"blue\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatsCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    title: \"New Jobs\",\n                                    value: stats.new,\n                                    color: \"green\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatsCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    title: \"Analyzed\",\n                                    value: stats.analyzed,\n                                    color: \"purple\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatsCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    title: \"Applied\",\n                                    value: stats.applied,\n                                    color: \"blue\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SearchForm__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                onSearch: startJobSearch,\n                                searching: searching\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 flex space-x-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: analyzeAllJobs,\n                                className: \"btn-secondary flex items-center\",\n                                disabled: jobs.filter((job)=>job.status === \"new\").length === 0,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Analyze All New Jobs\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Quick Filters:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setJobs(jobs.sort((a, b)=>b.match_score - a.match_score)),\n                                        className: \"text-blue-600 hover:text-blue-800\",\n                                        children: \"High Match Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setJobs(getAIRelevantJobs()),\n                                        className: \"text-purple-600 hover:text-purple-800\",\n                                        children: [\n                                            \"AI-Related Jobs (\",\n                                            getAIRelevantJobs().length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: fetchJobs,\n                                        className: \"text-gray-600 hover:text-gray-800\",\n                                        children: \"Show All\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-6\",\n                            children: jobs.length > 0 ? jobs.map((job)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_JobCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    job: job,\n                                    onJobUpdate: handleJobUpdate\n                                }, job.job_id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 19\n                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"No Jobs Found\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-4\",\n                                        children: 'Click \"Search Real Jobs\" to find opportunities from Indian job portals'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center space-x-4 text-sm text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83C\\uDF93 Internshala\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83D\\uDD0D Indeed India\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83D\\uDCBC Naukri.com\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                activeTab === \"resume\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ResumeBuilder__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    jobId: selectedJobId || undefined\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                    lineNumber: 344,\n                    columnNumber: 11\n                }, this),\n                activeTab === \"analytics\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold mb-4\",\n                            children: \"Job Analytics\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                            lineNumber: 349,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold mb-2\",\n                                            children: \"High Priority Jobs\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: getHighPriorityJobs().slice(0, 5).map((job)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center p-2 bg-gray-50 rounded\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: [\n                                                                job.title,\n                                                                \" at \",\n                                                                job.company\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-green-600\",\n                                                            children: [\n                                                                job.match_score,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, job.job_id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold mb-2\",\n                                            children: \"AI-Related Opportunities\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: getAIRelevantJobs().slice(0, 5).map((job)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center p-2 bg-purple-50 rounded\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: [\n                                                                job.title,\n                                                                \" at \",\n                                                                job.company\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-purple-600\",\n                                                            children: \"AI Role\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, job.job_id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n                    lineNumber: 348,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n            lineNumber: 201,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\JobDashboard.tsx\",\n        lineNumber: 200,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9Kb2JEYXNoYm9hcmQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDMkM7QUFDWjtBQUNNO0FBQ0Y7QUFDUTtBQUMrQztBQXNCM0UsU0FBU1M7SUFDdEIsTUFBTSxDQUFDQyxNQUFNQyxRQUFRLEdBQUdYLCtDQUFRQSxDQUFRLEVBQUU7SUFDMUMsTUFBTSxDQUFDWSxPQUFPQyxTQUFTLEdBQUdiLCtDQUFRQSxDQUFRO1FBQUVjLE9BQU87UUFBR0MsS0FBSztRQUFHQyxTQUFTO1FBQUdDLFVBQVU7SUFBRTtJQUN0RixNQUFNLENBQUNDLFNBQVNDLFdBQVcsR0FBR25CLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ29CLFdBQVdDLGFBQWEsR0FBR3JCLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ3NCLGVBQWVDLGlCQUFpQixHQUFHdkIsK0NBQVFBLENBQWdCO0lBQ2xFLE1BQU0sQ0FBQ3dCLFdBQVdDLGFBQWEsR0FBR3pCLCtDQUFRQSxDQUFrQztJQUM1RSxNQUFNLENBQUMwQixPQUFPQyxTQUFTLEdBQUczQiwrQ0FBUUEsQ0FBZ0I7SUFFbEQsMENBQTBDO0lBQzFDQyxnREFBU0EsQ0FBQztRQUNSMkI7UUFDQUM7SUFDRixHQUFHLEVBQUU7SUFFTCxNQUFNRCxZQUFZO1FBQ2hCLElBQUk7WUFDRixNQUFNRSxXQUFXLE1BQU1DLE1BQU07WUFDN0IsTUFBTUMsT0FBTyxNQUFNRixTQUFTRyxJQUFJO1lBRWhDLElBQUlELEtBQUtFLE9BQU8sRUFBRTtnQkFDaEJ2QixRQUFRcUIsS0FBS3RCLElBQUk7WUFDbkIsT0FBTztnQkFDTGlCLFNBQVM7WUFDWDtRQUNGLEVBQUUsT0FBT0QsT0FBTztZQUNkUyxRQUFRVCxLQUFLLENBQUMsd0JBQXdCQTtZQUN0Q0MsU0FBUztRQUNYLFNBQVU7WUFDUlIsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNVSxhQUFhO1FBQ2pCLElBQUk7WUFDRixNQUFNQyxXQUFXLE1BQU1DLE1BQU07WUFDN0IsTUFBTUMsT0FBTyxNQUFNRixTQUFTRyxJQUFJO1lBRWhDLElBQUlELEtBQUtFLE9BQU8sRUFBRTtnQkFDaEJyQixTQUFTbUIsS0FBS3BCLEtBQUs7WUFDckI7UUFDRixFQUFFLE9BQU9jLE9BQU87WUFDZFMsUUFBUVQsS0FBSyxDQUFDLHlCQUF5QkE7UUFDekM7SUFDRjtJQUVBLE1BQU1VLGlCQUFpQjtRQUNyQmYsYUFBYTtRQUNiTSxTQUFTO1FBRVQsSUFBSTtZQUNGLE1BQU1HLFdBQVcsTUFBTUMsTUFBTSxlQUFlO2dCQUMxQ00sUUFBUTtnQkFDUkMsU0FBUztvQkFBRSxnQkFBZ0I7Z0JBQW1CO1lBQ2hEO1lBRUEsTUFBTU4sT0FBTyxNQUFNRixTQUFTRyxJQUFJO1lBRWhDLElBQUlELEtBQUtFLE9BQU8sRUFBRTtnQkFDaEIsNkJBQTZCO2dCQUM3Qks7WUFDRixPQUFPO2dCQUNMWixTQUFTSyxLQUFLUSxPQUFPLElBQUk7Z0JBQ3pCbkIsYUFBYTtZQUNmO1FBQ0YsRUFBRSxPQUFPSyxPQUFPO1lBQ2RTLFFBQVFULEtBQUssQ0FBQywwQkFBMEJBO1lBQ3hDQyxTQUFTO1lBQ1ROLGFBQWE7UUFDZjtJQUNGO0lBRUEsTUFBTWtCLG1CQUFtQjtRQUN2QixNQUFNRSxlQUFlQyxZQUFZO1lBQy9CLElBQUk7Z0JBQ0YsTUFBTVosV0FBVyxNQUFNQyxNQUFNO2dCQUM3QixNQUFNQyxPQUFPLE1BQU1GLFNBQVNHLElBQUk7Z0JBRWhDLElBQUlELEtBQUtFLE9BQU8sSUFBSSxDQUFDRixLQUFLVyxNQUFNLENBQUNDLFlBQVksRUFBRTtvQkFDN0NDLGNBQWNKO29CQUNkcEIsYUFBYTtvQkFDYix5QkFBeUI7b0JBQ3pCTztvQkFDQUM7Z0JBQ0Y7WUFDRixFQUFFLE9BQU9ILE9BQU87Z0JBQ2RTLFFBQVFULEtBQUssQ0FBQyxnQ0FBZ0NBO2dCQUM5Q21CLGNBQWNKO2dCQUNkcEIsYUFBYTtZQUNmO1FBQ0YsR0FBRyxNQUFNLHVCQUF1Qjs7SUFDbEM7SUFFQSxNQUFNeUIsa0JBQWtCLENBQUNDLE9BQWVDO1FBQ3RDckMsUUFBUXNDLENBQUFBLFdBQ05BLFNBQVNDLEdBQUcsQ0FBQ0MsQ0FBQUEsTUFDWEEsSUFBSUMsTUFBTSxLQUFLTCxRQUFRO29CQUFFLEdBQUdJLEdBQUc7b0JBQUUsR0FBR0gsT0FBTztnQkFBQyxJQUFJRztRQUdwRCxpQ0FBaUM7UUFDakN0QjtJQUNGO0lBRUEsTUFBTXdCLGlCQUFpQjtRQUNyQixNQUFNQyxVQUFVNUMsS0FBSzZDLE1BQU0sQ0FBQ0osQ0FBQUEsTUFBT0EsSUFBSVIsTUFBTSxLQUFLO1FBRWxELElBQUlXLFFBQVFFLE1BQU0sS0FBSyxHQUFHO1lBQ3hCQyxNQUFNO1lBQ047UUFDRjtRQUVBOUIsU0FBUztRQUVULElBQUk7WUFDRixNQUFNRyxXQUFXLE1BQU1DLE1BQU0sMkJBQTJCO2dCQUN0RE0sUUFBUTtnQkFDUkMsU0FBUztvQkFBRSxnQkFBZ0I7Z0JBQW1CO2dCQUM5Q29CLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFDbkJDLFFBQVFQLFFBQVFKLEdBQUcsQ0FBQ0MsQ0FBQUEsTUFBT0EsSUFBSUMsTUFBTTtnQkFDdkM7WUFDRjtZQUVBLElBQUksQ0FBQ3RCLFNBQVNnQyxFQUFFLEVBQUU7Z0JBQ2hCLE1BQU1DLFlBQVksTUFBTWpDLFNBQVNrQyxJQUFJO2dCQUNyQzdCLFFBQVFULEtBQUssQ0FBQyx5QkFBeUJxQztnQkFDdkNwQyxTQUFTLENBQUMsaUJBQWlCLEVBQUVHLFNBQVNhLE1BQU0sQ0FBQyxDQUFDLEVBQUViLFNBQVNtQyxVQUFVLENBQUMsQ0FBQztnQkFDckU7WUFDRjtZQUVBLE1BQU1qQyxPQUFPLE1BQU1GLFNBQVNHLElBQUk7WUFFaEMsSUFBSUQsS0FBS0UsT0FBTyxFQUFFO2dCQUNoQixvQ0FBb0M7Z0JBQ3BDLE1BQU1nQyxjQUFjeEQsS0FBS3dDLEdBQUcsQ0FBQ0MsQ0FBQUE7b0JBQzNCLE1BQU1nQixTQUFTbkMsS0FBS29DLE9BQU8sQ0FBQ0MsSUFBSSxDQUFDLENBQUNDLElBQVdBLEVBQUVsQixNQUFNLEtBQUtELElBQUlDLE1BQU07b0JBQ3BFLE9BQU9lLFNBQVM7d0JBQUUsR0FBR2hCLEdBQUc7d0JBQUVvQixhQUFhSixPQUFPSSxXQUFXO3dCQUFFNUIsUUFBUTtvQkFBVyxJQUFJUTtnQkFDcEY7Z0JBQ0F4QyxRQUFRdUQ7Z0JBQ1JyQztnQkFDQTRCLE1BQU0sQ0FBQyxTQUFTLEVBQUV6QixLQUFLb0MsT0FBTyxDQUFDWixNQUFNLENBQUMsc0NBQXNDLEVBQUV4QixLQUFLd0MsT0FBTyxDQUFDQyxtQkFBbUIsQ0FBQyxlQUFlLEVBQUV6QyxLQUFLd0MsT0FBTyxDQUFDRSxpQkFBaUIsQ0FBQyxDQUFDO1lBQ2xLLE9BQU87Z0JBQ0wvQyxTQUFTSyxLQUFLTixLQUFLLElBQUk7WUFDekI7UUFDRixFQUFFLE9BQU9BLE9BQU87WUFDZFMsUUFBUVQsS0FBSyxDQUFDLHlCQUF5QkE7WUFDdkNDLFNBQVMsQ0FBQyxvQ0FBb0MsRUFBRUQsTUFBTWMsT0FBTyxDQUFDLENBQUM7UUFDakU7SUFDRjtJQUVBLE1BQU1tQyxzQkFBc0I7UUFDMUIsT0FBT2pFLEtBQUs2QyxNQUFNLENBQUNKLENBQUFBLE1BQU9BLElBQUlvQixXQUFXLElBQUksSUFBSUssSUFBSSxDQUFDLENBQUNDLEdBQUdDLElBQU1BLEVBQUVQLFdBQVcsR0FBR00sRUFBRU4sV0FBVztJQUMvRjtJQUVBLE1BQU1RLG9CQUFvQjtRQUN4QixPQUFPckUsS0FBSzZDLE1BQU0sQ0FBQ0osQ0FBQUEsTUFDakJBLElBQUk2QixXQUFXLENBQUNDLFdBQVcsR0FBR0MsUUFBUSxDQUFDLFNBQ3ZDL0IsSUFBSTZCLFdBQVcsQ0FBQ0MsV0FBVyxHQUFHQyxRQUFRLENBQUMsOEJBQ3ZDL0IsSUFBSTZCLFdBQVcsQ0FBQ0MsV0FBVyxHQUFHQyxRQUFRLENBQUMsaUJBQ3ZDL0IsSUFBSWdDLEtBQUssQ0FBQ0YsV0FBVyxHQUFHQyxRQUFRLENBQUM7SUFFckM7SUFFQSxJQUFJaEUsU0FBUztRQUNYLHFCQUNFLDhEQUFDa0U7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7Ozs7Ozs7Ozs7O0lBR3JCO0lBRUEscUJBQ0UsOERBQUNEO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNEO1lBQUlDLFdBQVU7OzhCQUViLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNDOzRCQUFHRCxXQUFVO3NDQUF3Qzs7Ozs7O3NDQUd0RCw4REFBQ0U7NEJBQUVGLFdBQVU7c0NBQWdCOzs7Ozs7Ozs7Ozs7Z0JBTTlCM0QsdUJBQ0MsOERBQUMwRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNFOzRCQUFFRixXQUFVO3NDQUFnQjNEOzs7Ozs7c0NBQzdCLDhEQUFDOEQ7NEJBQ0NDLFNBQVMsSUFBTTlELFNBQVM7NEJBQ3hCMEQsV0FBVTtzQ0FDWDs7Ozs7Ozs7Ozs7OzhCQU9MLDhEQUFDRDtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0s7d0JBQUlMLFdBQVU7OzBDQUNiLDhEQUFDRztnQ0FDQ0MsU0FBUyxJQUFNaEUsYUFBYTtnQ0FDNUI0RCxXQUFXLENBQUMseUNBQXlDLEVBQ25EN0QsY0FBYyxTQUNWLGtDQUNBLHVEQUNMLENBQUM7O2tEQUVGLDhEQUFDbEIsb0lBQVlBO3dDQUFDK0UsV0FBVTs7Ozs7O29DQUF3Qjs7Ozs7OzswQ0FHbEQsOERBQUNHO2dDQUNDQyxTQUFTLElBQU1oRSxhQUFhO2dDQUM1QjRELFdBQVcsQ0FBQyx5Q0FBeUMsRUFDbkQ3RCxjQUFjLFdBQ1Ysa0NBQ0EsdURBQ0wsQ0FBQzs7a0RBRUYsOERBQUNqQixvSUFBZ0JBO3dDQUFDOEUsV0FBVTs7Ozs7O29DQUF3Qjs7Ozs7OzswQ0FHdEQsOERBQUNHO2dDQUNDQyxTQUFTLElBQU1oRSxhQUFhO2dDQUM1QjRELFdBQVcsQ0FBQyx5Q0FBeUMsRUFDbkQ3RCxjQUFjLGNBQ1Ysa0NBQ0EsdURBQ0wsQ0FBQzs7a0RBRUYsOERBQUNoQixvSUFBWUE7d0NBQUM2RSxXQUFVOzs7Ozs7b0NBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0JBT3JEN0QsY0FBYyx3QkFDYjs7c0NBRUUsOERBQUM0RDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNqRixrREFBU0E7b0NBQUMrRSxPQUFNO29DQUFhUSxPQUFPL0UsTUFBTUUsS0FBSztvQ0FBRThFLE9BQU07Ozs7Ozs4Q0FDeEQsOERBQUN4RixrREFBU0E7b0NBQUMrRSxPQUFNO29DQUFXUSxPQUFPL0UsTUFBTUcsR0FBRztvQ0FBRTZFLE9BQU07Ozs7Ozs4Q0FDcEQsOERBQUN4RixrREFBU0E7b0NBQUMrRSxPQUFNO29DQUFXUSxPQUFPL0UsTUFBTUssUUFBUTtvQ0FBRTJFLE9BQU07Ozs7Ozs4Q0FDekQsOERBQUN4RixrREFBU0E7b0NBQUMrRSxPQUFNO29DQUFVUSxPQUFPL0UsTUFBTUksT0FBTztvQ0FBRTRFLE9BQU07Ozs7Ozs7Ozs7OztzQ0FJekQsOERBQUNSOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDbEYsbURBQVVBO2dDQUFDMEYsVUFBVXpEO2dDQUFnQmhCLFdBQVdBOzs7Ozs7Ozs7OztzQ0FJbkQsOERBQUNnRTs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0c7Z0NBQ0NDLFNBQVNwQztnQ0FDVGdDLFdBQVU7Z0NBQ1ZTLFVBQVVwRixLQUFLNkMsTUFBTSxDQUFDSixDQUFBQSxNQUFPQSxJQUFJUixNQUFNLEtBQUssT0FBT2EsTUFBTSxLQUFLOztrREFFOUQsOERBQUNsRCxvSUFBWUE7d0NBQUMrRSxXQUFVOzs7Ozs7b0NBQWlCOzs7Ozs7Ozs7Ozs7c0NBTTdDLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDVTt3Q0FBS1YsV0FBVTtrREFBZ0I7Ozs7OztrREFDaEMsOERBQUNHO3dDQUNDQyxTQUFTLElBQU05RSxRQUFRRCxLQUFLa0UsSUFBSSxDQUFDLENBQUNDLEdBQUdDLElBQU1BLEVBQUVQLFdBQVcsR0FBR00sRUFBRU4sV0FBVzt3Q0FDeEVjLFdBQVU7a0RBQ1g7Ozs7OztrREFHRCw4REFBQ0c7d0NBQ0NDLFNBQVMsSUFBTTlFLFFBQVFvRTt3Q0FDdkJNLFdBQVU7OzRDQUNYOzRDQUNtQk4sb0JBQW9CdkIsTUFBTTs0Q0FBQzs7Ozs7OztrREFFL0MsOERBQUNnQzt3Q0FDQ0MsU0FBUzdEO3dDQUNUeUQsV0FBVTtrREFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBT0wsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNaM0UsS0FBSzhDLE1BQU0sR0FBRyxJQUNiOUMsS0FBS3dDLEdBQUcsQ0FBQyxDQUFDQyxvQkFDUiw4REFBQ2pELGdEQUFPQTtvQ0FFTmlELEtBQUtBO29DQUNMNkMsYUFBYWxEO21DQUZSSyxJQUFJQyxNQUFNOzs7OzBEQU1uQiw4REFBQ2dDO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQy9FLG9JQUFZQTt3Q0FBQytFLFdBQVU7Ozs7OztrREFDeEIsOERBQUNZO3dDQUFHWixXQUFVO2tEQUF5Qzs7Ozs7O2tEQUN2RCw4REFBQ0U7d0NBQUVGLFdBQVU7a0RBQXFCOzs7Ozs7a0RBQ2xDLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNVOzBEQUFLOzs7Ozs7MERBQ04sOERBQUNBOzBEQUFLOzs7Ozs7MERBQ04sOERBQUNBOzBEQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dCQVFqQnZFLGNBQWMsMEJBQ2IsOERBQUNuQixzREFBYUE7b0JBQUMwQyxPQUFPekIsaUJBQWlCNEU7Ozs7OztnQkFHeEMxRSxjQUFjLDZCQUNiLDhEQUFDNEQ7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDYzs0QkFBR2QsV0FBVTtzQ0FBeUI7Ozs7OztzQ0FFdkMsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7O3NEQUNDLDhEQUFDYTs0Q0FBR1osV0FBVTtzREFBcUI7Ozs7OztzREFDbkMsOERBQUNEOzRDQUFJQyxXQUFVO3NEQUNaVixzQkFBc0J5QixLQUFLLENBQUMsR0FBRyxHQUFHbEQsR0FBRyxDQUFDQyxDQUFBQSxvQkFDckMsOERBQUNpQztvREFBcUJDLFdBQVU7O3NFQUM5Qiw4REFBQ1U7NERBQUtWLFdBQVU7O2dFQUFXbEMsSUFBSWdDLEtBQUs7Z0VBQUM7Z0VBQUtoQyxJQUFJa0QsT0FBTzs7Ozs7OztzRUFDckQsOERBQUNOOzREQUFLVixXQUFVOztnRUFBc0NsQyxJQUFJb0IsV0FBVztnRUFBQzs7Ozs7Ozs7bURBRjlEcEIsSUFBSUMsTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FRMUIsOERBQUNnQzs7c0RBQ0MsOERBQUNhOzRDQUFHWixXQUFVO3NEQUFxQjs7Ozs7O3NEQUNuQyw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQ1pOLG9CQUFvQnFCLEtBQUssQ0FBQyxHQUFHLEdBQUdsRCxHQUFHLENBQUNDLENBQUFBLG9CQUNuQyw4REFBQ2lDO29EQUFxQkMsV0FBVTs7c0VBQzlCLDhEQUFDVTs0REFBS1YsV0FBVTs7Z0VBQVdsQyxJQUFJZ0MsS0FBSztnRUFBQztnRUFBS2hDLElBQUlrRCxPQUFPOzs7Ozs7O3NFQUNyRCw4REFBQ047NERBQUtWLFdBQVU7c0VBQTBCOzs7Ozs7O21EQUZsQ2xDLElBQUlDLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQWF4QyIsInNvdXJjZXMiOlsid2VicGFjazovL2pvYi1haS1mcm9udGVuZC8uL2FwcC9jb21wb25lbnRzL0pvYkRhc2hib2FyZC50c3g/OWRjNiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCBKb2JDYXJkIGZyb20gJy4vSm9iQ2FyZCdcbmltcG9ydCBTZWFyY2hGb3JtIGZyb20gJy4vU2VhcmNoRm9ybSdcbmltcG9ydCBTdGF0c0NhcmQgZnJvbSAnLi9TdGF0c0NhcmQnXG5pbXBvcnQgUmVzdW1lQnVpbGRlciBmcm9tICcuL1Jlc3VtZUJ1aWxkZXInXG5pbXBvcnQgeyBTcGFya2xlc0ljb24sIERvY3VtZW50VGV4dEljb24sIENoYXJ0QmFySWNvbiB9IGZyb20gJ0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZSdcblxuaW50ZXJmYWNlIEpvYiB7XG4gIGpvYl9pZDogc3RyaW5nXG4gIHRpdGxlOiBzdHJpbmdcbiAgY29tcGFueTogc3RyaW5nXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmdcbiAgbG9jYXRpb246IHN0cmluZ1xuICBzb3VyY2U6IHN0cmluZ1xuICBwb3N0ZWRfZGF0ZTogc3RyaW5nXG4gIGFwcGx5X3VybDogc3RyaW5nXG4gIHN0YXR1czogc3RyaW5nXG4gIG1hdGNoX3Njb3JlOiBudW1iZXJcbn1cblxuaW50ZXJmYWNlIFN0YXRzIHtcbiAgdG90YWw6IG51bWJlclxuICBuZXc6IG51bWJlclxuICBhcHBsaWVkOiBudW1iZXJcbiAgYW5hbHl6ZWQ6IG51bWJlclxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBKb2JEYXNoYm9hcmQoKSB7XG4gIGNvbnN0IFtqb2JzLCBzZXRKb2JzXSA9IHVzZVN0YXRlPEpvYltdPihbXSlcbiAgY29uc3QgW3N0YXRzLCBzZXRTdGF0c10gPSB1c2VTdGF0ZTxTdGF0cz4oeyB0b3RhbDogMCwgbmV3OiAwLCBhcHBsaWVkOiAwLCBhbmFseXplZDogMCB9KVxuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKVxuICBjb25zdCBbc2VhcmNoaW5nLCBzZXRTZWFyY2hpbmddID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtzZWxlY3RlZEpvYklkLCBzZXRTZWxlY3RlZEpvYklkXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFthY3RpdmVUYWIsIHNldEFjdGl2ZVRhYl0gPSB1c2VTdGF0ZTwnam9icycgfCAncmVzdW1lJyB8ICdhbmFseXRpY3MnPignam9icycpXG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbClcblxuICAvLyBGZXRjaCBqb2JzIGFuZCBzdGF0cyBvbiBjb21wb25lbnQgbW91bnRcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBmZXRjaEpvYnMoKVxuICAgIGZldGNoU3RhdHMoKVxuICB9LCBbXSlcblxuICBjb25zdCBmZXRjaEpvYnMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvam9icycpXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG4gICAgICBcbiAgICAgIGlmIChkYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgc2V0Sm9icyhkYXRhLmpvYnMpXG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzZXRFcnJvcignRmFpbGVkIHRvIGZldGNoIGpvYnMnKVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBqb2JzOicsIGVycm9yKVxuICAgICAgc2V0RXJyb3IoJ05ldHdvcmsgZXJyb3Igd2hpbGUgZmV0Y2hpbmcgam9icycpXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgZmV0Y2hTdGF0cyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9zdGF0cycpXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG4gICAgICBcbiAgICAgIGlmIChkYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgc2V0U3RhdHMoZGF0YS5zdGF0cylcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgc3RhdHM6JywgZXJyb3IpXG4gICAgfVxuICB9XG5cbiAgY29uc3Qgc3RhcnRKb2JTZWFyY2ggPSBhc3luYyAoKSA9PiB7XG4gICAgc2V0U2VhcmNoaW5nKHRydWUpXG4gICAgc2V0RXJyb3IobnVsbClcbiAgICBcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9zZWFyY2gnLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7ICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicgfVxuICAgICAgfSlcbiAgICAgIFxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKVxuICAgICAgXG4gICAgICBpZiAoZGF0YS5zdWNjZXNzKSB7XG4gICAgICAgIC8vIFBvbGwgZm9yIHNlYXJjaCBjb21wbGV0aW9uXG4gICAgICAgIHBvbGxTZWFyY2hTdGF0dXMoKVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0RXJyb3IoZGF0YS5tZXNzYWdlIHx8ICdGYWlsZWQgdG8gc3RhcnQgam9iIHNlYXJjaCcpXG4gICAgICAgIHNldFNlYXJjaGluZyhmYWxzZSlcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igc3RhcnRpbmcgc2VhcmNoOicsIGVycm9yKVxuICAgICAgc2V0RXJyb3IoJ05ldHdvcmsgZXJyb3Igd2hpbGUgc3RhcnRpbmcgc2VhcmNoJylcbiAgICAgIHNldFNlYXJjaGluZyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBwb2xsU2VhcmNoU3RhdHVzID0gYXN5bmMgKCkgPT4ge1xuICAgIGNvbnN0IHBvbGxJbnRlcnZhbCA9IHNldEludGVydmFsKGFzeW5jICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvc2VhcmNoL3N0YXR1cycpXG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKClcbiAgICAgICAgXG4gICAgICAgIGlmIChkYXRhLnN1Y2Nlc3MgJiYgIWRhdGEuc3RhdHVzLmlzX3NlYXJjaGluZykge1xuICAgICAgICAgIGNsZWFySW50ZXJ2YWwocG9sbEludGVydmFsKVxuICAgICAgICAgIHNldFNlYXJjaGluZyhmYWxzZSlcbiAgICAgICAgICAvLyBSZWZyZXNoIGpvYnMgYW5kIHN0YXRzXG4gICAgICAgICAgZmV0Y2hKb2JzKClcbiAgICAgICAgICBmZXRjaFN0YXRzKClcbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgcG9sbGluZyBzZWFyY2ggc3RhdHVzOicsIGVycm9yKVxuICAgICAgICBjbGVhckludGVydmFsKHBvbGxJbnRlcnZhbClcbiAgICAgICAgc2V0U2VhcmNoaW5nKGZhbHNlKVxuICAgICAgfVxuICAgIH0sIDIwMDApIC8vIFBvbGwgZXZlcnkgMiBzZWNvbmRzXG4gIH1cblxuICBjb25zdCBoYW5kbGVKb2JVcGRhdGUgPSAoam9iSWQ6IHN0cmluZywgdXBkYXRlczogYW55KSA9PiB7XG4gICAgc2V0Sm9icyhwcmV2Sm9icyA9PiBcbiAgICAgIHByZXZKb2JzLm1hcChqb2IgPT4gXG4gICAgICAgIGpvYi5qb2JfaWQgPT09IGpvYklkID8geyAuLi5qb2IsIC4uLnVwZGF0ZXMgfSA6IGpvYlxuICAgICAgKVxuICAgIClcbiAgICAvLyBSZWZyZXNoIHN0YXRzIGFmdGVyIGpvYiB1cGRhdGVcbiAgICBmZXRjaFN0YXRzKClcbiAgfVxuXG4gIGNvbnN0IGFuYWx5emVBbGxKb2JzID0gYXN5bmMgKCkgPT4ge1xuICAgIGNvbnN0IG5ld0pvYnMgPSBqb2JzLmZpbHRlcihqb2IgPT4gam9iLnN0YXR1cyA9PT0gJ25ldycpXG5cbiAgICBpZiAobmV3Sm9icy5sZW5ndGggPT09IDApIHtcbiAgICAgIGFsZXJ0KCdObyBuZXcgam9icyB0byBhbmFseXplJylcbiAgICAgIHJldHVyblxuICAgIH1cblxuICAgIHNldEVycm9yKG51bGwpXG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9qb2JzL2FuYWx5emUtYmF0Y2gnLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7ICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgIGpvYklkczogbmV3Sm9icy5tYXAoam9iID0+IGpvYi5qb2JfaWQpXG4gICAgICAgIH0pXG4gICAgICB9KVxuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnN0IGVycm9yVGV4dCA9IGF3YWl0IHJlc3BvbnNlLnRleHQoKVxuICAgICAgICBjb25zb2xlLmVycm9yKCdCYXRjaCBhbmFseXNpcyBlcnJvcjonLCBlcnJvclRleHQpXG4gICAgICAgIHNldEVycm9yKGBBbmFseXNpcyBmYWlsZWQ6ICR7cmVzcG9uc2Uuc3RhdHVzfSAke3Jlc3BvbnNlLnN0YXR1c1RleHR9YClcbiAgICAgICAgcmV0dXJuXG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKClcblxuICAgICAgaWYgKGRhdGEuc3VjY2Vzcykge1xuICAgICAgICAvLyBVcGRhdGUgam9icyB3aXRoIGFuYWx5c2lzIHJlc3VsdHNcbiAgICAgICAgY29uc3QgdXBkYXRlZEpvYnMgPSBqb2JzLm1hcChqb2IgPT4ge1xuICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IGRhdGEucmVzdWx0cy5maW5kKChyOiBhbnkpID0+IHIuam9iX2lkID09PSBqb2Iuam9iX2lkKVxuICAgICAgICAgIHJldHVybiByZXN1bHQgPyB7IC4uLmpvYiwgbWF0Y2hfc2NvcmU6IHJlc3VsdC5tYXRjaF9zY29yZSwgc3RhdHVzOiAnYW5hbHl6ZWQnIH0gOiBqb2JcbiAgICAgICAgfSlcbiAgICAgICAgc2V0Sm9icyh1cGRhdGVkSm9icylcbiAgICAgICAgZmV0Y2hTdGF0cygpXG4gICAgICAgIGFsZXJ0KGBBbmFseXplZCAke2RhdGEucmVzdWx0cy5sZW5ndGh9IGpvYnMgc3VjY2Vzc2Z1bGx5IVxcblxcbkhpZ2ggUHJpb3JpdHk6ICR7ZGF0YS5zdW1tYXJ5LmhpZ2hfcHJpb3JpdHlfY291bnR9XFxuQUkgUmVsZXZhbnQ6ICR7ZGF0YS5zdW1tYXJ5LmFpX3JlbGV2YW50X2NvdW50fWApXG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzZXRFcnJvcihkYXRhLmVycm9yIHx8ICdGYWlsZWQgdG8gYW5hbHl6ZSBqb2JzJylcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgYW5hbHl6aW5nIGpvYnM6JywgZXJyb3IpXG4gICAgICBzZXRFcnJvcihgTmV0d29yayBlcnJvciB3aGlsZSBhbmFseXppbmcgam9iczogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgfVxuICB9XG5cbiAgY29uc3QgZ2V0SGlnaFByaW9yaXR5Sm9icyA9ICgpID0+IHtcbiAgICByZXR1cm4gam9icy5maWx0ZXIoam9iID0+IGpvYi5tYXRjaF9zY29yZSA+PSA3MCkuc29ydCgoYSwgYikgPT4gYi5tYXRjaF9zY29yZSAtIGEubWF0Y2hfc2NvcmUpXG4gIH1cblxuICBjb25zdCBnZXRBSVJlbGV2YW50Sm9icyA9ICgpID0+IHtcbiAgICByZXR1cm4gam9icy5maWx0ZXIoam9iID0+IFxuICAgICAgam9iLmRlc2NyaXB0aW9uLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoJ2FpJykgfHwgXG4gICAgICBqb2IuZGVzY3JpcHRpb24udG9Mb3dlckNhc2UoKS5pbmNsdWRlcygnYXJ0aWZpY2lhbCBpbnRlbGxpZ2VuY2UnKSB8fFxuICAgICAgam9iLmRlc2NyaXB0aW9uLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoJ2F1dG9tYXRpb24nKSB8fFxuICAgICAgam9iLnRpdGxlLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoJ2FpJylcbiAgICApXG4gIH1cblxuICBpZiAobG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG1pbi1oLXNjcmVlblwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC0xMiB3LTEyIGJvcmRlci1iLTIgYm9yZGVyLWJsdWUtNjAwXCI+PC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktNTBcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LTggcHktOFwiPlxuICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLThcIj5cbiAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItMlwiPlxuICAgICAgICAgICAgSm9iIEFJIERhc2hib2FyZFxuICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAg8J+HrvCfh7MgUmVhbCBqb2Igb3Bwb3J0dW5pdGllcyBmcm9tIEludGVybnNoYWxhLCBJbmRlZWQgSW5kaWEsIGFuZCBOYXVrcmkuY29tIHdpdGggYXV0aGVudGljIFVSTHNcbiAgICAgICAgICA8L3A+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBFcnJvciBEaXNwbGF5ICovfVxuICAgICAgICB7ZXJyb3IgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNiBwLTQgYmctcmVkLTUwIGJvcmRlciBib3JkZXItcmVkLTIwMCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXJlZC04MDBcIj57ZXJyb3J9PC9wPlxuICAgICAgICAgICAgPGJ1dHRvbiBcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0RXJyb3IobnVsbCl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtcmVkLTYwMCBob3Zlcjp0ZXh0LXJlZC04MDAgdGV4dC1zbSBtdC0yXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgRGlzbWlzc1xuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAgey8qIE5hdmlnYXRpb24gVGFicyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02XCI+XG4gICAgICAgICAgPG5hdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtOFwiPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVUYWIoJ2pvYnMnKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHktMiBweC0xIGJvcmRlci1iLTIgZm9udC1tZWRpdW0gdGV4dC1zbSAke1xuICAgICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gJ2pvYnMnXG4gICAgICAgICAgICAgICAgICA/ICdib3JkZXItYmx1ZS01MDAgdGV4dC1ibHVlLTYwMCdcbiAgICAgICAgICAgICAgICAgIDogJ2JvcmRlci10cmFuc3BhcmVudCB0ZXh0LWdyYXktNTAwIGhvdmVyOnRleHQtZ3JheS03MDAnXG4gICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8U3BhcmtsZXNJY29uIGNsYXNzTmFtZT1cImgtNSB3LTUgaW5saW5lIG1yLTJcIiAvPlxuICAgICAgICAgICAgICBKb2JzICYgQUkgQW5hbHlzaXNcbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVUYWIoJ3Jlc3VtZScpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweS0yIHB4LTEgYm9yZGVyLWItMiBmb250LW1lZGl1bSB0ZXh0LXNtICR7XG4gICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSAncmVzdW1lJ1xuICAgICAgICAgICAgICAgICAgPyAnYm9yZGVyLWJsdWUtNTAwIHRleHQtYmx1ZS02MDAnXG4gICAgICAgICAgICAgICAgICA6ICdib3JkZXItdHJhbnNwYXJlbnQgdGV4dC1ncmF5LTUwMCBob3Zlcjp0ZXh0LWdyYXktNzAwJ1xuICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPERvY3VtZW50VGV4dEljb24gY2xhc3NOYW1lPVwiaC01IHctNSBpbmxpbmUgbXItMlwiIC8+XG4gICAgICAgICAgICAgIFJlc3VtZSBCdWlsZGVyXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlVGFiKCdhbmFseXRpY3MnKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHktMiBweC0xIGJvcmRlci1iLTIgZm9udC1tZWRpdW0gdGV4dC1zbSAke1xuICAgICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gJ2FuYWx5dGljcydcbiAgICAgICAgICAgICAgICAgID8gJ2JvcmRlci1ibHVlLTUwMCB0ZXh0LWJsdWUtNjAwJ1xuICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLXRyYW5zcGFyZW50IHRleHQtZ3JheS01MDAgaG92ZXI6dGV4dC1ncmF5LTcwMCdcbiAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxDaGFydEJhckljb24gY2xhc3NOYW1lPVwiaC01IHctNSBpbmxpbmUgbXItMlwiIC8+XG4gICAgICAgICAgICAgIEFuYWx5dGljc1xuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9uYXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBUYWIgQ29udGVudCAqL31cbiAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ2pvYnMnICYmIChcbiAgICAgICAgICA8PlxuICAgICAgICAgICAgey8qIFN0YXRzIENhcmRzICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy00IGdhcC02IG1iLThcIj5cbiAgICAgICAgICAgICAgPFN0YXRzQ2FyZCB0aXRsZT1cIlRvdGFsIEpvYnNcIiB2YWx1ZT17c3RhdHMudG90YWx9IGNvbG9yPVwiYmx1ZVwiIC8+XG4gICAgICAgICAgICAgIDxTdGF0c0NhcmQgdGl0bGU9XCJOZXcgSm9ic1wiIHZhbHVlPXtzdGF0cy5uZXd9IGNvbG9yPVwiZ3JlZW5cIiAvPlxuICAgICAgICAgICAgICA8U3RhdHNDYXJkIHRpdGxlPVwiQW5hbHl6ZWRcIiB2YWx1ZT17c3RhdHMuYW5hbHl6ZWR9IGNvbG9yPVwicHVycGxlXCIgLz5cbiAgICAgICAgICAgICAgPFN0YXRzQ2FyZCB0aXRsZT1cIkFwcGxpZWRcIiB2YWx1ZT17c3RhdHMuYXBwbGllZH0gY29sb3I9XCJibHVlXCIgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogU2VhcmNoIEZvcm0gKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLThcIj5cbiAgICAgICAgICAgICAgPFNlYXJjaEZvcm0gb25TZWFyY2g9e3N0YXJ0Sm9iU2VhcmNofSBzZWFyY2hpbmc9e3NlYXJjaGluZ30gLz5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogQnVsayBBY3Rpb25zICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02IGZsZXggc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXthbmFseXplQWxsSm9ic31cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJidG4tc2Vjb25kYXJ5IGZsZXggaXRlbXMtY2VudGVyXCJcbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17am9icy5maWx0ZXIoam9iID0+IGpvYi5zdGF0dXMgPT09ICduZXcnKS5sZW5ndGggPT09IDB9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8U3BhcmtsZXNJY29uIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgQW5hbHl6ZSBBbGwgTmV3IEpvYnNcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIEpvYiBGaWx0ZXJzICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTQgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5RdWljayBGaWx0ZXJzOjwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uIFxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0Sm9icyhqb2JzLnNvcnQoKGEsIGIpID0+IGIubWF0Y2hfc2NvcmUgLSBhLm1hdGNoX3Njb3JlKSl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNjAwIGhvdmVyOnRleHQtYmx1ZS04MDBcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIEhpZ2ggTWF0Y2ggU2NvcmVcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uIFxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0Sm9icyhnZXRBSVJlbGV2YW50Sm9icygpKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtcHVycGxlLTYwMCBob3Zlcjp0ZXh0LXB1cnBsZS04MDBcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIEFJLVJlbGF0ZWQgSm9icyAoe2dldEFJUmVsZXZhbnRKb2JzKCkubGVuZ3RofSlcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uIFxuICAgICAgICAgICAgICAgICAgb25DbGljaz17ZmV0Y2hKb2JzfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBob3Zlcjp0ZXh0LWdyYXktODAwXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBTaG93IEFsbFxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogSm9icyBHcmlkICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdhcC02XCI+XG4gICAgICAgICAgICAgIHtqb2JzLmxlbmd0aCA+IDAgPyAoXG4gICAgICAgICAgICAgICAgam9icy5tYXAoKGpvYikgPT4gKFxuICAgICAgICAgICAgICAgICAgPEpvYkNhcmQgXG4gICAgICAgICAgICAgICAgICAgIGtleT17am9iLmpvYl9pZH0gXG4gICAgICAgICAgICAgICAgICAgIGpvYj17am9ifSBcbiAgICAgICAgICAgICAgICAgICAgb25Kb2JVcGRhdGU9e2hhbmRsZUpvYlVwZGF0ZX1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgKSlcbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTEyXCI+XG4gICAgICAgICAgICAgICAgICA8U3BhcmtsZXNJY29uIGNsYXNzTmFtZT1cImgtMTYgdy0xNiB0ZXh0LWdyYXktNDAwIG14LWF1dG8gbWItNFwiIC8+XG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTJcIj5ObyBKb2JzIEZvdW5kPC9oMz5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNFwiPkNsaWNrIFwiU2VhcmNoIFJlYWwgSm9ic1wiIHRvIGZpbmQgb3Bwb3J0dW5pdGllcyBmcm9tIEluZGlhbiBqb2IgcG9ydGFsczwvcD5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBzcGFjZS14LTQgdGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuPvCfjpMgSW50ZXJuc2hhbGE8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuPvCflI0gSW5kZWVkIEluZGlhPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8c3Bhbj7wn5K8IE5hdWtyaS5jb208L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvPlxuICAgICAgICApfVxuXG4gICAgICAgIHthY3RpdmVUYWIgPT09ICdyZXN1bWUnICYmIChcbiAgICAgICAgICA8UmVzdW1lQnVpbGRlciBqb2JJZD17c2VsZWN0ZWRKb2JJZCB8fCB1bmRlZmluZWR9IC8+XG4gICAgICAgICl9XG5cbiAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ2FuYWx5dGljcycgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3cgcC02XCI+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgbWItNFwiPkpvYiBBbmFseXRpY3M8L2gyPlxuICAgICAgICAgICAgXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTZcIj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCBtYi0yXCI+SGlnaCBQcmlvcml0eSBKb2JzPC9oMz5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAge2dldEhpZ2hQcmlvcml0eUpvYnMoKS5zbGljZSgwLCA1KS5tYXAoam9iID0+IChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2pvYi5qb2JfaWR9IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBwLTIgYmctZ3JheS01MCByb3VuZGVkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbVwiPntqb2IudGl0bGV9IGF0IHtqb2IuY29tcGFueX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyZWVuLTYwMFwiPntqb2IubWF0Y2hfc2NvcmV9JTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIG1iLTJcIj5BSS1SZWxhdGVkIE9wcG9ydHVuaXRpZXM8L2gzPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICB7Z2V0QUlSZWxldmFudEpvYnMoKS5zbGljZSgwLCA1KS5tYXAoam9iID0+IChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2pvYi5qb2JfaWR9IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBwLTIgYmctcHVycGxlLTUwIHJvdW5kZWRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+e2pvYi50aXRsZX0gYXQge2pvYi5jb21wYW55fTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtcHVycGxlLTYwMFwiPkFJIFJvbGU8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiSm9iQ2FyZCIsIlNlYXJjaEZvcm0iLCJTdGF0c0NhcmQiLCJSZXN1bWVCdWlsZGVyIiwiU3BhcmtsZXNJY29uIiwiRG9jdW1lbnRUZXh0SWNvbiIsIkNoYXJ0QmFySWNvbiIsIkpvYkRhc2hib2FyZCIsImpvYnMiLCJzZXRKb2JzIiwic3RhdHMiLCJzZXRTdGF0cyIsInRvdGFsIiwibmV3IiwiYXBwbGllZCIsImFuYWx5emVkIiwibG9hZGluZyIsInNldExvYWRpbmciLCJzZWFyY2hpbmciLCJzZXRTZWFyY2hpbmciLCJzZWxlY3RlZEpvYklkIiwic2V0U2VsZWN0ZWRKb2JJZCIsImFjdGl2ZVRhYiIsInNldEFjdGl2ZVRhYiIsImVycm9yIiwic2V0RXJyb3IiLCJmZXRjaEpvYnMiLCJmZXRjaFN0YXRzIiwicmVzcG9uc2UiLCJmZXRjaCIsImRhdGEiLCJqc29uIiwic3VjY2VzcyIsImNvbnNvbGUiLCJzdGFydEpvYlNlYXJjaCIsIm1ldGhvZCIsImhlYWRlcnMiLCJwb2xsU2VhcmNoU3RhdHVzIiwibWVzc2FnZSIsInBvbGxJbnRlcnZhbCIsInNldEludGVydmFsIiwic3RhdHVzIiwiaXNfc2VhcmNoaW5nIiwiY2xlYXJJbnRlcnZhbCIsImhhbmRsZUpvYlVwZGF0ZSIsImpvYklkIiwidXBkYXRlcyIsInByZXZKb2JzIiwibWFwIiwiam9iIiwiam9iX2lkIiwiYW5hbHl6ZUFsbEpvYnMiLCJuZXdKb2JzIiwiZmlsdGVyIiwibGVuZ3RoIiwiYWxlcnQiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsImpvYklkcyIsIm9rIiwiZXJyb3JUZXh0IiwidGV4dCIsInN0YXR1c1RleHQiLCJ1cGRhdGVkSm9icyIsInJlc3VsdCIsInJlc3VsdHMiLCJmaW5kIiwiciIsIm1hdGNoX3Njb3JlIiwic3VtbWFyeSIsImhpZ2hfcHJpb3JpdHlfY291bnQiLCJhaV9yZWxldmFudF9jb3VudCIsImdldEhpZ2hQcmlvcml0eUpvYnMiLCJzb3J0IiwiYSIsImIiLCJnZXRBSVJlbGV2YW50Sm9icyIsImRlc2NyaXB0aW9uIiwidG9Mb3dlckNhc2UiLCJpbmNsdWRlcyIsInRpdGxlIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJwIiwiYnV0dG9uIiwib25DbGljayIsIm5hdiIsInZhbHVlIiwiY29sb3IiLCJvblNlYXJjaCIsImRpc2FibGVkIiwic3BhbiIsIm9uSm9iVXBkYXRlIiwiaDMiLCJ1bmRlZmluZWQiLCJoMiIsInNsaWNlIiwiY29tcGFueSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/components/JobDashboard.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ResumeBuilder.tsx":
/*!******************************************!*\
  !*** ./app/components/ResumeBuilder.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ResumeBuilder)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,DocumentTextIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,DocumentTextIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,DocumentTextIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ResumeBuilder({ jobId }) {\n    const [resumeData, setResumeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [jobInfo, setJobInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [analysis, setAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isCustomizing, setIsCustomizing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGeneratingPDF, setIsGeneratingPDF] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const customizeForJob = async ()=>{\n        if (!jobId) {\n            setError(\"No job ID provided for customization\");\n            return;\n        }\n        setIsCustomizing(true);\n        setError(null);\n        try {\n            const response = await fetch(\"/api/resume/customize\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    jobId\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                setResumeData(data.resume);\n                setAnalysis(data.analysis);\n                setJobInfo(data.job_info);\n            } else {\n                setError(data.error || \"Failed to customize resume\");\n            }\n        } catch (error) {\n            console.error(\"Error customizing resume:\", error);\n            setError(\"Network error while customizing resume\");\n        } finally{\n            setIsCustomizing(false);\n        }\n    };\n    const generateAndDownloadPDF = async ()=>{\n        if (!resumeData) {\n            setError(\"No resume data available for PDF generation\");\n            return;\n        }\n        setIsGeneratingPDF(true);\n        setError(null);\n        try {\n            const response = await fetch(\"/api/resume/generate-pdf\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    resumeData,\n                    jobId: jobId || \"general\"\n                })\n            });\n            if (response.ok) {\n                const blob = await response.blob();\n                const url = window.URL.createObjectURL(blob);\n                const a = document.createElement(\"a\");\n                a.href = url;\n                // Create a meaningful filename\n                const jobTitle = resumeData.customized_for || \"general\";\n                const fileName = `${resumeData.contact_info?.name || \"Resume\"}_${jobTitle.replace(/[^a-zA-Z0-9]/g, \"_\")}.pdf`;\n                a.download = fileName;\n                document.body.appendChild(a);\n                a.click();\n                document.body.removeChild(a);\n                window.URL.revokeObjectURL(url);\n            } else {\n                const errorData = await response.json();\n                setError(errorData.error || \"Failed to generate PDF\");\n            }\n        } catch (error) {\n            console.error(\"Error generating PDF:\", error);\n            setError(\"Network error while generating PDF\");\n        } finally{\n            setIsGeneratingPDF(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"h-8 w-8 text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"AI Resume Builder\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Create ATS-optimized resumes for specific jobs\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this),\n                    resumeData?.match_score && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-right\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Job Match Score\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `text-2xl font-bold ${resumeData.match_score >= 80 ? \"text-green-600\" : resumeData.match_score >= 60 ? \"text-yellow-600\" : \"text-red-600\"}`,\n                                children: [\n                                    resumeData.match_score,\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 p-4 bg-red-50 border border-red-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-800\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                lineNumber: 171,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: customizeForJob,\n                        disabled: isCustomizing || !jobId,\n                        className: \"btn-primary flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: isCustomizing ? \"Customizing...\" : \"AI Customize Resume\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: generateAndDownloadPDF,\n                        disabled: !resumeData || isGeneratingPDF,\n                        className: \"btn-secondary flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: isGeneratingPDF ? \"Generating...\" : \"Download PDF\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, this),\n            jobInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-blue-900 mb-2\",\n                        children: \"Customizing for:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-blue-800\",\n                        children: [\n                            jobInfo.title,\n                            \" at \",\n                            jobInfo.company\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 11\n                    }, this),\n                    resumeData?.ai_emphasis_level && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-blue-600 mt-1\",\n                        children: [\n                            \"AI Emphasis Level: \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"capitalize\",\n                                children: resumeData.ai_emphasis_level\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 34\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                lineNumber: 199,\n                columnNumber: 9\n            }, this),\n            resumeData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"resume-preview bg-gray-50 p-6 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-4 text-gray-900\",\n                        children: \"Resume Preview\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 11\n                    }, this),\n                    resumeData.contact_info && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-white rounded-lg border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800 mb-3\",\n                                children: \"Contact Information\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-3 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Name:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 22\n                                            }, this),\n                                            \" \",\n                                            resumeData.contact_info.name\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Email:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 22\n                                            }, this),\n                                            \" \",\n                                            resumeData.contact_info.email\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Phone:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 22\n                                            }, this),\n                                            \" \",\n                                            resumeData.contact_info.phone\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Location:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 22\n                                            }, this),\n                                            \" \",\n                                            resumeData.contact_info.location\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 17\n                                    }, this),\n                                    resumeData.contact_info.portfolio_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Portfolio:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 24\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: resumeData.contact_info.portfolio_url,\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                className: \"text-blue-600 hover:text-blue-800 ml-1\",\n                                                children: resumeData.contact_info.portfolio_url\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 19\n                                    }, this),\n                                    resumeData.contact_info.linkedin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"LinkedIn:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 24\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: resumeData.contact_info.linkedin,\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                className: \"text-blue-600 hover:text-blue-800 ml-1\",\n                                                children: \"LinkedIn Profile\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 13\n                    }, this),\n                    resumeData.professional_summary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-white rounded-lg border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800 mb-2\",\n                                children: \"Professional Summary\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-700 leading-relaxed\",\n                                children: resumeData.professional_summary\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 13\n                    }, this),\n                    resumeData.highlighted_skills && resumeData.highlighted_skills.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-white rounded-lg border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800 mb-3\",\n                                children: \"Core Skills\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: resumeData.highlighted_skills.map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full\",\n                                        children: skill\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 13\n                    }, this),\n                    resumeData.ai_expertise && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-purple-50 rounded-lg border border-purple-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-purple-800 mb-3\",\n                                children: \"\\uD83E\\uDD16 AI Expertise & Automation\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 15\n                            }, this),\n                            resumeData.ai_expertise.prompt_engineering && resumeData.ai_expertise.prompt_engineering.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-purple-700 mb-1\",\n                                        children: \"Prompt Engineering\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"list-disc list-inside text-sm text-purple-600\",\n                                        children: resumeData.ai_expertise.prompt_engineering.map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: skill\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 17\n                            }, this),\n                            resumeData.ai_expertise.ai_tools && resumeData.ai_expertise.ai_tools.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-purple-700 mb-1\",\n                                        children: \"AI Tools Mastery\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-1\",\n                                        children: resumeData.ai_expertise.ai_tools.map((tool, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded\",\n                                                children: tool\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 17\n                            }, this),\n                            resumeData.ai_expertise.automation && resumeData.ai_expertise.automation.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-purple-700 mb-1\",\n                                        children: \"Workflow Automation\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"list-disc list-inside text-sm text-purple-600\",\n                                        children: resumeData.ai_expertise.automation.map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: skill\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 13\n                    }, this),\n                    resumeData.experience && resumeData.experience.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-white rounded-lg border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800 mb-3\",\n                                children: \"Professional Experience\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 15\n                            }, this),\n                            resumeData.experience.map((exp, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 last:mb-0 pb-4 last:pb-0 border-b last:border-b-0 border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-gray-900 text-lg\",\n                                                            children: exp.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700 font-medium\",\n                                                            children: exp.company\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        exp.type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: exp.type\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 36\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600 font-medium\",\n                                                    children: exp.duration\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 19\n                                        }, this),\n                                        exp.achievements && exp.achievements.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"font-medium text-gray-800 mb-2\",\n                                                    children: \"Key Achievements:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"list-disc list-inside text-sm text-gray-700 space-y-1\",\n                                                    children: exp.achievements.map((achievement, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: achievement\n                                                        }, i, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 17\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 13\n                    }, this),\n                    resumeData.projects && resumeData.projects.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-white rounded-lg border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800 mb-3\",\n                                children: \"Key Projects\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 15\n                            }, this),\n                            resumeData.projects.map((project, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 last:mb-0 pb-4 last:pb-0 border-b last:border-b-0 border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-gray-900 text-lg\",\n                                                    children: project.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 21\n                                                }, this),\n                                                project.role && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 font-medium\",\n                                                    children: project.role\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 38\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 text-sm mb-3 leading-relaxed\",\n                                            children: project.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 19\n                                        }, this),\n                                        project.technologies && project.technologies.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"text-xs font-medium text-gray-600 mb-1\",\n                                                    children: \"Technologies:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-1\",\n                                                    children: project.technologies.map((tech, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded\",\n                                                            children: tech\n                                                        }, i, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 21\n                                        }, this),\n                                        project.achievements && project.achievements.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"text-xs font-medium text-gray-600 mb-1\",\n                                                    children: \"Impact & Results:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"list-disc list-inside text-sm text-gray-700 space-y-1\",\n                                                    children: project.achievements.map((achievement, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: achievement\n                                                        }, i, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 17\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 13\n                    }, this),\n                    resumeData.education && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-white rounded-lg border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800 mb-3\",\n                                children: \"Education\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-gray-900 text-lg\",\n                                        children: resumeData.education.degree\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700 font-medium\",\n                                        children: resumeData.education.institution\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4 text-sm text-gray-600 mt-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: resumeData.education.duration\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 19\n                                            }, this),\n                                            resumeData.education.status && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: [\n                                                    \"(\",\n                                                    resumeData.education.status,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 51\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 15\n                            }, this),\n                            resumeData.education.relevant_coursework && resumeData.education.relevant_coursework.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        className: \"text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"Relevant Coursework:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-1\",\n                                        children: resumeData.education.relevant_coursework.map((course, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 py-1 bg-green-100 text-green-700 text-xs rounded\",\n                                                children: course\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 13\n                    }, this),\n                    resumeData.certifications && resumeData.certifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-white rounded-lg border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800 mb-3\",\n                                children: \"Certifications\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-2\",\n                                children: resumeData.certifications.map((cert, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex items-center text-sm text-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-blue-500 rounded-full mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 21\n                                            }, this),\n                                            cert\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 406,\n                        columnNumber: 13\n                    }, this),\n                    resumeData.unique_value_props && resumeData.unique_value_props.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800 mb-3\",\n                                children: \"\\uD83C\\uDF1F Unique Value Propositions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-2\",\n                                children: resumeData.unique_value_props.map((prop, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex items-start text-sm text-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-500 mr-2 mt-1\",\n                                                children: \"✨\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: prop\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 421,\n                        columnNumber: 13\n                    }, this),\n                    resumeData.customization_notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-yellow-50 rounded-lg border border-yellow-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-semibold text-yellow-800 mb-2\",\n                                children: \"\\uD83D\\uDCDD Customization Notes\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-yellow-700\",\n                                children: resumeData.customization_notes\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 436,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                lineNumber: 212,\n                columnNumber: 9\n            }, this),\n            !resumeData && !isCustomizing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 446,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"No Resume Data\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 447,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: jobId ? 'Click \"AI Customize Resume\" to generate a job-specific resume' : \"Select a job to customize your resume\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                        lineNumber: 448,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n                lineNumber: 445,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\ResumeBuilder.tsx\",\n        lineNumber: 145,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9SZXN1bWVCdWlsZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFDMkM7QUFDb0Q7QUFzRGhGLFNBQVNJLGNBQWMsRUFBRUMsS0FBSyxFQUFzQjtJQUNqRSxNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBR1AsK0NBQVFBLENBQW9CO0lBQ2hFLE1BQU0sQ0FBQ1EsU0FBU0MsV0FBVyxHQUFHVCwrQ0FBUUEsQ0FBaUI7SUFDdkQsTUFBTSxDQUFDVSxVQUFVQyxZQUFZLEdBQUdYLCtDQUFRQSxDQUFNO0lBQzlDLE1BQU0sQ0FBQ1ksZUFBZUMsaUJBQWlCLEdBQUdiLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ2MsaUJBQWlCQyxtQkFBbUIsR0FBR2YsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDZ0IsT0FBT0MsU0FBUyxHQUFHakIsK0NBQVFBLENBQWdCO0lBRWxELE1BQU1rQixrQkFBa0I7UUFDdEIsSUFBSSxDQUFDYixPQUFPO1lBQ1ZZLFNBQVM7WUFDVDtRQUNGO1FBRUFKLGlCQUFpQjtRQUNqQkksU0FBUztRQUVULElBQUk7WUFDRixNQUFNRSxXQUFXLE1BQU1DLE1BQU0seUJBQXlCO2dCQUNwREMsUUFBUTtnQkFDUkMsU0FBUztvQkFBRSxnQkFBZ0I7Z0JBQW1CO2dCQUM5Q0MsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO29CQUFFcEI7Z0JBQU07WUFDL0I7WUFFQSxNQUFNcUIsT0FBTyxNQUFNUCxTQUFTUSxJQUFJO1lBRWhDLElBQUlELEtBQUtFLE9BQU8sRUFBRTtnQkFDaEJyQixjQUFjbUIsS0FBS0csTUFBTTtnQkFDekJsQixZQUFZZSxLQUFLaEIsUUFBUTtnQkFDekJELFdBQVdpQixLQUFLSSxRQUFRO1lBQzFCLE9BQU87Z0JBQ0xiLFNBQVNTLEtBQUtWLEtBQUssSUFBSTtZQUN6QjtRQUNGLEVBQUUsT0FBT0EsT0FBTztZQUNkZSxRQUFRZixLQUFLLENBQUMsNkJBQTZCQTtZQUMzQ0MsU0FBUztRQUNYLFNBQVU7WUFDUkosaUJBQWlCO1FBQ25CO0lBQ0Y7SUFFQSxNQUFNbUIseUJBQXlCO1FBQzdCLElBQUksQ0FBQzFCLFlBQVk7WUFDZlcsU0FBUztZQUNUO1FBQ0Y7UUFFQUYsbUJBQW1CO1FBQ25CRSxTQUFTO1FBRVQsSUFBSTtZQUNGLE1BQU1FLFdBQVcsTUFBTUMsTUFBTSw0QkFBNEI7Z0JBQ3ZEQyxRQUFRO2dCQUNSQyxTQUFTO29CQUFFLGdCQUFnQjtnQkFBbUI7Z0JBQzlDQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQ25CbkI7b0JBQ0FELE9BQU9BLFNBQVM7Z0JBQ2xCO1lBQ0Y7WUFFQSxJQUFJYyxTQUFTYyxFQUFFLEVBQUU7Z0JBQ2YsTUFBTUMsT0FBTyxNQUFNZixTQUFTZSxJQUFJO2dCQUNoQyxNQUFNQyxNQUFNQyxPQUFPQyxHQUFHLENBQUNDLGVBQWUsQ0FBQ0o7Z0JBQ3ZDLE1BQU1LLElBQUlDLFNBQVNDLGFBQWEsQ0FBQztnQkFDakNGLEVBQUVHLElBQUksR0FBR1A7Z0JBRVQsK0JBQStCO2dCQUMvQixNQUFNUSxXQUFXckMsV0FBV3NDLGNBQWMsSUFBSTtnQkFDOUMsTUFBTUMsV0FBVyxDQUFDLEVBQUV2QyxXQUFXd0MsWUFBWSxFQUFFQyxRQUFRLFNBQVMsQ0FBQyxFQUFFSixTQUFTSyxPQUFPLENBQUMsaUJBQWlCLEtBQUssSUFBSSxDQUFDO2dCQUM3R1QsRUFBRVUsUUFBUSxHQUFHSjtnQkFFYkwsU0FBU2pCLElBQUksQ0FBQzJCLFdBQVcsQ0FBQ1g7Z0JBQzFCQSxFQUFFWSxLQUFLO2dCQUNQWCxTQUFTakIsSUFBSSxDQUFDNkIsV0FBVyxDQUFDYjtnQkFDMUJILE9BQU9DLEdBQUcsQ0FBQ2dCLGVBQWUsQ0FBQ2xCO1lBQzdCLE9BQU87Z0JBQ0wsTUFBTW1CLFlBQVksTUFBTW5DLFNBQVNRLElBQUk7Z0JBQ3JDVixTQUFTcUMsVUFBVXRDLEtBQUssSUFBSTtZQUM5QjtRQUNGLEVBQUUsT0FBT0EsT0FBTztZQUNkZSxRQUFRZixLQUFLLENBQUMseUJBQXlCQTtZQUN2Q0MsU0FBUztRQUNYLFNBQVU7WUFDUkYsbUJBQW1CO1FBQ3JCO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ3dDO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ3ZELHlJQUFnQkE7Z0NBQUN1RCxXQUFVOzs7Ozs7MENBQzVCLDhEQUFDRDs7a0RBQ0MsOERBQUNFO3dDQUFHRCxXQUFVO2tEQUFtQzs7Ozs7O2tEQUNqRCw4REFBQ0U7d0NBQUVGLFdBQVU7a0RBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7b0JBSWhDbEQsWUFBWXFELDZCQUNYLDhEQUFDSjt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUF3Qjs7Ozs7OzBDQUN2Qyw4REFBQ0Q7Z0NBQUlDLFdBQVcsQ0FBQyxtQkFBbUIsRUFDbENsRCxXQUFXcUQsV0FBVyxJQUFJLEtBQUssbUJBQy9CckQsV0FBV3FELFdBQVcsSUFBSSxLQUFLLG9CQUFvQixlQUNwRCxDQUFDOztvQ0FDQ3JELFdBQVdxRCxXQUFXO29DQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBTy9CM0MsdUJBQ0MsOERBQUN1QztnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0U7b0JBQUVGLFdBQVU7OEJBQWdCeEM7Ozs7Ozs7Ozs7OzBCQUtqQyw4REFBQ3VDO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0k7d0JBQ0NDLFNBQVMzQzt3QkFDVDRDLFVBQVVsRCxpQkFBaUIsQ0FBQ1A7d0JBQzVCbUQsV0FBVTs7MENBRVYsOERBQUN0RCx5SUFBWUE7Z0NBQUNzRCxXQUFVOzs7Ozs7MENBQ3hCLDhEQUFDTzswQ0FBTW5ELGdCQUFnQixtQkFBbUI7Ozs7Ozs7Ozs7OztrQ0FHNUMsOERBQUNnRDt3QkFDQ0MsU0FBUzdCO3dCQUNUOEIsVUFBVSxDQUFDeEQsY0FBY1E7d0JBQ3pCMEMsV0FBVTs7MENBRVYsOERBQUNyRCx5SUFBaUJBO2dDQUFDcUQsV0FBVTs7Ozs7OzBDQUM3Qiw4REFBQ087MENBQU1qRCxrQkFBa0Isa0JBQWtCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFLOUNOLHlCQUNDLDhEQUFDK0M7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDUTt3QkFBR1IsV0FBVTtrQ0FBbUM7Ozs7OztrQ0FDakQsOERBQUNFO3dCQUFFRixXQUFVOzs0QkFBaUJoRCxRQUFReUQsS0FBSzs0QkFBQzs0QkFBS3pELFFBQVEwRCxPQUFPOzs7Ozs7O29CQUMvRDVELFlBQVk2RCxtQ0FDWCw4REFBQ1Q7d0JBQUVGLFdBQVU7OzRCQUE2QjswQ0FDckIsOERBQUNPO2dDQUFLUCxXQUFVOzBDQUFjbEQsV0FBVzZELGlCQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBT3BGN0QsNEJBQ0MsOERBQUNpRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNZO3dCQUFHWixXQUFVO2tDQUF1Qzs7Ozs7O29CQUdwRGxELFdBQVd3QyxZQUFZLGtCQUN0Qiw4REFBQ1M7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDUTtnQ0FBR1IsV0FBVTswQ0FBMkM7Ozs7OzswQ0FDekQsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7OzBEQUFJLDhEQUFDYzswREFBTzs7Ozs7OzRDQUFjOzRDQUFFL0QsV0FBV3dDLFlBQVksQ0FBQ0MsSUFBSTs7Ozs7OztrREFDekQsOERBQUNROzswREFBSSw4REFBQ2M7MERBQU87Ozs7Ozs0Q0FBZTs0Q0FBRS9ELFdBQVd3QyxZQUFZLENBQUN3QixLQUFLOzs7Ozs7O2tEQUMzRCw4REFBQ2Y7OzBEQUFJLDhEQUFDYzswREFBTzs7Ozs7OzRDQUFlOzRDQUFFL0QsV0FBV3dDLFlBQVksQ0FBQ3lCLEtBQUs7Ozs7Ozs7a0RBQzNELDhEQUFDaEI7OzBEQUFJLDhEQUFDYzswREFBTzs7Ozs7OzRDQUFrQjs0Q0FBRS9ELFdBQVd3QyxZQUFZLENBQUMwQixRQUFROzs7Ozs7O29DQUNoRWxFLFdBQVd3QyxZQUFZLENBQUMyQixhQUFhLGtCQUNwQyw4REFBQ2xCOzswREFBSSw4REFBQ2M7MERBQU87Ozs7OzswREFDWCw4REFBQzlCO2dEQUFFRyxNQUFNcEMsV0FBV3dDLFlBQVksQ0FBQzJCLGFBQWE7Z0RBQUVDLFFBQU87Z0RBQVNDLEtBQUk7Z0RBQ2pFbkIsV0FBVTswREFDVmxELFdBQVd3QyxZQUFZLENBQUMyQixhQUFhOzs7Ozs7Ozs7Ozs7b0NBSTNDbkUsV0FBV3dDLFlBQVksQ0FBQzhCLFFBQVEsa0JBQy9CLDhEQUFDckI7OzBEQUFJLDhEQUFDYzswREFBTzs7Ozs7OzBEQUNYLDhEQUFDOUI7Z0RBQUVHLE1BQU1wQyxXQUFXd0MsWUFBWSxDQUFDOEIsUUFBUTtnREFBRUYsUUFBTztnREFBU0MsS0FBSTtnREFDNURuQixXQUFVOzBEQUF5Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O29CQVUvRGxELFdBQVd1RSxvQkFBb0Isa0JBQzlCLDhEQUFDdEI7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDUTtnQ0FBR1IsV0FBVTswQ0FBMkM7Ozs7OzswQ0FDekQsOERBQUNFO2dDQUFFRixXQUFVOzBDQUFpQ2xELFdBQVd1RSxvQkFBb0I7Ozs7Ozs7Ozs7OztvQkFLaEZ2RSxXQUFXd0Usa0JBQWtCLElBQUl4RSxXQUFXd0Usa0JBQWtCLENBQUNDLE1BQU0sR0FBRyxtQkFDdkUsOERBQUN4Qjt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNRO2dDQUFHUixXQUFVOzBDQUEyQzs7Ozs7OzBDQUN6RCw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ1psRCxXQUFXd0Usa0JBQWtCLENBQUNFLEdBQUcsQ0FBQyxDQUFDQyxPQUFPQyxzQkFDekMsOERBQUNuQjt3Q0FBaUJQLFdBQVU7a0RBQ3pCeUI7dUNBRFFDOzs7Ozs7Ozs7Ozs7Ozs7O29CQVNsQjVFLFdBQVc2RSxZQUFZLGtCQUN0Qiw4REFBQzVCO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ1E7Z0NBQUdSLFdBQVU7MENBQTZDOzs7Ozs7NEJBRTFEbEQsV0FBVzZFLFlBQVksQ0FBQ0Msa0JBQWtCLElBQUk5RSxXQUFXNkUsWUFBWSxDQUFDQyxrQkFBa0IsQ0FBQ0wsTUFBTSxHQUFHLG1CQUNqRyw4REFBQ3hCO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQzZCO3dDQUFHN0IsV0FBVTtrREFBbUM7Ozs7OztrREFDakQsOERBQUM4Qjt3Q0FBRzlCLFdBQVU7a0RBQ1hsRCxXQUFXNkUsWUFBWSxDQUFDQyxrQkFBa0IsQ0FBQ0osR0FBRyxDQUFDLENBQUNDLE9BQU9DLHNCQUN0RCw4REFBQ0s7MERBQWdCTjsrQ0FBUkM7Ozs7Ozs7Ozs7Ozs7Ozs7NEJBTWhCNUUsV0FBVzZFLFlBQVksQ0FBQ0ssUUFBUSxJQUFJbEYsV0FBVzZFLFlBQVksQ0FBQ0ssUUFBUSxDQUFDVCxNQUFNLEdBQUcsbUJBQzdFLDhEQUFDeEI7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDNkI7d0NBQUc3QixXQUFVO2tEQUFtQzs7Ozs7O2tEQUNqRCw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ1psRCxXQUFXNkUsWUFBWSxDQUFDSyxRQUFRLENBQUNSLEdBQUcsQ0FBQyxDQUFDUyxNQUFNUCxzQkFDM0MsOERBQUNuQjtnREFBaUJQLFdBQVU7MERBQ3pCaUM7K0NBRFFQOzs7Ozs7Ozs7Ozs7Ozs7OzRCQVFsQjVFLFdBQVc2RSxZQUFZLENBQUNPLFVBQVUsSUFBSXBGLFdBQVc2RSxZQUFZLENBQUNPLFVBQVUsQ0FBQ1gsTUFBTSxHQUFHLG1CQUNqRiw4REFBQ3hCOztrREFDQyw4REFBQzhCO3dDQUFHN0IsV0FBVTtrREFBbUM7Ozs7OztrREFDakQsOERBQUM4Qjt3Q0FBRzlCLFdBQVU7a0RBQ1hsRCxXQUFXNkUsWUFBWSxDQUFDTyxVQUFVLENBQUNWLEdBQUcsQ0FBQyxDQUFDQyxPQUFPQyxzQkFDOUMsOERBQUNLOzBEQUFnQk47K0NBQVJDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O29CQVNwQjVFLFdBQVdxRixVQUFVLElBQUlyRixXQUFXcUYsVUFBVSxDQUFDWixNQUFNLEdBQUcsbUJBQ3ZELDhEQUFDeEI7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDUTtnQ0FBR1IsV0FBVTswQ0FBMkM7Ozs7Ozs0QkFDeERsRCxXQUFXcUYsVUFBVSxDQUFDWCxHQUFHLENBQUMsQ0FBQ1ksS0FBS1Ysc0JBQy9CLDhEQUFDM0I7b0NBQWdCQyxXQUFVOztzREFDekIsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7O3NFQUNDLDhEQUFDOEI7NERBQUc3QixXQUFVO3NFQUF1Q29DLElBQUkzQixLQUFLOzs7Ozs7c0VBQzlELDhEQUFDUDs0REFBRUYsV0FBVTtzRUFBNkJvQyxJQUFJMUIsT0FBTzs7Ozs7O3dEQUNwRDBCLElBQUlDLElBQUksa0JBQUksOERBQUNuQzs0REFBRUYsV0FBVTtzRUFBeUJvQyxJQUFJQyxJQUFJOzs7Ozs7Ozs7Ozs7OERBRTdELDhEQUFDOUI7b0RBQUtQLFdBQVU7OERBQXFDb0MsSUFBSUUsUUFBUTs7Ozs7Ozs7Ozs7O3dDQUVsRUYsSUFBSUcsWUFBWSxJQUFJSCxJQUFJRyxZQUFZLENBQUNoQixNQUFNLEdBQUcsbUJBQzdDLDhEQUFDeEI7OzhEQUNDLDhEQUFDeUM7b0RBQUd4QyxXQUFVOzhEQUFpQzs7Ozs7OzhEQUMvQyw4REFBQzhCO29EQUFHOUIsV0FBVTs4REFDWG9DLElBQUlHLFlBQVksQ0FBQ2YsR0FBRyxDQUFDLENBQUNpQixhQUFxQkMsa0JBQzFDLDhEQUFDWDtzRUFBWVU7MkRBQUpDOzs7Ozs7Ozs7Ozs7Ozs7OzttQ0FkVGhCOzs7Ozs7Ozs7OztvQkF5QmY1RSxXQUFXNkYsUUFBUSxJQUFJN0YsV0FBVzZGLFFBQVEsQ0FBQ3BCLE1BQU0sR0FBRyxtQkFDbkQsOERBQUN4Qjt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNRO2dDQUFHUixXQUFVOzBDQUEyQzs7Ozs7OzRCQUN4RGxELFdBQVc2RixRQUFRLENBQUNuQixHQUFHLENBQUMsQ0FBQ29CLFNBQVNsQixzQkFDakMsOERBQUMzQjtvQ0FBZ0JDLFdBQVU7O3NEQUN6Qiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDNkI7b0RBQUc3QixXQUFVOzhEQUF1QzRDLFFBQVFyRCxJQUFJOzs7Ozs7Z0RBQ2hFcUQsUUFBUUMsSUFBSSxrQkFBSSw4REFBQzNDO29EQUFFRixXQUFVOzhEQUFxQzRDLFFBQVFDLElBQUk7Ozs7Ozs7Ozs7OztzREFFakYsOERBQUMzQzs0Q0FBRUYsV0FBVTtzREFBOEM0QyxRQUFRRSxXQUFXOzs7Ozs7d0NBRTdFRixRQUFRRyxZQUFZLElBQUlILFFBQVFHLFlBQVksQ0FBQ3hCLE1BQU0sR0FBRyxtQkFDckQsOERBQUN4Qjs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUN3QztvREFBR3hDLFdBQVU7OERBQXlDOzs7Ozs7OERBQ3ZELDhEQUFDRDtvREFBSUMsV0FBVTs4REFDWjRDLFFBQVFHLFlBQVksQ0FBQ3ZCLEdBQUcsQ0FBQyxDQUFDd0IsTUFBY04sa0JBQ3ZDLDhEQUFDbkM7NERBQWFQLFdBQVU7c0VBQ3JCZ0Q7MkRBRFFOOzs7Ozs7Ozs7Ozs7Ozs7O3dDQVFsQkUsUUFBUUwsWUFBWSxJQUFJSyxRQUFRTCxZQUFZLENBQUNoQixNQUFNLEdBQUcsbUJBQ3JELDhEQUFDeEI7OzhEQUNDLDhEQUFDeUM7b0RBQUd4QyxXQUFVOzhEQUF5Qzs7Ozs7OzhEQUN2RCw4REFBQzhCO29EQUFHOUIsV0FBVTs4REFDWDRDLFFBQVFMLFlBQVksQ0FBQ2YsR0FBRyxDQUFDLENBQUNpQixhQUFxQkMsa0JBQzlDLDhEQUFDWDtzRUFBWVU7MkRBQUpDOzs7Ozs7Ozs7Ozs7Ozs7OzttQ0F6QlRoQjs7Ozs7Ozs7Ozs7b0JBb0NmNUUsV0FBV21HLFNBQVMsa0JBQ25CLDhEQUFDbEQ7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDUTtnQ0FBR1IsV0FBVTswQ0FBMkM7Ozs7OzswQ0FDekQsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQzZCO3dDQUFHN0IsV0FBVTtrREFBdUNsRCxXQUFXbUcsU0FBUyxDQUFDQyxNQUFNOzs7Ozs7a0RBQ2hGLDhEQUFDaEQ7d0NBQUVGLFdBQVU7a0RBQTZCbEQsV0FBV21HLFNBQVMsQ0FBQ0UsV0FBVzs7Ozs7O2tEQUMxRSw4REFBQ3BEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ087MERBQU16RCxXQUFXbUcsU0FBUyxDQUFDWCxRQUFROzs7Ozs7NENBQ25DeEYsV0FBV21HLFNBQVMsQ0FBQ0csTUFBTSxrQkFBSSw4REFBQzdDO2dEQUFLUCxXQUFVOztvREFBYztvREFBRWxELFdBQVdtRyxTQUFTLENBQUNHLE1BQU07b0RBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7NEJBRy9GdEcsV0FBV21HLFNBQVMsQ0FBQ0ksbUJBQW1CLElBQUl2RyxXQUFXbUcsU0FBUyxDQUFDSSxtQkFBbUIsQ0FBQzlCLE1BQU0sR0FBRyxtQkFDN0YsOERBQUN4QjtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUN3Qzt3Q0FBR3hDLFdBQVU7a0RBQXlDOzs7Ozs7a0RBQ3ZELDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDWmxELFdBQVdtRyxTQUFTLENBQUNJLG1CQUFtQixDQUFDN0IsR0FBRyxDQUFDLENBQUM4QixRQUFRNUIsc0JBQ3JELDhEQUFDbkI7Z0RBQWlCUCxXQUFVOzBEQUN6QnNEOytDQURRNUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7b0JBV3RCNUUsV0FBV3lHLGNBQWMsSUFBSXpHLFdBQVd5RyxjQUFjLENBQUNoQyxNQUFNLEdBQUcsbUJBQy9ELDhEQUFDeEI7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDUTtnQ0FBR1IsV0FBVTswQ0FBMkM7Ozs7OzswQ0FDekQsOERBQUM4QjtnQ0FBRzlCLFdBQVU7MENBQ1hsRCxXQUFXeUcsY0FBYyxDQUFDL0IsR0FBRyxDQUFDLENBQUNnQyxNQUFNOUIsc0JBQ3BDLDhEQUFDSzt3Q0FBZS9CLFdBQVU7OzBEQUN4Qiw4REFBQ087Z0RBQUtQLFdBQVU7Ozs7Ozs0Q0FDZndEOzt1Q0FGTTlCOzs7Ozs7Ozs7Ozs7Ozs7O29CQVVoQjVFLFdBQVcyRyxrQkFBa0IsSUFBSTNHLFdBQVcyRyxrQkFBa0IsQ0FBQ2xDLE1BQU0sR0FBRyxtQkFDdkUsOERBQUN4Qjt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNRO2dDQUFHUixXQUFVOzBDQUEyQzs7Ozs7OzBDQUN6RCw4REFBQzhCO2dDQUFHOUIsV0FBVTswQ0FDWGxELFdBQVcyRyxrQkFBa0IsQ0FBQ2pDLEdBQUcsQ0FBQyxDQUFDa0MsTUFBTWhDLHNCQUN4Qyw4REFBQ0s7d0NBQWUvQixXQUFVOzswREFDeEIsOERBQUNPO2dEQUFLUCxXQUFVOzBEQUEwQjs7Ozs7OzBEQUMxQyw4REFBQ087MERBQU1tRDs7Ozs7Ozt1Q0FGQWhDOzs7Ozs7Ozs7Ozs7Ozs7O29CQVVoQjVFLFdBQVc2RyxtQkFBbUIsa0JBQzdCLDhEQUFDNUQ7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDUTtnQ0FBR1IsV0FBVTswQ0FBNkM7Ozs7OzswQ0FDM0QsOERBQUNFO2dDQUFFRixXQUFVOzBDQUEyQmxELFdBQVc2RyxtQkFBbUI7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQU03RSxDQUFDN0csY0FBYyxDQUFDTSwrQkFDZiw4REFBQzJDO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ3ZELHlJQUFnQkE7d0JBQUN1RCxXQUFVOzs7Ozs7a0NBQzVCLDhEQUFDUTt3QkFBR1IsV0FBVTtrQ0FBeUM7Ozs7OztrQ0FDdkQsOERBQUNFO3dCQUFFRixXQUFVO2tDQUNWbkQsUUFBUSxrRUFBa0U7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU12RiIsInNvdXJjZXMiOlsid2VicGFjazovL2pvYi1haS1mcm9udGVuZC8uL2FwcC9jb21wb25lbnRzL1Jlc3VtZUJ1aWxkZXIudHN4P2RkZTUiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBEb2N1bWVudFRleHRJY29uLCBTcGFya2xlc0ljb24sIEFycm93RG93blRyYXlJY29uIH0gZnJvbSAnQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lJ1xuXG5pbnRlcmZhY2UgUmVzdW1lRGF0YSB7XG4gIGNvbnRhY3RfaW5mbzoge1xuICAgIG5hbWU6IHN0cmluZ1xuICAgIGVtYWlsOiBzdHJpbmdcbiAgICBwaG9uZTogc3RyaW5nXG4gICAgbG9jYXRpb246IHN0cmluZ1xuICAgIHBvcnRmb2xpb191cmw/OiBzdHJpbmdcbiAgICBsaW5rZWRpbj86IHN0cmluZ1xuICAgIGdpdGh1Yj86IHN0cmluZ1xuICB9XG4gIHByb2Zlc3Npb25hbF9zdW1tYXJ5OiBzdHJpbmdcbiAgaGlnaGxpZ2h0ZWRfc2tpbGxzOiBzdHJpbmdbXVxuICBleHBlcmllbmNlOiBBcnJheTx7XG4gICAgdGl0bGU6IHN0cmluZ1xuICAgIGNvbXBhbnk6IHN0cmluZ1xuICAgIGR1cmF0aW9uOiBzdHJpbmdcbiAgICB0eXBlPzogc3RyaW5nXG4gICAgYWNoaWV2ZW1lbnRzOiBzdHJpbmdbXVxuICB9PlxuICBwcm9qZWN0czogQXJyYXk8e1xuICAgIG5hbWU6IHN0cmluZ1xuICAgIHJvbGU/OiBzdHJpbmdcbiAgICBkZXNjcmlwdGlvbjogc3RyaW5nXG4gICAgdGVjaG5vbG9naWVzOiBzdHJpbmdbXVxuICAgIGFjaGlldmVtZW50czogc3RyaW5nW11cbiAgfT5cbiAgZWR1Y2F0aW9uOiB7XG4gICAgZGVncmVlOiBzdHJpbmdcbiAgICBpbnN0aXR1dGlvbjogc3RyaW5nXG4gICAgZHVyYXRpb246IHN0cmluZ1xuICAgIHN0YXR1cz86IHN0cmluZ1xuICAgIHJlbGV2YW50X2NvdXJzZXdvcms/OiBzdHJpbmdbXVxuICB9XG4gIGNlcnRpZmljYXRpb25zOiBzdHJpbmdbXVxuICBhaV9leHBlcnRpc2U/OiB7XG4gICAgcHJvbXB0X2VuZ2luZWVyaW5nOiBzdHJpbmdbXVxuICAgIGFpX3Rvb2xzOiBzdHJpbmdbXVxuICAgIGF1dG9tYXRpb246IHN0cmluZ1tdXG4gIH1cbiAgdW5pcXVlX3ZhbHVlX3Byb3BzPzogc3RyaW5nW11cbiAgY3VzdG9taXplZF9mb3I/OiBzdHJpbmdcbiAgbWF0Y2hfc2NvcmU/OiBudW1iZXJcbiAgYWlfZW1waGFzaXNfbGV2ZWw/OiBzdHJpbmdcbiAgY3VzdG9taXphdGlvbl9ub3Rlcz86IHN0cmluZ1xufVxuXG5pbnRlcmZhY2UgSm9iSW5mbyB7XG4gIHRpdGxlOiBzdHJpbmdcbiAgY29tcGFueTogc3RyaW5nXG4gIG1hdGNoX3Njb3JlOiBudW1iZXJcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUmVzdW1lQnVpbGRlcih7IGpvYklkIH06IHsgam9iSWQ/OiBzdHJpbmcgfSkge1xuICBjb25zdCBbcmVzdW1lRGF0YSwgc2V0UmVzdW1lRGF0YV0gPSB1c2VTdGF0ZTxSZXN1bWVEYXRhIHwgbnVsbD4obnVsbClcbiAgY29uc3QgW2pvYkluZm8sIHNldEpvYkluZm9dID0gdXNlU3RhdGU8Sm9iSW5mbyB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFthbmFseXNpcywgc2V0QW5hbHlzaXNdID0gdXNlU3RhdGU8YW55PihudWxsKVxuICBjb25zdCBbaXNDdXN0b21pemluZywgc2V0SXNDdXN0b21pemluZ10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2lzR2VuZXJhdGluZ1BERiwgc2V0SXNHZW5lcmF0aW5nUERGXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpXG5cbiAgY29uc3QgY3VzdG9taXplRm9ySm9iID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICgham9iSWQpIHtcbiAgICAgIHNldEVycm9yKCdObyBqb2IgSUQgcHJvdmlkZWQgZm9yIGN1c3RvbWl6YXRpb24nKVxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgc2V0SXNDdXN0b21pemluZyh0cnVlKVxuICAgIHNldEVycm9yKG51bGwpXG4gICAgXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvcmVzdW1lL2N1c3RvbWl6ZScsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHsgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7IGpvYklkIH0pXG4gICAgICB9KVxuICAgICAgXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG4gICAgICBcbiAgICAgIGlmIChkYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgc2V0UmVzdW1lRGF0YShkYXRhLnJlc3VtZSlcbiAgICAgICAgc2V0QW5hbHlzaXMoZGF0YS5hbmFseXNpcylcbiAgICAgICAgc2V0Sm9iSW5mbyhkYXRhLmpvYl9pbmZvKVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0RXJyb3IoZGF0YS5lcnJvciB8fCAnRmFpbGVkIHRvIGN1c3RvbWl6ZSByZXN1bWUnKVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjdXN0b21pemluZyByZXN1bWU6JywgZXJyb3IpXG4gICAgICBzZXRFcnJvcignTmV0d29yayBlcnJvciB3aGlsZSBjdXN0b21pemluZyByZXN1bWUnKVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0N1c3RvbWl6aW5nKGZhbHNlKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGdlbmVyYXRlQW5kRG93bmxvYWRQREYgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFyZXN1bWVEYXRhKSB7XG4gICAgICBzZXRFcnJvcignTm8gcmVzdW1lIGRhdGEgYXZhaWxhYmxlIGZvciBQREYgZ2VuZXJhdGlvbicpXG4gICAgICByZXR1cm5cbiAgICB9XG5cbiAgICBzZXRJc0dlbmVyYXRpbmdQREYodHJ1ZSlcbiAgICBzZXRFcnJvcihudWxsKVxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvcmVzdW1lL2dlbmVyYXRlLXBkZicsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHsgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7IFxuICAgICAgICAgIHJlc3VtZURhdGEsIFxuICAgICAgICAgIGpvYklkOiBqb2JJZCB8fCAnZ2VuZXJhbCcgXG4gICAgICAgIH0pXG4gICAgICB9KVxuICAgICAgXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgYmxvYiA9IGF3YWl0IHJlc3BvbnNlLmJsb2IoKVxuICAgICAgICBjb25zdCB1cmwgPSB3aW5kb3cuVVJMLmNyZWF0ZU9iamVjdFVSTChibG9iKVxuICAgICAgICBjb25zdCBhID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYScpXG4gICAgICAgIGEuaHJlZiA9IHVybFxuICAgICAgICBcbiAgICAgICAgLy8gQ3JlYXRlIGEgbWVhbmluZ2Z1bCBmaWxlbmFtZVxuICAgICAgICBjb25zdCBqb2JUaXRsZSA9IHJlc3VtZURhdGEuY3VzdG9taXplZF9mb3IgfHwgJ2dlbmVyYWwnXG4gICAgICAgIGNvbnN0IGZpbGVOYW1lID0gYCR7cmVzdW1lRGF0YS5jb250YWN0X2luZm8/Lm5hbWUgfHwgJ1Jlc3VtZSd9XyR7am9iVGl0bGUucmVwbGFjZSgvW15hLXpBLVowLTldL2csICdfJyl9LnBkZmBcbiAgICAgICAgYS5kb3dubG9hZCA9IGZpbGVOYW1lXG4gICAgICAgIFxuICAgICAgICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKGEpXG4gICAgICAgIGEuY2xpY2soKVxuICAgICAgICBkb2N1bWVudC5ib2R5LnJlbW92ZUNoaWxkKGEpXG4gICAgICAgIHdpbmRvdy5VUkwucmV2b2tlT2JqZWN0VVJMKHVybClcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKVxuICAgICAgICBzZXRFcnJvcihlcnJvckRhdGEuZXJyb3IgfHwgJ0ZhaWxlZCB0byBnZW5lcmF0ZSBQREYnKVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZW5lcmF0aW5nIFBERjonLCBlcnJvcilcbiAgICAgIHNldEVycm9yKCdOZXR3b3JrIGVycm9yIHdoaWxlIGdlbmVyYXRpbmcgUERGJylcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNHZW5lcmF0aW5nUERGKGZhbHNlKVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy00eGwgbXgtYXV0byBwLTYgYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctbGdcIj5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi02XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgPERvY3VtZW50VGV4dEljb24gY2xhc3NOYW1lPVwiaC04IHctOCB0ZXh0LWJsdWUtNjAwXCIgLz5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+QUkgUmVzdW1lIEJ1aWxkZXI8L2gxPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPkNyZWF0ZSBBVFMtb3B0aW1pemVkIHJlc3VtZXMgZm9yIHNwZWNpZmljIGpvYnM8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICBcbiAgICAgICAge3Jlc3VtZURhdGE/Lm1hdGNoX3Njb3JlICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcmlnaHRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+Sm9iIE1hdGNoIFNjb3JlPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHRleHQtMnhsIGZvbnQtYm9sZCAke1xuICAgICAgICAgICAgICByZXN1bWVEYXRhLm1hdGNoX3Njb3JlID49IDgwID8gJ3RleHQtZ3JlZW4tNjAwJyA6IFxuICAgICAgICAgICAgICByZXN1bWVEYXRhLm1hdGNoX3Njb3JlID49IDYwID8gJ3RleHQteWVsbG93LTYwMCcgOiAndGV4dC1yZWQtNjAwJ1xuICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICB7cmVzdW1lRGF0YS5tYXRjaF9zY29yZX0lXG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogRXJyb3IgRGlzcGxheSAqL31cbiAgICAgIHtlcnJvciAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNCBwLTQgYmctcmVkLTUwIGJvcmRlciBib3JkZXItcmVkLTIwMCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1yZWQtODAwXCI+e2Vycm9yfTwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7LyogQ29udHJvbHMgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC00IG1iLTZcIj5cbiAgICAgICAgPGJ1dHRvblxuICAgICAgICAgIG9uQ2xpY2s9e2N1c3RvbWl6ZUZvckpvYn1cbiAgICAgICAgICBkaXNhYmxlZD17aXNDdXN0b21pemluZyB8fCAham9iSWR9XG4gICAgICAgICAgY2xhc3NOYW1lPVwiYnRuLXByaW1hcnkgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCJcbiAgICAgICAgPlxuICAgICAgICAgIDxTcGFya2xlc0ljb24gY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XG4gICAgICAgICAgPHNwYW4+e2lzQ3VzdG9taXppbmcgPyAnQ3VzdG9taXppbmcuLi4nIDogJ0FJIEN1c3RvbWl6ZSBSZXN1bWUnfTwvc3Bhbj5cbiAgICAgICAgPC9idXR0b24+XG4gICAgICAgIFxuICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgb25DbGljaz17Z2VuZXJhdGVBbmREb3dubG9hZFBERn1cbiAgICAgICAgICBkaXNhYmxlZD17IXJlc3VtZURhdGEgfHwgaXNHZW5lcmF0aW5nUERGfVxuICAgICAgICAgIGNsYXNzTmFtZT1cImJ0bi1zZWNvbmRhcnkgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCJcbiAgICAgICAgPlxuICAgICAgICAgIDxBcnJvd0Rvd25UcmF5SWNvbiBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz5cbiAgICAgICAgICA8c3Bhbj57aXNHZW5lcmF0aW5nUERGID8gJ0dlbmVyYXRpbmcuLi4nIDogJ0Rvd25sb2FkIFBERid9PC9zcGFuPlxuICAgICAgICA8L2J1dHRvbj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogSm9iIEluZm8gKi99XG4gICAgICB7am9iSW5mbyAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNiBwLTQgYmctYmx1ZS01MCBib3JkZXIgYm9yZGVyLWJsdWUtMjAwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWJsdWUtOTAwIG1iLTJcIj5DdXN0b21pemluZyBmb3I6PC9oMz5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtODAwXCI+e2pvYkluZm8udGl0bGV9IGF0IHtqb2JJbmZvLmNvbXBhbnl9PC9wPlxuICAgICAgICAgIHtyZXN1bWVEYXRhPy5haV9lbXBoYXNpc19sZXZlbCAmJiAoXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtYmx1ZS02MDAgbXQtMVwiPlxuICAgICAgICAgICAgICBBSSBFbXBoYXNpcyBMZXZlbDogPHNwYW4gY2xhc3NOYW1lPVwiY2FwaXRhbGl6ZVwiPntyZXN1bWVEYXRhLmFpX2VtcGhhc2lzX2xldmVsfTwvc3Bhbj5cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBSZXN1bWUgUHJldmlldyAqL31cbiAgICAgIHtyZXN1bWVEYXRhICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZXN1bWUtcHJldmlldyBiZy1ncmF5LTUwIHAtNiByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIG1iLTQgdGV4dC1ncmF5LTkwMFwiPlJlc3VtZSBQcmV2aWV3PC9oMj5cblxuICAgICAgICAgIHsvKiBDb250YWN0IEluZm9ybWF0aW9uICovfVxuICAgICAgICAgIHtyZXN1bWVEYXRhLmNvbnRhY3RfaW5mbyAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTYgcC00IGJnLXdoaXRlIHJvdW5kZWQtbGcgYm9yZGVyXCI+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTgwMCBtYi0zXCI+Q29udGFjdCBJbmZvcm1hdGlvbjwvaDM+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtMyB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgPGRpdj48c3Ryb25nPk5hbWU6PC9zdHJvbmc+IHtyZXN1bWVEYXRhLmNvbnRhY3RfaW5mby5uYW1lfTwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXY+PHN0cm9uZz5FbWFpbDo8L3N0cm9uZz4ge3Jlc3VtZURhdGEuY29udGFjdF9pbmZvLmVtYWlsfTwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXY+PHN0cm9uZz5QaG9uZTo8L3N0cm9uZz4ge3Jlc3VtZURhdGEuY29udGFjdF9pbmZvLnBob25lfTwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXY+PHN0cm9uZz5Mb2NhdGlvbjo8L3N0cm9uZz4ge3Jlc3VtZURhdGEuY29udGFjdF9pbmZvLmxvY2F0aW9ufTwvZGl2PlxuICAgICAgICAgICAgICAgIHtyZXN1bWVEYXRhLmNvbnRhY3RfaW5mby5wb3J0Zm9saW9fdXJsICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXY+PHN0cm9uZz5Qb3J0Zm9saW86PC9zdHJvbmc+XG4gICAgICAgICAgICAgICAgICAgIDxhIGhyZWY9e3Jlc3VtZURhdGEuY29udGFjdF9pbmZvLnBvcnRmb2xpb191cmx9IHRhcmdldD1cIl9ibGFua1wiIHJlbD1cIm5vb3BlbmVyIG5vcmVmZXJyZXJcIlxuICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNjAwIGhvdmVyOnRleHQtYmx1ZS04MDAgbWwtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtyZXN1bWVEYXRhLmNvbnRhY3RfaW5mby5wb3J0Zm9saW9fdXJsfVxuICAgICAgICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIHtyZXN1bWVEYXRhLmNvbnRhY3RfaW5mby5saW5rZWRpbiAmJiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2PjxzdHJvbmc+TGlua2VkSW46PC9zdHJvbmc+XG4gICAgICAgICAgICAgICAgICAgIDxhIGhyZWY9e3Jlc3VtZURhdGEuY29udGFjdF9pbmZvLmxpbmtlZGlufSB0YXJnZXQ9XCJfYmxhbmtcIiByZWw9XCJub29wZW5lciBub3JlZmVycmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTYwMCBob3Zlcjp0ZXh0LWJsdWUtODAwIG1sLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICBMaW5rZWRJbiBQcm9maWxlXG4gICAgICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cblxuICAgICAgICAgIHsvKiBQcm9mZXNzaW9uYWwgU3VtbWFyeSAqL31cbiAgICAgICAgICB7cmVzdW1lRGF0YS5wcm9mZXNzaW9uYWxfc3VtbWFyeSAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTYgcC00IGJnLXdoaXRlIHJvdW5kZWQtbGcgYm9yZGVyXCI+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTgwMCBtYi0yXCI+UHJvZmVzc2lvbmFsIFN1bW1hcnk8L2gzPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNzAwIGxlYWRpbmctcmVsYXhlZFwiPntyZXN1bWVEYXRhLnByb2Zlc3Npb25hbF9zdW1tYXJ5fTwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7LyogQ29yZSBTa2lsbHMgKi99XG4gICAgICAgICAge3Jlc3VtZURhdGEuaGlnaGxpZ2h0ZWRfc2tpbGxzICYmIHJlc3VtZURhdGEuaGlnaGxpZ2h0ZWRfc2tpbGxzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02IHAtNCBiZy13aGl0ZSByb3VuZGVkLWxnIGJvcmRlclwiPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS04MDAgbWItM1wiPkNvcmUgU2tpbGxzPC9oMz5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtd3JhcCBnYXAtMlwiPlxuICAgICAgICAgICAgICAgIHtyZXN1bWVEYXRhLmhpZ2hsaWdodGVkX3NraWxscy5tYXAoKHNraWxsLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgPHNwYW4ga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwicHgtMyBweS0xIGJnLWJsdWUtMTAwIHRleHQtYmx1ZS04MDAgdGV4dC1zbSByb3VuZGVkLWZ1bGxcIj5cbiAgICAgICAgICAgICAgICAgICAge3NraWxsfVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7LyogQUkgRXhwZXJ0aXNlIFNlY3Rpb24gKi99XG4gICAgICAgICAge3Jlc3VtZURhdGEuYWlfZXhwZXJ0aXNlICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNiBwLTQgYmctcHVycGxlLTUwIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1wdXJwbGUtMjAwXCI+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1wdXJwbGUtODAwIG1iLTNcIj7wn6SWIEFJIEV4cGVydGlzZSAmIEF1dG9tYXRpb248L2gzPlxuXG4gICAgICAgICAgICAgIHtyZXN1bWVEYXRhLmFpX2V4cGVydGlzZS5wcm9tcHRfZW5naW5lZXJpbmcgJiYgcmVzdW1lRGF0YS5haV9leHBlcnRpc2UucHJvbXB0X2VuZ2luZWVyaW5nLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItM1wiPlxuICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtcHVycGxlLTcwMCBtYi0xXCI+UHJvbXB0IEVuZ2luZWVyaW5nPC9oND5cbiAgICAgICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJsaXN0LWRpc2MgbGlzdC1pbnNpZGUgdGV4dC1zbSB0ZXh0LXB1cnBsZS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAge3Jlc3VtZURhdGEuYWlfZXhwZXJ0aXNlLnByb21wdF9lbmdpbmVlcmluZy5tYXAoKHNraWxsLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgIDxsaSBrZXk9e2luZGV4fT57c2tpbGx9PC9saT5cbiAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICA8L3VsPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgIHtyZXN1bWVEYXRhLmFpX2V4cGVydGlzZS5haV90b29scyAmJiByZXN1bWVEYXRhLmFpX2V4cGVydGlzZS5haV90b29scy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTNcIj5cbiAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LXB1cnBsZS03MDAgbWItMVwiPkFJIFRvb2xzIE1hc3Rlcnk8L2g0PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtd3JhcCBnYXAtMVwiPlxuICAgICAgICAgICAgICAgICAgICB7cmVzdW1lRGF0YS5haV9leHBlcnRpc2UuYWlfdG9vbHMubWFwKCh0b29sLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGtleT17aW5kZXh9IGNsYXNzTmFtZT1cInB4LTIgcHktMSBiZy1wdXJwbGUtMTAwIHRleHQtcHVycGxlLTcwMCB0ZXh0LXhzIHJvdW5kZWRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHt0b29sfVxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICB7cmVzdW1lRGF0YS5haV9leHBlcnRpc2UuYXV0b21hdGlvbiAmJiByZXN1bWVEYXRhLmFpX2V4cGVydGlzZS5hdXRvbWF0aW9uLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1wdXJwbGUtNzAwIG1iLTFcIj5Xb3JrZmxvdyBBdXRvbWF0aW9uPC9oND5cbiAgICAgICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJsaXN0LWRpc2MgbGlzdC1pbnNpZGUgdGV4dC1zbSB0ZXh0LXB1cnBsZS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAge3Jlc3VtZURhdGEuYWlfZXhwZXJ0aXNlLmF1dG9tYXRpb24ubWFwKChza2lsbCwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8bGkga2V5PXtpbmRleH0+e3NraWxsfTwvbGk+XG4gICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7LyogRXhwZXJpZW5jZSAqL31cbiAgICAgICAgICB7cmVzdW1lRGF0YS5leHBlcmllbmNlICYmIHJlc3VtZURhdGEuZXhwZXJpZW5jZS5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNiBwLTQgYmctd2hpdGUgcm91bmRlZC1sZyBib3JkZXJcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktODAwIG1iLTNcIj5Qcm9mZXNzaW9uYWwgRXhwZXJpZW5jZTwvaDM+XG4gICAgICAgICAgICAgIHtyZXN1bWVEYXRhLmV4cGVyaWVuY2UubWFwKChleHAsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJtYi02IGxhc3Q6bWItMCBwYi00IGxhc3Q6cGItMCBib3JkZXItYiBsYXN0OmJvcmRlci1iLTAgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLXN0YXJ0IG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIHRleHQtbGdcIj57ZXhwLnRpdGxlfTwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTcwMCBmb250LW1lZGl1bVwiPntleHAuY29tcGFueX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAge2V4cC50eXBlICYmIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPntleHAudHlwZX08L3A+fVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIGZvbnQtbWVkaXVtXCI+e2V4cC5kdXJhdGlvbn08L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIHtleHAuYWNoaWV2ZW1lbnRzICYmIGV4cC5hY2hpZXZlbWVudHMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGg1IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS04MDAgbWItMlwiPktleSBBY2hpZXZlbWVudHM6PC9oNT5cbiAgICAgICAgICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwibGlzdC1kaXNjIGxpc3QtaW5zaWRlIHRleHQtc20gdGV4dC1ncmF5LTcwMCBzcGFjZS15LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtleHAuYWNoaWV2ZW1lbnRzLm1hcCgoYWNoaWV2ZW1lbnQ6IHN0cmluZywgaTogbnVtYmVyKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxsaSBrZXk9e2l9PnthY2hpZXZlbWVudH08L2xpPlxuICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7LyogUHJvamVjdHMgKi99XG4gICAgICAgICAge3Jlc3VtZURhdGEucHJvamVjdHMgJiYgcmVzdW1lRGF0YS5wcm9qZWN0cy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNiBwLTQgYmctd2hpdGUgcm91bmRlZC1sZyBib3JkZXJcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktODAwIG1iLTNcIj5LZXkgUHJvamVjdHM8L2gzPlxuICAgICAgICAgICAgICB7cmVzdW1lRGF0YS5wcm9qZWN0cy5tYXAoKHByb2plY3QsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJtYi02IGxhc3Q6bWItMCBwYi00IGxhc3Q6cGItMCBib3JkZXItYiBsYXN0OmJvcmRlci1iLTAgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCB0ZXh0LWxnXCI+e3Byb2plY3QubmFtZX08L2g0PlxuICAgICAgICAgICAgICAgICAgICB7cHJvamVjdC5yb2xlICYmIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMCBmb250LW1lZGl1bVwiPntwcm9qZWN0LnJvbGV9PC9wPn1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTcwMCB0ZXh0LXNtIG1iLTMgbGVhZGluZy1yZWxheGVkXCI+e3Byb2plY3QuZGVzY3JpcHRpb259PC9wPlxuXG4gICAgICAgICAgICAgICAgICB7cHJvamVjdC50ZWNobm9sb2dpZXMgJiYgcHJvamVjdC50ZWNobm9sb2dpZXMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItM1wiPlxuICAgICAgICAgICAgICAgICAgICAgIDxoNSBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS02MDAgbWItMVwiPlRlY2hub2xvZ2llczo8L2g1PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXdyYXAgZ2FwLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtwcm9qZWN0LnRlY2hub2xvZ2llcy5tYXAoKHRlY2g6IHN0cmluZywgaTogbnVtYmVyKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGtleT17aX0gY2xhc3NOYW1lPVwicHgtMiBweS0xIGJnLWJsdWUtMTAwIHRleHQtYmx1ZS03MDAgdGV4dC14cyByb3VuZGVkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge3RlY2h9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICAgIHtwcm9qZWN0LmFjaGlldmVtZW50cyAmJiBwcm9qZWN0LmFjaGlldmVtZW50cy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8aDUgY2xhc3NOYW1lPVwidGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNjAwIG1iLTFcIj5JbXBhY3QgJiBSZXN1bHRzOjwvaDU+XG4gICAgICAgICAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cImxpc3QtZGlzYyBsaXN0LWluc2lkZSB0ZXh0LXNtIHRleHQtZ3JheS03MDAgc3BhY2UteS0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7cHJvamVjdC5hY2hpZXZlbWVudHMubWFwKChhY2hpZXZlbWVudDogc3RyaW5nLCBpOiBudW1iZXIpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGxpIGtleT17aX0+e2FjaGlldmVtZW50fTwvbGk+XG4gICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L3VsPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cblxuICAgICAgICAgIHsvKiBFZHVjYXRpb24gKi99XG4gICAgICAgICAge3Jlc3VtZURhdGEuZWR1Y2F0aW9uICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNiBwLTQgYmctd2hpdGUgcm91bmRlZC1sZyBib3JkZXJcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktODAwIG1iLTNcIj5FZHVjYXRpb248L2gzPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTJcIj5cbiAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIHRleHQtbGdcIj57cmVzdW1lRGF0YS5lZHVjYXRpb24uZGVncmVlfTwvaDQ+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTcwMCBmb250LW1lZGl1bVwiPntyZXN1bWVEYXRhLmVkdWNhdGlvbi5pbnN0aXR1dGlvbn08L3A+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtNCB0ZXh0LXNtIHRleHQtZ3JheS02MDAgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgPHNwYW4+e3Jlc3VtZURhdGEuZWR1Y2F0aW9uLmR1cmF0aW9ufTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIHtyZXN1bWVEYXRhLmVkdWNhdGlvbi5zdGF0dXMgJiYgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj4oe3Jlc3VtZURhdGEuZWR1Y2F0aW9uLnN0YXR1c30pPC9zcGFuPn1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIHtyZXN1bWVEYXRhLmVkdWNhdGlvbi5yZWxldmFudF9jb3Vyc2V3b3JrICYmIHJlc3VtZURhdGEuZWR1Y2F0aW9uLnJlbGV2YW50X2NvdXJzZXdvcmsubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0zXCI+XG4gICAgICAgICAgICAgICAgICA8aDUgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTFcIj5SZWxldmFudCBDb3Vyc2V3b3JrOjwvaDU+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC0xXCI+XG4gICAgICAgICAgICAgICAgICAgIHtyZXN1bWVEYXRhLmVkdWNhdGlvbi5yZWxldmFudF9jb3Vyc2V3b3JrLm1hcCgoY291cnNlLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGtleT17aW5kZXh9IGNsYXNzTmFtZT1cInB4LTIgcHktMSBiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi03MDAgdGV4dC14cyByb3VuZGVkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7Y291cnNlfVxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7LyogQ2VydGlmaWNhdGlvbnMgKi99XG4gICAgICAgICAge3Jlc3VtZURhdGEuY2VydGlmaWNhdGlvbnMgJiYgcmVzdW1lRGF0YS5jZXJ0aWZpY2F0aW9ucy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNiBwLTQgYmctd2hpdGUgcm91bmRlZC1sZyBib3JkZXJcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktODAwIG1iLTNcIj5DZXJ0aWZpY2F0aW9uczwvaDM+XG4gICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICB7cmVzdW1lRGF0YS5jZXJ0aWZpY2F0aW9ucy5tYXAoKGNlcnQsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICA8bGkga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgdGV4dC1zbSB0ZXh0LWdyYXktNzAwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInctMiBoLTIgYmctYmx1ZS01MDAgcm91bmRlZC1mdWxsIG1yLTNcIj48L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIHtjZXJ0fVxuICAgICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7LyogVW5pcXVlIFZhbHVlIFByb3Bvc2l0aW9ucyAqL31cbiAgICAgICAgICB7cmVzdW1lRGF0YS51bmlxdWVfdmFsdWVfcHJvcHMgJiYgcmVzdW1lRGF0YS51bmlxdWVfdmFsdWVfcHJvcHMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTYgcC00IGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTUwIHRvLXB1cnBsZS01MCByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItYmx1ZS0yMDBcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktODAwIG1iLTNcIj7wn4yfIFVuaXF1ZSBWYWx1ZSBQcm9wb3NpdGlvbnM8L2gzPlxuICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAge3Jlc3VtZURhdGEudW5pcXVlX3ZhbHVlX3Byb3BzLm1hcCgocHJvcCwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgIDxsaSBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IHRleHQtc20gdGV4dC1ncmF5LTcwMFwiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNTAwIG1yLTIgbXQtMVwiPuKcqDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4+e3Byb3B9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7LyogQ3VzdG9taXphdGlvbiBOb3RlcyAqL31cbiAgICAgICAgICB7cmVzdW1lRGF0YS5jdXN0b21pemF0aW9uX25vdGVzICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNiBwLTQgYmcteWVsbG93LTUwIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci15ZWxsb3ctMjAwXCI+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgdGV4dC15ZWxsb3ctODAwIG1iLTJcIj7wn5OdIEN1c3RvbWl6YXRpb24gTm90ZXM8L2gzPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQteWVsbG93LTcwMFwiPntyZXN1bWVEYXRhLmN1c3RvbWl6YXRpb25fbm90ZXN9PC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgICAgXG4gICAgICB7IXJlc3VtZURhdGEgJiYgIWlzQ3VzdG9taXppbmcgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTEyXCI+XG4gICAgICAgICAgPERvY3VtZW50VGV4dEljb24gY2xhc3NOYW1lPVwiaC0xNiB3LTE2IHRleHQtZ3JheS00MDAgbXgtYXV0byBtYi00XCIgLz5cbiAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTJcIj5ObyBSZXN1bWUgRGF0YTwvaDM+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAge2pvYklkID8gJ0NsaWNrIFwiQUkgQ3VzdG9taXplIFJlc3VtZVwiIHRvIGdlbmVyYXRlIGEgam9iLXNwZWNpZmljIHJlc3VtZScgOiAnU2VsZWN0IGEgam9iIHRvIGN1c3RvbWl6ZSB5b3VyIHJlc3VtZSd9XG4gICAgICAgICAgPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsIkRvY3VtZW50VGV4dEljb24iLCJTcGFya2xlc0ljb24iLCJBcnJvd0Rvd25UcmF5SWNvbiIsIlJlc3VtZUJ1aWxkZXIiLCJqb2JJZCIsInJlc3VtZURhdGEiLCJzZXRSZXN1bWVEYXRhIiwiam9iSW5mbyIsInNldEpvYkluZm8iLCJhbmFseXNpcyIsInNldEFuYWx5c2lzIiwiaXNDdXN0b21pemluZyIsInNldElzQ3VzdG9taXppbmciLCJpc0dlbmVyYXRpbmdQREYiLCJzZXRJc0dlbmVyYXRpbmdQREYiLCJlcnJvciIsInNldEVycm9yIiwiY3VzdG9taXplRm9ySm9iIiwicmVzcG9uc2UiLCJmZXRjaCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsImRhdGEiLCJqc29uIiwic3VjY2VzcyIsInJlc3VtZSIsImpvYl9pbmZvIiwiY29uc29sZSIsImdlbmVyYXRlQW5kRG93bmxvYWRQREYiLCJvayIsImJsb2IiLCJ1cmwiLCJ3aW5kb3ciLCJVUkwiLCJjcmVhdGVPYmplY3RVUkwiLCJhIiwiZG9jdW1lbnQiLCJjcmVhdGVFbGVtZW50IiwiaHJlZiIsImpvYlRpdGxlIiwiY3VzdG9taXplZF9mb3IiLCJmaWxlTmFtZSIsImNvbnRhY3RfaW5mbyIsIm5hbWUiLCJyZXBsYWNlIiwiZG93bmxvYWQiLCJhcHBlbmRDaGlsZCIsImNsaWNrIiwicmVtb3ZlQ2hpbGQiLCJyZXZva2VPYmplY3RVUkwiLCJlcnJvckRhdGEiLCJkaXYiLCJjbGFzc05hbWUiLCJoMSIsInAiLCJtYXRjaF9zY29yZSIsImJ1dHRvbiIsIm9uQ2xpY2siLCJkaXNhYmxlZCIsInNwYW4iLCJoMyIsInRpdGxlIiwiY29tcGFueSIsImFpX2VtcGhhc2lzX2xldmVsIiwiaDIiLCJzdHJvbmciLCJlbWFpbCIsInBob25lIiwibG9jYXRpb24iLCJwb3J0Zm9saW9fdXJsIiwidGFyZ2V0IiwicmVsIiwibGlua2VkaW4iLCJwcm9mZXNzaW9uYWxfc3VtbWFyeSIsImhpZ2hsaWdodGVkX3NraWxscyIsImxlbmd0aCIsIm1hcCIsInNraWxsIiwiaW5kZXgiLCJhaV9leHBlcnRpc2UiLCJwcm9tcHRfZW5naW5lZXJpbmciLCJoNCIsInVsIiwibGkiLCJhaV90b29scyIsInRvb2wiLCJhdXRvbWF0aW9uIiwiZXhwZXJpZW5jZSIsImV4cCIsInR5cGUiLCJkdXJhdGlvbiIsImFjaGlldmVtZW50cyIsImg1IiwiYWNoaWV2ZW1lbnQiLCJpIiwicHJvamVjdHMiLCJwcm9qZWN0Iiwicm9sZSIsImRlc2NyaXB0aW9uIiwidGVjaG5vbG9naWVzIiwidGVjaCIsImVkdWNhdGlvbiIsImRlZ3JlZSIsImluc3RpdHV0aW9uIiwic3RhdHVzIiwicmVsZXZhbnRfY291cnNld29yayIsImNvdXJzZSIsImNlcnRpZmljYXRpb25zIiwiY2VydCIsInVuaXF1ZV92YWx1ZV9wcm9wcyIsInByb3AiLCJjdXN0b21pemF0aW9uX25vdGVzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ResumeBuilder.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/SearchForm.tsx":
/*!***************************************!*\
  !*** ./app/components/SearchForm.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SearchForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n\n\nfunction SearchForm({ onSearch, searching }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                            children: \"\\uD83C\\uDDEE\\uD83C\\uDDF3 Search Indian Job Portals\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\SearchForm.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 text-sm\",\n                            children: \"Search Internshala, Indeed India, and Naukri.com for real UI/UX design jobs with authentic URLs\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\SearchForm.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\SearchForm.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onSearch,\n                    disabled: searching,\n                    className: \"btn-primary flex items-center disabled:opacity-50 disabled:cursor-not-allowed\",\n                    children: searching ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\SearchForm.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 15\n                            }, this),\n                            \"Searching Indian Portals...\"\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\SearchForm.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 15\n                            }, this),\n                            \"Search Real Jobs\"\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\SearchForm.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\SearchForm.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\SearchForm.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/SearchForm.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/StatsCard.tsx":
/*!**************************************!*\
  !*** ./app/components/StatsCard.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StatsCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction StatsCard({ title, value, color }) {\n    const colorClasses = {\n        blue: \"bg-blue-50 text-blue-700 border-blue-200\",\n        green: \"bg-green-50 text-green-700 border-green-200\",\n        purple: \"bg-purple-50 text-purple-700 border-purple-200\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `card border-l-4 ${colorClasses[color]}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm font-medium opacity-75\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\StatsCard.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-2xl font-bold\",\n                        children: value\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\StatsCard.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\StatsCard.tsx\",\n                lineNumber: 17,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\StatsCard.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\components\\\\StatsCard.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9TdGF0c0NhcmQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFNZSxTQUFTQSxVQUFVLEVBQUVDLEtBQUssRUFBRUMsS0FBSyxFQUFFQyxLQUFLLEVBQWtCO0lBQ3ZFLE1BQU1DLGVBQWU7UUFDbkJDLE1BQU07UUFDTkMsT0FBTztRQUNQQyxRQUFRO0lBQ1Y7SUFFQSxxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVyxDQUFDLGdCQUFnQixFQUFFTCxZQUFZLENBQUNELE1BQU0sQ0FBQyxDQUFDO2tCQUN0RCw0RUFBQ0s7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7O2tDQUNDLDhEQUFDRTt3QkFBRUQsV0FBVTtrQ0FBa0NSOzs7Ozs7a0NBQy9DLDhEQUFDUzt3QkFBRUQsV0FBVTtrQ0FBc0JQOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSzdDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vam9iLWFpLWZyb250ZW5kLy4vYXBwL2NvbXBvbmVudHMvU3RhdHNDYXJkLnRzeD8yZTMyIl0sInNvdXJjZXNDb250ZW50IjpbImludGVyZmFjZSBTdGF0c0NhcmRQcm9wcyB7XG4gIHRpdGxlOiBzdHJpbmdcbiAgdmFsdWU6IG51bWJlclxuICBjb2xvcjogJ2JsdWUnIHwgJ2dyZWVuJyB8ICdwdXJwbGUnXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFN0YXRzQ2FyZCh7IHRpdGxlLCB2YWx1ZSwgY29sb3IgfTogU3RhdHNDYXJkUHJvcHMpIHtcbiAgY29uc3QgY29sb3JDbGFzc2VzID0ge1xuICAgIGJsdWU6ICdiZy1ibHVlLTUwIHRleHQtYmx1ZS03MDAgYm9yZGVyLWJsdWUtMjAwJyxcbiAgICBncmVlbjogJ2JnLWdyZWVuLTUwIHRleHQtZ3JlZW4tNzAwIGJvcmRlci1ncmVlbi0yMDAnLFxuICAgIHB1cnBsZTogJ2JnLXB1cnBsZS01MCB0ZXh0LXB1cnBsZS03MDAgYm9yZGVyLXB1cnBsZS0yMDAnLFxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17YGNhcmQgYm9yZGVyLWwtNCAke2NvbG9yQ2xhc3Nlc1tjb2xvcl19YH0+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSBvcGFjaXR5LTc1XCI+e3RpdGxlfTwvcD5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGRcIj57dmFsdWV9PC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59Il0sIm5hbWVzIjpbIlN0YXRzQ2FyZCIsInRpdGxlIiwidmFsdWUiLCJjb2xvciIsImNvbG9yQ2xhc3NlcyIsImJsdWUiLCJncmVlbiIsInB1cnBsZSIsImRpdiIsImNsYXNzTmFtZSIsInAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/components/StatsCard.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"759aab823e77\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9qb2ItYWktZnJvbnRlbmQvLi9hcHAvZ2xvYmFscy5jc3M/NjliMCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjc1OWFhYjgyM2U3N1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/components/JobDashboard.tsx":
/*!*****************************************!*\
  !*** ./app/components/JobDashboard.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\job-ai\frontend\app\components\JobDashboard.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Job AI - Automated Job Search\",\n    description: \"AI-powered job search and application automation\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"bg-white shadow-sm border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between h-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-bold text-gray-900\",\n                                            children: \"Job AI\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n                                            lineNumber: 24,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n                                        lineNumber: 23,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"Automated Job Search\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n                                            lineNumber: 27,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 19,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_JobDashboard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./components/JobDashboard */ \"(rsc)/./app/components/JobDashboard.tsx\");\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_JobDashboard__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\job-ai\\\\frontend\\\\app\\\\page.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBb0Q7QUFFckMsU0FBU0M7SUFDdEIscUJBQU8sOERBQUNELGdFQUFZQTs7Ozs7QUFDdEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9qb2ItYWktZnJvbnRlbmQvLi9hcHAvcGFnZS50c3g/NzYwMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgSm9iRGFzaGJvYXJkIGZyb20gJy4vY29tcG9uZW50cy9Kb2JEYXNoYm9hcmQnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhvbWUoKSB7XG4gIHJldHVybiA8Sm9iRGFzaGJvYXJkIC8+XG59XG5cblxuXG5cblxuXG4iXSwibmFtZXMiOlsiSm9iRGFzaGJvYXJkIiwiSG9tZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@heroicons"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cwwwst%5COneDrive%5CDesktop%5Cjob-ai%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();