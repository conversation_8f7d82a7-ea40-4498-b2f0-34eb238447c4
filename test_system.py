#!/usr/bin/env python3
"""
Comprehensive test script for the Job AI automated application system.
Tests all major components and workflows.
"""

import sys
import json
import asyncio
import requests
from pathlib import Path

# Add src to path for imports
sys.path.append('src')

from src.utils.database import JobData<PERSON>
from src.utils.gemini_client import Gemini<PERSON><PERSON>
from src.ai.job_analyzer import JobAnalyzer
from src.ai.resume_generator import AIResumeGenerator
from src.automation.job_applier import AutoJobApplier
from src.utils.config import Config
from src.utils.logger import setup_logger

logger = setup_logger('system_test')

class SystemTester:
    def __init__(self):
        self.db = JobDatabase()
        self.gemini = GeminiClient()
        self.analyzer = JobAnalyzer()
        self.resume_gen = AIResumeGenerator()
        self.applier = AutoJobApplier()
        self.test_results = {}
        
    def run_all_tests(self):
        """Run comprehensive system tests"""
        logger.info("Starting comprehensive system tests...")
        
        tests = [
            ('Database Operations', self.test_database),
            ('Gemini Client', self.test_gemini_client),
            ('Job Analysis', self.test_job_analyzer),
            ('Resume Generation', self.test_resume_generator),
            ('PDF Generation', self.test_pdf_generation),
            ('API Endpoints', self.test_api_endpoints),
            ('End-to-End Workflow', self.test_e2e_workflow)
        ]
        
        for test_name, test_func in tests:
            logger.info(f"\n{'='*50}")
            logger.info(f"Running test: {test_name}")
            logger.info(f"{'='*50}")
            
            try:
                result = test_func()
                self.test_results[test_name] = {
                    'status': 'PASSED' if result else 'FAILED',
                    'details': result if isinstance(result, dict) else {}
                }
                logger.info(f"✅ {test_name}: PASSED")
            except Exception as e:
                self.test_results[test_name] = {
                    'status': 'ERROR',
                    'error': str(e)
                }
                logger.error(f"❌ {test_name}: ERROR - {e}")
        
        self.print_test_summary()
        return self.test_results
    
    def test_database(self):
        """Test database operations"""
        logger.info("Testing database operations...")
        
        # Test job insertion
        test_job = {
            'job_id': 'test_job_001',
            'title': 'Senior UI/UX Designer',
            'company': 'Test Company',
            'description': 'Looking for an experienced UI/UX designer with AI knowledge',
            'requirements': 'Figma, Adobe Creative Suite, AI tools experience',
            'apply_url': 'https://example.com/apply',
            'posted_date': '2024-01-15',
            'source': 'test',
            'location': 'Remote',
            'scraped_at': '2024-01-15T10:00:00',
            'status': 'new'
        }
        
        self.db.insert_job(test_job)
        logger.info("✓ Job insertion successful")
        
        # Test job retrieval
        retrieved_job = self.db.get_job_by_id('test_job_001')
        assert retrieved_job is not None, "Failed to retrieve inserted job"
        assert retrieved_job['title'] == test_job['title'], "Job data mismatch"
        logger.info("✓ Job retrieval successful")
        
        # Test status update
        self.db.update_job_status('test_job_001', 'analyzed')
        updated_job = self.db.get_job_by_id('test_job_001')
        assert updated_job['status'] == 'analyzed', "Status update failed"
        logger.info("✓ Status update successful")
        
        return True
    
    def test_gemini_client(self):
        """Test Gemini AI client"""
        logger.info("Testing Gemini client...")
        
        if not Config.GEMINI_API_KEY:
            logger.warning("⚠️ No Gemini API key found - skipping Gemini tests")
            return {'status': 'skipped', 'reason': 'No API key'}
        
        # Test basic content generation
        try:
            response = self.gemini.model.generate_content("Say 'Hello, World!' in JSON format")
            assert response.text, "No response from Gemini"
            logger.info("✓ Basic Gemini communication successful")
            
            # Test job analysis
            test_job = self.db.get_job_by_id('test_job_001')
            if test_job:
                analysis_response = self.gemini.analyze_job(test_job)
                assert analysis_response, "Job analysis failed"
                logger.info("✓ Job analysis successful")
            
            return True
            
        except Exception as e:
            logger.error(f"Gemini test failed: {e}")
            return False
    
    def test_job_analyzer(self):
        """Test job analysis functionality"""
        logger.info("Testing job analyzer...")
        
        test_job = self.db.get_job_by_id('test_job_001')
        if not test_job:
            logger.warning("No test job found for analysis")
            return False
        
        analysis = self.analyzer.analyze_job_match(test_job, Config.USER_PROFILE)
        
        assert analysis is not None, "Analysis returned None"
        assert 'match_score' in analysis, "Missing match_score in analysis"
        assert 'compatibility_level' in analysis, "Missing compatibility_level"
        assert 'key_skills_match' in analysis, "Missing key_skills_match"
        
        logger.info(f"✓ Job analysis completed - Match Score: {analysis.get('match_score', 'N/A')}")
        return analysis
    
    def test_resume_generator(self):
        """Test resume generation"""
        logger.info("Testing resume generator...")
        
        test_job = self.db.get_job_by_id('test_job_001')
        if not test_job:
            logger.warning("No test job found for resume generation")
            return False
        
        # Test with analysis
        analysis = self.analyzer.analyze_job_match(test_job, Config.USER_PROFILE)
        resume = self.resume_gen.customize_resume(Config.USER_PROFILE, test_job, analysis)
        
        assert resume is not None, "Resume generation returned None"
        assert 'professional_summary' in resume, "Missing professional_summary"
        assert 'highlighted_skills' in resume, "Missing highlighted_skills"
        
        logger.info("✓ Resume generation successful")
        
        # Test ATS optimization
        ats_resume = self.resume_gen.make_ats_friendly(resume, test_job.get('requirements', ''))
        assert ats_resume is not None, "ATS optimization failed"
        logger.info("✓ ATS optimization successful")
        
        return resume
    
    def test_pdf_generation(self):
        """Test PDF generation"""
        logger.info("Testing PDF generation...")
        
        try:
            from src.utils.pdf_generator import ProfessionalPDFGenerator
            import io
            
            # Generate test resume data
            test_resume = {
                'contact_info': {
                    'name': 'Test User',
                    'email': '<EMAIL>',
                    'phone': '+1234567890'
                },
                'professional_summary': 'Experienced designer with AI expertise',
                'highlighted_skills': ['UI/UX Design', 'Figma', 'AI Tools'],
                'experience': [{
                    'title': 'Designer',
                    'company': 'Test Co',
                    'duration': '2023-Present',
                    'achievements': ['Increased productivity by 60%']
                }]
            }
            
            buffer = io.BytesIO()
            pdf_gen = ProfessionalPDFGenerator(buffer)
            pdf_gen.create_resume_pdf(test_resume)
            
            assert buffer.tell() > 0, "PDF generation produced empty file"
            logger.info("✓ PDF generation successful")
            
            return True
            
        except Exception as e:
            logger.error(f"PDF generation test failed: {e}")
            return False
    
    def test_api_endpoints(self):
        """Test API endpoints (requires server to be running)"""
        logger.info("Testing API endpoints...")
        
        base_url = "http://localhost:8000"
        
        try:
            # Test health check
            response = requests.get(f"{base_url}/api/jobs", timeout=5)
            if response.status_code == 200:
                logger.info("✓ API server is responding")
                
                # Test job analysis endpoint
                test_job_id = 'test_job_001'
                analysis_response = requests.post(
                    f"{base_url}/api/jobs/{test_job_id}/analyze",
                    timeout=10
                )
                
                if analysis_response.status_code == 200:
                    logger.info("✓ Job analysis endpoint working")
                else:
                    logger.warning(f"Job analysis endpoint returned {analysis_response.status_code}")
                
                return True
            else:
                logger.warning(f"API server returned status {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            logger.warning(f"⚠️ API server not accessible: {e}")
            return {'status': 'skipped', 'reason': 'Server not running'}
    
    def test_e2e_workflow(self):
        """Test end-to-end workflow"""
        logger.info("Testing end-to-end workflow...")
        
        try:
            # 1. Get a test job
            test_job = self.db.get_job_by_id('test_job_001')
            assert test_job, "No test job available"
            
            # 2. Analyze the job
            analysis = self.analyzer.analyze_job_match(test_job, Config.USER_PROFILE)
            assert analysis, "Job analysis failed"
            
            # 3. Generate customized resume
            resume = self.resume_gen.customize_resume(Config.USER_PROFILE, test_job, analysis)
            assert resume, "Resume generation failed"
            
            # 4. Generate cover letter
            cover_letter = self.gemini.generate_cover_letter(test_job, Config.USER_PROFILE)
            # Note: cover_letter might be None if Gemini API is not available
            
            # 5. Update job status
            self.db.update_job_status('test_job_001', 'processed')
            
            logger.info("✓ End-to-end workflow completed successfully")
            
            return {
                'job_analyzed': True,
                'resume_generated': True,
                'cover_letter_generated': cover_letter is not None,
                'status_updated': True
            }
            
        except Exception as e:
            logger.error(f"E2E workflow failed: {e}")
            return False
    
    def print_test_summary(self):
        """Print comprehensive test summary"""
        logger.info("\n" + "="*60)
        logger.info("TEST SUMMARY")
        logger.info("="*60)
        
        passed = sum(1 for r in self.test_results.values() if r['status'] == 'PASSED')
        failed = sum(1 for r in self.test_results.values() if r['status'] == 'FAILED')
        errors = sum(1 for r in self.test_results.values() if r['status'] == 'ERROR')
        skipped = sum(1 for r in self.test_results.values() if r['status'] == 'skipped')
        
        logger.info(f"Total Tests: {len(self.test_results)}")
        logger.info(f"✅ Passed: {passed}")
        logger.info(f"❌ Failed: {failed}")
        logger.info(f"🔥 Errors: {errors}")
        logger.info(f"⏭️ Skipped: {skipped}")
        
        logger.info("\nDetailed Results:")
        for test_name, result in self.test_results.items():
            status_emoji = {
                'PASSED': '✅',
                'FAILED': '❌',
                'ERROR': '🔥',
                'skipped': '⏭️'
            }.get(result['status'], '❓')
            
            logger.info(f"{status_emoji} {test_name}: {result['status']}")
            if 'error' in result:
                logger.info(f"   Error: {result['error']}")

if __name__ == "__main__":
    tester = SystemTester()
    results = tester.run_all_tests()
    
    # Exit with appropriate code
    failed_tests = sum(1 for r in results.values() if r['status'] in ['FAILED', 'ERROR'])
    sys.exit(failed_tests)
