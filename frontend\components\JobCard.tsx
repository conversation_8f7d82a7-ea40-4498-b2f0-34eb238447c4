'use client'
import { useState, useEffect } from 'react'

export default function JobCard({ job }: { job: any }) {

  
  const [applying, setApplying] = useState(false)
  const [analysis, setAnalysis] = useState(null)
  
  const analyzeJob = async () => {
    const response = await fetch(`/api/jobs/${job.job_id}/analyze`)
    const data = await response.json()
    setAnalysis(data.analysis)
  }
  
  const autoApply = async () => {
    setApplying(true)
    try {
      const response = await fetch(`/api/jobs/${job.job_id}/apply`, {
        method: 'POST'
      })
      const result = await response.json()
      if (result.success) {
        toast.success('Application submitted successfully!')
      }
    } finally {
      setApplying(false)
    }
  }
  
  return (
    <div className="job-card">
      <div className="job-header">
        <h3>{job.title}</h3>
        <span className="company">{job.company}</span>
        {analysis && (
          <div className="match-score">
            Match: {analysis.match_score}%
          </div>
        )}
      </div>
      
      <div className="job-actions">
        <button onClick={analyzeJob}>AI Analyze</button>
        <button onClick={autoApply} disabled={applying}>
          {applying ? 'Applying...' : 'Auto Apply'}
        </button>
      </div>
      
      {analysis && (
        <div className="analysis-details">
          <div className="skills-match">
            <h4>Matching Skills:</h4>
            <ul>
              {analysis.key_skills_match.map(skill => (
                <li key={skill}>{skill}</li>
              ))}
            </ul>
          </div>
        </div>
      )}
    </div>
  )
}