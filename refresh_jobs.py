#!/usr/bin/env python3
"""
Script to refresh job database with real job postings.
This replaces fake Gemini-generated jobs with realistic job data.
"""

import sys
import os
import time
from datetime import datetime, timedelta
sys.path.append('src')

from src.utils.database import JobDatabase
from src.scrapers.real_job_scraper import JobSearchManager
from src.scrapers.live_job_scraper import LiveJobSearchManager
from src.utils.logger import setup_logger

def main():
    """Main function to refresh jobs with current dates"""
    logger = setup_logger('refresh_jobs')

    print("="*60)
    print("JOB AI - REFRESHING JOB DATABASE WITH CURRENT JOBS")
    print("="*60)
    print(f"Current Date: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)

    # Initialize components
    db = JobDatabase()
    search_manager = JobSearchManager()
    live_search_manager = LiveJobSearchManager()

    try:
        # Get current job count
        current_jobs = db.get_all_jobs()
        print(f"Current jobs in database: {len(current_jobs)}")

        # Show date range of existing jobs
        if current_jobs:
            dates = [job.get('posted_date', '') for job in current_jobs if job.get('posted_date')]
            if dates:
                dates.sort()
                print(f"Existing job date range: {dates[0]} to {dates[-1]}")

        # Ask user if they want to clear existing jobs
        if current_jobs:
            response = input("\nDo you want to clear existing jobs and add fresh ones with current dates? (y/N): ")
            if response.lower() == 'y':
                print("Clearing existing jobs...")
                try:
                    import sqlite3
                    with sqlite3.connect(db.db_path) as conn:
                        conn.execute("DELETE FROM jobs")
                        conn.commit()
                    print("✅ Existing jobs cleared")
                except Exception as e:
                    print(f"❌ Error clearing jobs: {e}")
                    return 1
        
        # Search for new jobs with real URLs
        print("\n🔍 Searching for live job opportunities with real URLs...")
        print("📡 Fetching from RemoteOK API and company career pages...")
        jobs = live_search_manager.search_all_jobs()
        
        if not jobs:
            print("❌ No jobs found")
            return 1
        
        print(f"✅ Found {len(jobs)} job opportunities")
        
        # Display job summary
        print("\n📋 Job Summary:")
        print("-" * 60)

        sources = {}
        companies = set()
        ai_jobs = 0
        recent_jobs = 0
        real_url_jobs = 0

        # Calculate current date for comparison
        today = datetime.now()

        for i, job in enumerate(jobs, 1):
            source = job.get('source', 'Unknown')
            sources[source] = sources.get(source, 0) + 1
            companies.add(job.get('company', 'Unknown'))

            # Check if AI-related
            title_lower = job.get('title', '').lower()
            desc_lower = job.get('description', '').lower()
            if 'ai' in title_lower or 'ai' in desc_lower or 'machine learning' in desc_lower:
                ai_jobs += 1

            # Check if recent (within 7 days)
            try:
                posted_date = datetime.strptime(job.get('posted_date', ''), '%Y-%m-%d')
                if (today - posted_date).days <= 7:
                    recent_jobs += 1
            except:
                pass

            # Check if URL is real
            apply_url = job.get('apply_url', '')
            real_domains = [
                'remoteok.io', 'indeed.com', 'linkedin.com', 'github.com',
                'gitlab.com', 'atlassian.com', 'shopify.com', 'vercel.com',
                'linear.app', 'supabase.com', 'framer.com', 'webflow.com',
                'dribbble.com', 'discord.com', 'twitch.tv', 'redditinc.com',
                'dropbox.com', 'slack.com'
            ]
            if any(domain in apply_url for domain in real_domains):
                real_url_jobs += 1

            print(f"{i:2d}. {job.get('title', 'N/A')}")
            print(f"    Company: {job.get('company', 'N/A')}")
            print(f"    Source: {source} | Location: {job.get('location', 'N/A')}")
            print(f"    Posted: {job.get('posted_date', 'N/A')}")
            print(f"    URL: {job.get('apply_url', 'N/A')[:60]}...")
            print()

        print("-" * 60)
        print(f"📊 Statistics:")
        print(f"   Total Jobs: {len(jobs)}")
        print(f"   Sources: {dict(sources)}")
        print(f"   Companies: {len(companies)} unique companies")
        print(f"   AI-Related Jobs: {ai_jobs}")
        print(f"   Recent Jobs (≤7 days): {recent_jobs}")
        print(f"   ✅ Real URL Jobs: {real_url_jobs}")
        print(f"   ❌ Fake URL Jobs: {len(jobs) - real_url_jobs}")

        # Show date range
        dates = [job.get('posted_date', '') for job in jobs if job.get('posted_date')]
        if dates:
            dates.sort()
            print(f"   Date Range: {dates[0]} to {dates[-1]}")

        print("-" * 60)
        
        # Verify jobs were saved
        updated_jobs = db.get_all_jobs()
        print(f"\n✅ Database now contains {len(updated_jobs)} jobs")
        
        print("\n" + "="*60)
        print("JOB REFRESH COMPLETED SUCCESSFULLY!")
        print("="*60)
        print("Next steps:")
        print("1. Start the backend server: python api_server.py")
        print("2. Start the frontend: cd frontend && npm run dev")
        print("3. Visit http://localhost:3000 to see the new jobs")
        print("4. Try the AI analysis and auto-apply features!")
        
        return 0
        
    except Exception as e:
        logger.error(f"Error refreshing jobs: {e}")
        print(f"❌ Error: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
