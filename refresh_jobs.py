#!/usr/bin/env python3
"""
Script to refresh job database with real job postings.
This replaces fake Gemini-generated jobs with realistic job data.
"""

import sys
import os
sys.path.append('src')

from src.utils.database import JobDatabase
from src.scrapers.real_job_scraper import JobSearchManager
from src.utils.logger import setup_logger

def main():
    """Main function to refresh jobs"""
    logger = setup_logger('refresh_jobs')
    
    print("="*60)
    print("JOB AI - REFRESHING JOB DATABASE")
    print("="*60)
    
    # Initialize components
    db = JobDatabase()
    search_manager = JobSearchManager()
    
    try:
        # Get current job count
        current_jobs = db.get_all_jobs()
        print(f"Current jobs in database: {len(current_jobs)}")
        
        # Ask user if they want to clear existing jobs
        if current_jobs:
            response = input("\nDo you want to clear existing jobs and add fresh ones? (y/N): ")
            if response.lower() == 'y':
                print("Clearing existing jobs...")
                # Clear jobs (you might need to implement this method)
                try:
                    import sqlite3
                    with sqlite3.connect(db.db_path) as conn:
                        conn.execute("DELETE FROM jobs")
                        conn.commit()
                    print("✅ Existing jobs cleared")
                except Exception as e:
                    print(f"❌ Error clearing jobs: {e}")
                    return 1
        
        # Search for new jobs
        print("\n🔍 Searching for real job opportunities...")
        jobs = search_manager.search_all_jobs()
        
        if not jobs:
            print("❌ No jobs found")
            return 1
        
        print(f"✅ Found {len(jobs)} job opportunities")
        
        # Display job summary
        print("\n📋 Job Summary:")
        print("-" * 40)
        
        sources = {}
        companies = set()
        
        for job in jobs:
            source = job.get('source', 'Unknown')
            sources[source] = sources.get(source, 0) + 1
            companies.add(job.get('company', 'Unknown'))
            
            print(f"• {job.get('title', 'N/A')} at {job.get('company', 'N/A')}")
            print(f"  Source: {source} | Location: {job.get('location', 'N/A')}")
            print()
        
        print(f"Sources: {dict(sources)}")
        print(f"Companies: {len(companies)} unique companies")
        
        # Verify jobs were saved
        updated_jobs = db.get_all_jobs()
        print(f"\n✅ Database now contains {len(updated_jobs)} jobs")
        
        print("\n" + "="*60)
        print("JOB REFRESH COMPLETED SUCCESSFULLY!")
        print("="*60)
        print("Next steps:")
        print("1. Start the backend server: python api_server.py")
        print("2. Start the frontend: cd frontend && npm run dev")
        print("3. Visit http://localhost:3000 to see the new jobs")
        print("4. Try the AI analysis and auto-apply features!")
        
        return 0
        
    except Exception as e:
        logger.error(f"Error refreshing jobs: {e}")
        print(f"❌ Error: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
