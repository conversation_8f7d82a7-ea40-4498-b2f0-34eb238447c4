#!/usr/bin/env python3
"""
AI-Powered Resume and Cover Letter Generator
Uses Gemini AI to create personalized documents based on job postings
"""

from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
import sys
import os
sys.path.append('src')

from src.utils.config import Config
from src.utils.gemini_client import GeminiClient
import json
import re
from datetime import datetime
import logging
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_JUSTIFY
import io
import tempfile

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

class AIDocumentGenerator:
    def __init__(self):
        self.gemini_client = GeminiClient()
        self.user_profile = Config.USER_PROFILE
        
    def extract_job_info(self, job_text):
        """Extract job title, company, and description from job posting text"""
        try:
            # Use Gemini to extract structured information
            prompt = f"""
            Extract the following information from this job posting text:
            
            Job Text:
            {job_text}
            
            Please extract and return in JSON format:
            {{
                "job_title": "extracted job title",
                "company_name": "extracted company name",
                "job_description": "cleaned job description",
                "key_requirements": ["requirement1", "requirement2", ...],
                "key_skills": ["skill1", "skill2", ...],
                "location": "job location if mentioned",
                "job_type": "full-time/part-time/internship/contract"
            }}
            
            Return only the JSON, no additional text.
            """
            
            response = self.gemini_client.generate_content(prompt)
            
            # Clean the response to extract JSON
            json_text = response.strip()
            if json_text.startswith('```json'):
                json_text = json_text[7:-3]
            elif json_text.startswith('```'):
                json_text = json_text[3:-3]
            
            return json.loads(json_text)
            
        except Exception as e:
            logger.error(f"Error extracting job info: {e}")
            # Fallback extraction
            lines = job_text.split('\n')
            return {
                "job_title": lines[0] if lines else "Position",
                "company_name": "Company",
                "job_description": job_text,
                "key_requirements": [],
                "key_skills": [],
                "location": "Not specified",
                "job_type": "Not specified"
            }
    
    def generate_cover_letter(self, job_info):
        """Generate personalized cover letter using Gemini AI"""
        try:
            user_skills_str = ", ".join(self.user_profile["skills"][:15])
            experience_summary = "\n".join([
                f"- {exp['title']} at {exp['company']} ({exp['duration']})"
                for exp in self.user_profile["experience"]
            ])
            
            prompt = f"""
            Create a professional, personalized cover letter for this job application:
            
            Job Information:
            - Position: {job_info['job_title']}
            - Company: {job_info['company_name']}
            - Location: {job_info.get('location', 'Not specified')}
            - Job Type: {job_info.get('job_type', 'Not specified')}
            
            Job Requirements:
            {job_info['job_description'][:1000]}
            
            Candidate Profile:
            - Name: {self.user_profile['name']}
            - Email: {self.user_profile['email']}
            - Phone: {self.user_profile['phone']}
            - Location: {self.user_profile['location']}
            - Summary: {self.user_profile['summary']}
            - Key Skills: {user_skills_str}
            - Experience: {experience_summary}
            - Portfolio: {self.user_profile['portfolio_url']}
            
            Requirements:
            1. Write a compelling, professional cover letter
            2. Highlight relevant skills and experience that match the job requirements
            3. Show enthusiasm for the role and company
            4. Keep it concise (3-4 paragraphs)
            5. Use a professional tone
            6. Include specific examples from the candidate's experience
            7. Emphasize AI and automation skills if relevant to the role
            
            Format the cover letter properly with:
            - Date
            - Hiring Manager address
            - Professional greeting
            - Body paragraphs
            - Professional closing
            - Signature
            """
            
            cover_letter = self.gemini_client.generate_content(prompt)
            return cover_letter.strip()
            
        except Exception as e:
            logger.error(f"Error generating cover letter: {e}")
            return f"Error generating cover letter: {str(e)}"
    
    def generate_ats_resume(self, job_info):
        """Generate ATS-friendly resume tailored to the job using Gemini AI"""
        try:
            prompt = f"""
            Create an ATS-friendly resume tailored for this specific job posting:
            
            Job Information:
            - Position: {job_info['job_title']}
            - Company: {job_info['company_name']}
            - Key Requirements: {', '.join(job_info.get('key_requirements', []))}
            - Key Skills: {', '.join(job_info.get('key_skills', []))}
            
            Job Description:
            {job_info['job_description'][:1500]}
            
            Candidate Profile:
            {json.dumps(self.user_profile, indent=2)}
            
            Requirements:
            1. Create an ATS-optimized resume that matches the job requirements
            2. Prioritize skills and experience most relevant to this specific role
            3. Use keywords from the job posting naturally
            4. Structure: Contact Info, Professional Summary, Skills, Experience, Education, Projects
            5. Make it scannable by ATS systems
            6. Highlight AI/automation expertise if relevant to the role
            7. Quantify achievements where possible
            8. Keep descriptions concise but impactful
            
            Return the resume in a structured format with clear sections.
            """
            
            resume_content = self.gemini_client.generate_content(prompt)
            return resume_content.strip()
            
        except Exception as e:
            logger.error(f"Error generating resume: {e}")
            return f"Error generating resume: {str(e)}"
    
    def create_pdf(self, content, doc_type="resume", job_title="", company_name=""):
        """Create PDF from content"""
        try:
            # Create a temporary file
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
            
            # Create PDF document
            doc = SimpleDocTemplate(temp_file.name, pagesize=A4,
                                  rightMargin=72, leftMargin=72,
                                  topMargin=72, bottomMargin=18)
            
            # Get styles
            styles = getSampleStyleSheet()
            
            # Create custom styles
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=16,
                spaceAfter=30,
                alignment=TA_CENTER
            )
            
            normal_style = ParagraphStyle(
                'CustomNormal',
                parent=styles['Normal'],
                fontSize=10,
                spaceAfter=12,
                alignment=TA_JUSTIFY
            )
            
            heading_style = ParagraphStyle(
                'CustomHeading',
                parent=styles['Heading2'],
                fontSize=12,
                spaceAfter=12,
                spaceBefore=12,
                textColor=colors.darkblue
            )
            
            # Build PDF content
            story = []
            
            # Add title
            if doc_type == "cover_letter":
                title = f"Cover Letter - {job_title} at {company_name}"
            else:
                title = f"Resume - {self.user_profile['name']}"
            
            story.append(Paragraph(title, title_style))
            story.append(Spacer(1, 12))
            
            # Process content
            lines = content.split('\n')
            current_section = ""
            
            for line in lines:
                line = line.strip()
                if not line:
                    story.append(Spacer(1, 6))
                    continue
                
                # Check if it's a heading (all caps or starts with specific patterns)
                if (line.isupper() and len(line) > 3) or \
                   any(line.startswith(prefix) for prefix in ['CONTACT', 'SUMMARY', 'SKILLS', 'EXPERIENCE', 'EDUCATION', 'PROJECTS']):
                    story.append(Paragraph(line, heading_style))
                    current_section = line
                else:
                    # Regular content
                    story.append(Paragraph(line, normal_style))
            
            # Build PDF
            doc.build(story)
            
            return temp_file.name
            
        except Exception as e:
            logger.error(f"Error creating PDF: {e}")
            return None

# Initialize the generator
doc_generator = AIDocumentGenerator()

# API Routes
@app.route('/')
def home():
    """Home page"""
    return jsonify({
        "message": "AI-Powered Resume and Cover Letter Generator",
        "endpoints": {
            "generate_documents": "/api/generate",
            "get_profile": "/api/profile"
        }
    })

@app.route('/api/profile', methods=['GET'])
def get_profile():
    """Get user profile from config"""
    return jsonify({
        "success": True,
        "profile": Config.USER_PROFILE
    })

@app.route('/api/generate', methods=['POST'])
def generate_documents():
    """Generate cover letter and resume from job posting"""
    try:
        data = request.get_json()
        
        if not data or 'job_text' not in data:
            return jsonify({
                "success": False,
                "error": "job_text is required"
            }), 400
        
        job_text = data['job_text'].strip()
        if not job_text:
            return jsonify({
                "success": False,
                "error": "job_text cannot be empty"
            }), 400
        
        # Extract job information
        logger.info("Extracting job information...")
        job_info = doc_generator.extract_job_info(job_text)
        
        # Generate documents
        logger.info("Generating cover letter...")
        cover_letter = doc_generator.generate_cover_letter(job_info)
        
        logger.info("Generating ATS resume...")
        resume = doc_generator.generate_ats_resume(job_info)
        
        return jsonify({
            "success": True,
            "job_info": job_info,
            "cover_letter": cover_letter,
            "resume": resume,
            "generated_at": datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error in generate_documents: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/generate-pdf', methods=['POST'])
def generate_pdf():
    """Generate PDF for cover letter or resume"""
    try:
        data = request.get_json()
        
        if not data or 'content' not in data or 'type' not in data:
            return jsonify({
                "success": False,
                "error": "content and type are required"
            }), 400
        
        content = data['content']
        doc_type = data['type']  # 'cover_letter' or 'resume'
        job_title = data.get('job_title', '')
        company_name = data.get('company_name', '')
        
        # Create PDF
        pdf_path = doc_generator.create_pdf(content, doc_type, job_title, company_name)
        
        if pdf_path:
            return send_file(
                pdf_path,
                as_attachment=True,
                download_name=f"{doc_type}_{company_name}_{job_title}.pdf".replace(" ", "_"),
                mimetype='application/pdf'
            )
        else:
            return jsonify({
                "success": False,
                "error": "Failed to generate PDF"
            }), 500
            
    except Exception as e:
        logger.error(f"Error generating PDF: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

if __name__ == '__main__':
    print("🤖 Starting AI-Powered Resume and Cover Letter Generator")
    print("📝 Features:")
    print("   - Gemini AI-powered document generation")
    print("   - ATS-friendly resume optimization")
    print("   - Personalized cover letters")
    print("   - PDF generation")
    print("   - Job posting analysis")
    print()
    print("🔗 Endpoints:")
    print("   - POST /api/generate (generate documents from job text)")
    print("   - POST /api/generate-pdf (create PDF)")
    print("   - GET  /api/profile (get user profile)")
    print()
    
    app.run(debug=True, host='0.0.0.0', port=5000)
