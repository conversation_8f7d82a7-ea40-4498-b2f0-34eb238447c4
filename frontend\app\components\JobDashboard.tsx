'use client'
import { useState, useEffect } from 'react'
import JobC<PERSON> from './JobCard'
import SearchForm from './SearchForm'
import StatsCard from './StatsCard'
import ResumeBuilder from './ResumeBuilder'
import { SparklesIcon, DocumentTextIcon, ChartBarIcon } from '@heroicons/react/24/outline'

interface Job {
  job_id: string
  title: string
  company: string
  description: string
  location: string
  source: string
  posted_date: string
  apply_url: string
  status: string
  match_score: number
}

interface Stats {
  total: number
  new: number
  applied: number
  analyzed: number
}

export default function JobDashboard() {
  const [jobs, setJobs] = useState<Job[]>([])
  const [stats, setStats] = useState<Stats>({ total: 0, new: 0, applied: 0, analyzed: 0 })
  const [loading, setLoading] = useState(true)
  const [searching, setSearching] = useState(false)
  const [selectedJobId, setSelectedJobId] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'jobs' | 'resume' | 'analytics'>('jobs')
  const [error, setError] = useState<string | null>(null)

  // Fetch jobs and stats on component mount
  useEffect(() => {
    fetchJobs()
    fetchStats()
  }, [])

  const fetchJobs = async () => {
    try {
      const response = await fetch('/api/jobs')
      const data = await response.json()
      
      if (data.success) {
        setJobs(data.jobs)
      } else {
        setError('Failed to fetch jobs')
      }
    } catch (error) {
      console.error('Error fetching jobs:', error)
      setError('Network error while fetching jobs')
    } finally {
      setLoading(false)
    }
  }

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/stats')
      const data = await response.json()
      
      if (data.success) {
        setStats(data.stats)
      }
    } catch (error) {
      console.error('Error fetching stats:', error)
    }
  }

  const startJobSearch = async () => {
    setSearching(true)
    setError(null)
    
    try {
      const response = await fetch('/api/search', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })
      
      const data = await response.json()
      
      if (data.success) {
        // Poll for search completion
        pollSearchStatus()
      } else {
        setError(data.message || 'Failed to start job search')
        setSearching(false)
      }
    } catch (error) {
      console.error('Error starting search:', error)
      setError('Network error while starting search')
      setSearching(false)
    }
  }

  const pollSearchStatus = async () => {
    const pollInterval = setInterval(async () => {
      try {
        const response = await fetch('/api/search/status')
        const data = await response.json()
        
        if (data.success && !data.status.is_searching) {
          clearInterval(pollInterval)
          setSearching(false)
          // Refresh jobs and stats
          fetchJobs()
          fetchStats()
        }
      } catch (error) {
        console.error('Error polling search status:', error)
        clearInterval(pollInterval)
        setSearching(false)
      }
    }, 2000) // Poll every 2 seconds
  }

  const handleJobUpdate = (jobId: string, updates: any) => {
    setJobs(prevJobs => 
      prevJobs.map(job => 
        job.job_id === jobId ? { ...job, ...updates } : job
      )
    )
    // Refresh stats after job update
    fetchStats()
  }

  const analyzeAllJobs = async () => {
    const newJobs = jobs.filter(job => job.status === 'new')

    if (newJobs.length === 0) {
      alert('No new jobs to analyze')
      return
    }

    setError(null)

    try {
      const response = await fetch('/api/jobs/analyze-batch', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jobIds: newJobs.map(job => job.job_id)
        })
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('Batch analysis error:', errorText)
        setError(`Analysis failed: ${response.status} ${response.statusText}`)
        return
      }

      const data = await response.json()

      if (data.success) {
        // Update jobs with analysis results
        const updatedJobs = jobs.map(job => {
          const result = data.results.find((r: any) => r.job_id === job.job_id)
          return result ? { ...job, match_score: result.match_score, status: 'analyzed' } : job
        })
        setJobs(updatedJobs)
        fetchStats()
        alert(`Analyzed ${data.results.length} jobs successfully!\n\nHigh Priority: ${data.summary.high_priority_count}\nAI Relevant: ${data.summary.ai_relevant_count}`)
      } else {
        setError(data.error || 'Failed to analyze jobs')
      }
    } catch (error) {
      console.error('Error analyzing jobs:', error)
      setError(`Network error while analyzing jobs: ${error.message}`)
    }
  }

  const getHighPriorityJobs = () => {
    return jobs.filter(job => job.match_score >= 70).sort((a, b) => b.match_score - a.match_score)
  }

  const getAIRelevantJobs = () => {
    return jobs.filter(job => 
      job.description.toLowerCase().includes('ai') || 
      job.description.toLowerCase().includes('artificial intelligence') ||
      job.description.toLowerCase().includes('automation') ||
      job.title.toLowerCase().includes('ai')
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Job AI Dashboard
          </h1>
          <p className="text-gray-600">
            AI-powered job search and application automation for Tishbian Meshach S
          </p>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-800">{error}</p>
            <button 
              onClick={() => setError(null)}
              className="text-red-600 hover:text-red-800 text-sm mt-2"
            >
              Dismiss
            </button>
          </div>
        )}

        {/* Navigation Tabs */}
        <div className="mb-6">
          <nav className="flex space-x-8">
            <button
              onClick={() => setActiveTab('jobs')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'jobs'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <SparklesIcon className="h-5 w-5 inline mr-2" />
              Jobs & AI Analysis
            </button>
            <button
              onClick={() => setActiveTab('resume')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'resume'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <DocumentTextIcon className="h-5 w-5 inline mr-2" />
              Resume Builder
            </button>
            <button
              onClick={() => setActiveTab('analytics')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'analytics'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <ChartBarIcon className="h-5 w-5 inline mr-2" />
              Analytics
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'jobs' && (
          <>
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <StatsCard title="Total Jobs" value={stats.total} color="blue" />
              <StatsCard title="New Jobs" value={stats.new} color="green" />
              <StatsCard title="Analyzed" value={stats.analyzed} color="purple" />
              <StatsCard title="Applied" value={stats.applied} color="blue" />
            </div>

            {/* Search Form */}
            <div className="mb-8">
              <SearchForm onSearch={startJobSearch} searching={searching} />
            </div>

            {/* Bulk Actions */}
            <div className="mb-6 flex space-x-4">
              <button
                onClick={analyzeAllJobs}
                className="btn-secondary flex items-center"
                disabled={jobs.filter(job => job.status === 'new').length === 0}
              >
                <SparklesIcon className="h-4 w-4 mr-2" />
                Analyze All New Jobs
              </button>
            </div>

            {/* Job Filters */}
            <div className="mb-6">
              <div className="flex space-x-4 text-sm">
                <span className="text-gray-600">Quick Filters:</span>
                <button 
                  onClick={() => setJobs(jobs.sort((a, b) => b.match_score - a.match_score))}
                  className="text-blue-600 hover:text-blue-800"
                >
                  High Match Score
                </button>
                <button 
                  onClick={() => setJobs(getAIRelevantJobs())}
                  className="text-purple-600 hover:text-purple-800"
                >
                  AI-Related Jobs ({getAIRelevantJobs().length})
                </button>
                <button 
                  onClick={fetchJobs}
                  className="text-gray-600 hover:text-gray-800"
                >
                  Show All
                </button>
              </div>
            </div>

            {/* Jobs Grid */}
            <div className="grid gap-6">
              {jobs.length > 0 ? (
                jobs.map((job) => (
                  <JobCard 
                    key={job.job_id} 
                    job={job} 
                    onJobUpdate={handleJobUpdate}
                  />
                ))
              ) : (
                <div className="text-center py-12">
                  <SparklesIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Jobs Found</h3>
                  <p className="text-gray-600">Start a new search to find job opportunities</p>
                </div>
              )}
            </div>
          </>
        )}

        {activeTab === 'resume' && (
          <ResumeBuilder jobId={selectedJobId || undefined} />
        )}

        {activeTab === 'analytics' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-bold mb-4">Job Analytics</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold mb-2">High Priority Jobs</h3>
                <div className="space-y-2">
                  {getHighPriorityJobs().slice(0, 5).map(job => (
                    <div key={job.job_id} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                      <span className="text-sm">{job.title} at {job.company}</span>
                      <span className="text-sm font-medium text-green-600">{job.match_score}%</span>
                    </div>
                  ))}
                </div>
              </div>
              
              <div>
                <h3 className="font-semibold mb-2">AI-Related Opportunities</h3>
                <div className="space-y-2">
                  {getAIRelevantJobs().slice(0, 5).map(job => (
                    <div key={job.job_id} className="flex justify-between items-center p-2 bg-purple-50 rounded">
                      <span className="text-sm">{job.title} at {job.company}</span>
                      <span className="text-xs text-purple-600">AI Role</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
